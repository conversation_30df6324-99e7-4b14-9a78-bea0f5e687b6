var broadcast = async function () {
  const elements = document.querySelectorAll('.MuiGrid-item.MuiGrid-grid-xs-6');
  const synth = window.speechSynthesis;

  // Utility function to wait until the speech is finished
  function speakAndWait(utterance) {
    return new Promise((resolve, reject) => {
      utterance.onend = resolve;
      utterance.onerror = reject;
      synth.speak(utterance);
    });
  }

  for (let element of elements) {
    if (element.textContent.includes('READY')) {
      const match = element.textContent.match(/\d{4}/);
      if (match) {
        let orderNumber = match[0];
        
        // Break the order number into individual digits
        let digits = orderNumber.split('').join(' ');

        // English announcement
        let utterThisEn = new SpeechSynthesisUtterance(`Order Number ended with ${digits} is ready`);
        utterThisEn.lang = 'en-US';
        await speakAndWait(utterThisEn);

        // Mandarin Chinese announcement
        let utterThisZh = new SpeechSynthesisUtterance(`尾号 ${digits} 的订单已经就绪`);
        utterThisZh.lang = 'zh-CN';
        await speakAndWait(utterThisZh);
      }
    }
  }
}
broadcast();