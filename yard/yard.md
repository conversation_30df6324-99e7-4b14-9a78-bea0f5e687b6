# DCFX Site
We have a big distribution center site, named DCFX, the site has several areas:
1. building 1, building 2, they hold goods, and both have several docks for goods to flow out
2. weighting area, check the weight of trucks before and after trucks comes in/out the site
3. air quality check, for overseas inbound, we have to check the air quality before any other processes
4. gatehouses, for truck drivers to wait for process to handle
5. 
draw sample use case diagram, for yard management; including role: 
1. planner, planning the inbound and outbound flow; for any deviation, they will handle; 
2. security check, check the trucks for coming in and going out, check the truck following a checklist
3. truck driver, drives the trucks and flows the planning; 
4. forklift driver, transport the goods to gate

```plantuml

@startuml
left to right direction
skinparam packageStyle rectangle

actor "Transport Planner" as TP
actor "Carrier Company" as CC
actor "Security Guard" as SG
actor "Truck Driver" as TD
actor "Forklift Driver" as FD
actor "Flow Steering" as FS

rectangle "Yard Management System" {
    usecase "Plan Inbound/Outbound Flow" as UC1
    usecase "Submit Task Assignments" as UC2
    usecase "Check Trucks" as UC3
    usecase "Drive Trucks" as UC4
    usecase "Load/Unload Goods" as UC5
    usecase "Steer Truck Drivers" as UC6
}

TP --> UC1
CC --> UC2
SG --> UC3
TD --> UC4
FD --> UC5
FS --> UC6

UC1 ..> UC6 : "Provide plans"
UC2 ..> UC3 : "Submit information"
UC3 ..> UC4 : "Admit to yard"
UC4 ..> UC5 : "Receive/Deliver goods"
UC6 ..> UC4 : "Notify drivers"

@enduml
```
