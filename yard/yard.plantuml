@startuml
participant "[DSM] Freight Management" order 5
participant "[DSM] Work Order Scheduling & Allocation" order 5
participant "[DSM] Pickup Point Management" order 6
participant "[DSM] Handout Management" order 7
participant "[DSM] Capacity Management (ATO)" order 5
participant "AA: Fulfillment Locations" order 4
participant "AA: Order Management" order 3
participant "AA: Order Promise" order 2
participant "AA: Sales" order 1
== Order Promising ==
activate "AA: Sales"
"AA: Sales" -> "AA: Order Promise" ++ : "SA00: Timewindow Request"
deactivate "AA: Sales"
"AA: Order Promise" -> "[DSM] Capacity Management (ATO)" ++ : "SA01: Timewindow Request"
deactivate "AA: Order Promise"
"[DSM] Capacity Management (ATO)" -> "[DSM] Pickup Point Management" ++ : "SA02: Request Pickup Points for Order"
deactivate "[DSM] Capacity Management (ATO)"
"[DSM] Pickup Point Management" -> "[DSM] Capacity Management (ATO)" ++ : "SA03: Eligable Pickup point Slots for Order"
deactivate "[DSM] Pickup Point Management"
"[DSM] Capacity Management (ATO)" -> "AA: Order Promise" ++ : "SA04: Requested timewindow"
deactivate "[DSM] Capacity Management (ATO)"
"AA: Order Promise" -> "AA: Sales" ++ : "SA05: Requested timewindow"
deactivate "AA: Order Promise"

== Order Placement & Planning == 
"AA: Sales" -> "AA: Order Management" ++ : "SB01: Fulfilment Order"
deactivate "AA: Sales"
"AA: Order Management" -> "[DSM] Work Order Scheduling & Allocation" ++ : "SB02: Work Order Creation / Updates"
"AA: Order Management" -> "AA: Fulfillment Locations" ++ : "SC01: Picking Order"
deactivate "AA: Order Management"
"[DSM] Work Order Scheduling & Allocation" -> "[DSM] Handout Management" ++ : "SB03: Handout Work Order"
deactivate "[DSM] Work Order Scheduling & Allocation"
"[DSM] Handout Management" -> "[DSM] Capacity Management (ATO)" ++ : "SB04: Handout capacity booking"
"[DSM] Handout Management" -> "[DSM] Work Order Scheduling & Allocation" ++ : "SB05: Handout status updates"
deactivate "[DSM] Handout Management"
deactivate "[DSM] Capacity Management (ATO)"
"[DSM] Work Order Scheduling & Allocation" -> "AA: Order Management" ++ : "SB06: Work order Notifications"
deactivate "[DSM] Work Order Scheduling & Allocation"
deactivate "AA: Order Management"
== Execute Picking ==
"AA: Fulfillment Locations" -> "[DSM] Freight Management" ++ : "SC02: Cmd: Create CDU + Label"
deactivate "AA: Fulfillment Locations"
"[DSM] Freight Management" -> "AA: Fulfillment Locations" ++ : "SC03: Created CDU and Label"
deactivate "[DSM] Freight Management"
== Handout order ==
"AA: Fulfillment Locations" -> "[DSM] Handout Management" ++ : "SD01: Handed Out to Customer"
deactivate "AA: Fulfillment Locations"
"[DSM] Handout Management" -> "[DSM] Capacity Management (ATO)" ++ : "SD02: Handout Capacity Consumption"
"[DSM] Handout Management" -> "[DSM] Work Order Scheduling & Allocation" ++ : "SD03: Handout status updates"
deactivate "[DSM] Handout Management"
deactivate "[DSM] Capacity Management (ATO)"
deactivate "[DSM] Work Order Scheduling & Allocation"
@enduml