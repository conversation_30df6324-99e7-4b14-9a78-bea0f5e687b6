# 手动商品总额
GoodsExclSaving=49768.56 = 49,768.56
GoodsInclSaving=49334.95 = 49,334.95
# 订单显示商品总额
GoodsValue = 48,669.56 = 48,669.56

# Service
ProvidedService = 3415.00 = 3,415
DeliveryService = 660 = 660
TotalService = ProvidedService + DeliveryService = 4,075

# FY25D11PC10decoCSC 
DiscountCoupon=334.60  = 334.6
DiscountPromotion=99.01  = 99.01
TotalDiscount = DiscountCoupon + DiscountPromotion = 433.61

# OrderTotal
OrderTotal = GoodsValue + TotalService = 52,744.56
ToPay = OrderTotal - TotalDiscount = 52,310.95

# Total payment gap
TotalInTheory = GoodsInclSaving + TotalService  = 53,409.95
TotalPaymentGap = TotalInTheory - ToPay = 1,099
RemaingingGap = TotalPaymentGap - TotalDiscount = 665.39
