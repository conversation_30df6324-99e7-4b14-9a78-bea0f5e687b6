{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 2352, "links": [], "panels": [{"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 10, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "direction": "backward", "editorMode": "code", "expr": "sum by (http_status) (\n  count_over_time(\n    {k8s_cluster_name=\"mpp-prod\", service_name=\"order-history\"}\n      |=\"https://private-api.ingka.prodcn.ikea.com/order-management-isom/order-cancellation/order-cancel\"\n      | json\n      | http_status!=200\n      | http_body_title!=`Order Already Cancelled`\n    [$__range]\n  )\n)", "key": "Q-726e98a6-1511-46fe-b2a5-edf167f5547a-0", "queryType": "instant", "refId": "A"}], "title": "Tmall Order cancellation failure", "type": "table"}, {"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 5, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "direction": "backward", "editorMode": "code", "expr": "sort_desc(\n    sum by (uri, uri_param) ( count_over_time( {service_name=\"cdn\"} |= \"res.app.ikea.cn\" |= \"odi-cps-pickup\" | json | __error__=\"\" | uri =~ \".*html.*\" [$__range] ) )\n)\n", "queryType": "instant", "refId": "A"}], "title": "odi-cps-pickup", "type": "table"}, {"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 15}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 9, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "direction": "backward", "editorMode": "code", "expr": "sort_desc(sum by (service_name, path) (\n    count_over_time(\n        {context_tenant=\"dtc-tenant\",deployment_environment=\"prod\",ingka_system_name=\"cn-digital-hub\",ingka_team=\"cn-odi\"} \n        | level=`error` \n        | json \n        |= \"exception\" \n        | __error__=\"\" \n        [$__range]\n    )\n))\n", "key": "Q-2707a23d-a700-4f99-a1d4-0d05878657e7-0", "queryType": "instant", "refId": "A"}], "title": "Error log", "type": "table"}, {"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 17, "w": 12, "x": 12, "y": 8}, "id": 3, "options": {"dedupStrategy": "none", "enableInfiniteScrolling": true, "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "direction": "backward", "editorMode": "code", "expr": "{context_tenant=\"dtc-tenant\",deployment_environment=\"prod\",ingka_system_name=\"cn-digital-hub\",ingka_team=\"cn-odi\",k8s_cluster_name=\"mpp-prod\"}\n |=\"https://private-api.ingka.prodcn.ikea.com/\" |~\"^5|^4\"", "legendFormat": "", "queryType": "range", "refId": "A"}], "title": "https://private-api.ingka.prodcn.ikea.com/ API calls", "type": "logs"}, {"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.width", "value": 159}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 16}, "id": 8, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "direction": "backward", "editorMode": "builder", "expr": "{deployment_environment=\"prod\", k8s_container_name=\"master-data\"} | level=`info` |= `call ali bailian completion api response` |= `BAD`", "key": "Q-ed762cce-a843-4a69-8885-c3a904e878c9-0", "queryType": "range", "refId": "A"}], "title": "Address validation", "transformations": [{"id": "extractFields", "options": {"source": "labels"}}, {"id": "organize", "options": {"includeByName": {"Line": true, "Time": true}, "indexByName": {"Line": 1, "Time": 0}}}], "type": "table"}, {"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Line"}, "properties": []}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "id": 7, "options": {"dedupStrategy": "none", "enableInfiniteScrolling": false, "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "direction": "backward", "editorMode": "code", "expr": "{context_tenant=\"dtc-tenant\",deployment_environment=\"prod\",k8s_container_name=~\"checkoutservice|shoppingbff-service\"}  |= `SpeException` | json | status=500", "queryType": "range", "refId": "A"}], "title": "SpeException", "type": "logs"}, {"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 25}, "id": 6, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "direction": "backward", "editorMode": "code", "expr": "topk(100, \n    sort_desc(\n    sum by(request_uri,status) (\n        count_over_time(\n            {context_tenant=\"dtc-tenant\", k8s_namespace_name=\"kong\"} \n            | json \n            | request_uri =~ `.*order.*` \n            | status != \"200\" \n            | status != \"201\" \n            | status != \"301\" \n            | status != \"400\"\n            | status != \"401\" \n            | status != \"404\" \n            [$__range]\n        )\n    ) > 5\n    )\n)\n\n", "queryType": "instant", "refId": "A"}], "title": "order API non 200 400 401 404", "type": "table"}, {"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "uri"}, "properties": [{"id": "custom.width", "value": 471}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 33}, "id": 4, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "direction": "backward", "editorMode": "code", "expr": "sort_desc(sum by (uri, uri_param) ( count_over_time( {service_name=\"cdn\"} |= \"res.app.ikea.cn\" |= \"orders-data-dashboard\" | json | __error__=\"\" | uri =~ \".*html.*\" [1h] ) ))\n", "key": "Q-cb0e5b02-a003-420a-a12a-aca2287bc365-0", "queryType": "instant", "refId": "A"}], "title": "order dashboard usage", "type": "table"}, {"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisPlacement": "auto", "fillOpacity": 70, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineWidth": 0, "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 10}, {"color": "dark-red", "value": 20}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": []}]}, "gridPos": {"h": 18, "w": 24, "x": 0, "y": 42}, "id": 2, "options": {"alignValue": "left", "legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "mergeValues": true, "rowHeight": 0.9, "showValue": "auto", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "direction": "backward", "editorMode": "code", "expr": "sort_desc(\n  sum by (message, http_status) (\n    count_over_time(\n      (\n        {context_tenant=\"dtc-tenant\",deployment_environment=\"prod\",ingka_system_name=\"cn-digital-hub\",k8s_cluster_name=\"mpp-prod\"}\n        | json \n        | http_status!=200\n        | http_status!=201\n        | http_status!=204\n      ) [$__range]\n    )\n  )> 5\n)", "legendFormat": "", "queryType": "instant", "refId": "A"}], "title": "mpp-prod non 200/201/204", "type": "state-timeline"}, {"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"axisPlacement": "auto", "fillOpacity": 70, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineWidth": 0, "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 17, "w": 24, "x": 0, "y": 60}, "id": 1, "options": {"alignValue": "left", "legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "mergeValues": true, "rowHeight": 0.9, "showValue": "auto", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "direction": "backward", "editorMode": "code", "expr": "topk(10, \n    sum by(request_uri,status) (\n        count_over_time(\n            {context_tenant=\"dtc-tenant\", k8s_namespace_name=\"kong\"} \n            | json \n            | request_uri =~ `^/shopping/.*` \n            | status != \"200\" \n            | status != \"400\"\n            | status != \"401\" \n            | status != \"404\" \n            [$__range]\n        )\n    )\n)\n\n", "queryType": "instant", "refId": "A"}], "title": "/shopping/ non 200/400/401/404", "type": "state-timeline"}], "preload": false, "schemaVersion": 41, "tags": [], "templating": {"list": [{"baseFilters": [], "datasource": {"type": "loki", "uid": "loki-dtc-tenant-52"}, "filters": [], "name": "Filters", "type": "adhoc"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "<PERSON><PERSON>'s temporary dashboard", "uid": "ceohquwvu9logc", "version": 49}