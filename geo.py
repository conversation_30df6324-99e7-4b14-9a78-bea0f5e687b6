import csv
import os
import logging
from os import environ as env
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv, find_dotenv
from time import sleep
import requests
import pandas as pd

_ = load_dotenv(find_dotenv())

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# ThreadPoolExecutor for handling concurrency
executor = ThreadPoolExecutor(max_workers=100)

# Baidu API key
API_KEY = env.get('BAIDU_API_KEY')

# Input and output CSV file paths
input_csv_file = '4Level.csv'
output_csv_file = '4Level-geo.csv'

# Number of rows to process in each chunk
rows_per_chunk = 10

def get_geocode(row, retry_count=3):
    address = row['省'] + row['市'] + row['区'] + row['街道']
    try:
        response = requests.get('https://api.map.baidu.com/geocoding/v3/', params={
            'address': address,
            'output': 'json',
            'ak': API_KEY
        })
        response.raise_for_status()
        data = response.json()
        if data['status'] == 0:
            location = data['result']['location']
            return location['lng'], location['lat']
        else:
            raise ValueError(f'Invalid status received in response: {data}')
    except Exception as e:
        logging.error('Failed to get geocode for address: %s. Error: %s', address, str(e))
        if retry_count > 0:
            sleep(2)
            return get_geocode(row, retry_count - 1)
        else:
            logging.error('Exceeded maximum retry count. Giving up on getting geocode for address: %s', address)
            return None

def process_chunk(chunk):
    logging.info('Processing a chunk of %d rows', len(chunk))
    results = []
    for _, row in chunk.iterrows():
        try:
            result = get_geocode(row)
            if result is None:
                result = (None, None)
            results.append(result)
        except Exception as e:
            logging.error('Failed to get geocode for row: %s. Error: %s', row, str(e))
            results.append((None, None))
    chunk['Longitude'], chunk['Latitude'] = zip(*results)
    with open(output_csv_file, 'a') as f:
        chunk.to_csv(f, header=f.tell()==0, index=False)
    logging.info('Finished processing a chunk')

def main():
    logging.info('Starting processing')
    
    # Check if input file exists
    if not os.path.isfile(input_csv_file):
        logging.error('The input file does not exist.')
        return
    
    # Check if output file can be opened for writing
    try:
        with open(output_csv_file, 'a') as f:
            pass
    except Exception as e:
        logging.error('Failed to open output file for writing: %s', str(e))
        return
    
    # Read and process the CSV file in chunks
    try:
        for i, chunk in enumerate(pd.read_csv(input_csv_file, chunksize=rows_per_chunk), 1):
            logging.info('Processing chunk #%d', i)
            process_chunk(chunk)
    except pd.errors.EmptyDataError:
        logging.error('No data in input file.')
    except pd.errors.ParserError:
        logging.error('Error parsing input file.')
    except Exception as e:
        logging.error('An error occurred: %s', str(e))
    
    logging.info('Finished processing')

if __name__ == '__main__':
    main()