import csv
import requests
import pandas as pd
import logging
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor
from retrying import retry

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# ThreadPoolExecutor for handling concurrency
executor = ThreadPoolExecutor(max_workers=100)

# Baidu API key
API_KEY = 'irGERIawpYOZyk0KXrENUpVhCK2N7aNu'

# Input and output CSV file paths
input_csv_file = '4Level.csv'
output_csv_file = '4Level-geo.csv'

# Size of each chunk to process at a time
chunk_size = 100

@retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
def get_geocode(row):
    # If 'Longitude' and 'Latitude' exist and are not null, return them directly
    if 'Longitude' in row and 'Latitude' in row and pd.notnull(row['Longitude']) and pd.notnull(row['Latitude']):
        return row['Longitude'], row['Latitude']

    # Otherwise, fetch from Baidu API
    address = row['省'] + row['市'] + row['区'] + row['街道']
    response = requests.get('https://api.map.baidu.com/geocoding/v3/', params={
        'address': address,
        'output': 'json',
        'ak': API_KEY
    })
    if response.status_code != 200:
        logging.error('Failed to get geocode for address: %s. Status code: %d', 
                      address, response.status_code)
        response.raise_for_status()
    try:
        data = response.json()
    except ValueError:
        logging.error('Invalid JSON received for address: %s', address)
        raise

    if data['status'] == 0:
        location = data['result']['location']
        return location['lng'], location['lat']
    else:
        raise ValueError('Invalid status received in response')

def process_chunk(chunk, output_df):
    logging.info('Processing a chunk of %d rows', len(chunk))

    # Filter the chunk to only include rows that are not in the output DataFrame
    chunk = chunk[~chunk.index.isin(output_df.index)]

    try:
        # get geocodes in parallel
        results = list(executor.map(get_geocode, [row for _, row in chunk.iterrows()]))

        # If results is empty, log a warning and return the chunk unprocessed
        if not results:
            logging.warning('No results received for chunk. Skipping.')
            return output_df

        # add geocodes to dataframe
        chunk['Longitude'], chunk['Latitude'] = zip(*results)

    except Exception as e:
        logging.error(f'Error occurred while processing chunk: {str(e)}')
        return output_df

    # write DataFrame chunk to CSV file
    with open(output_csv_file, 'a') as f:
        chunk.to_csv(f, header=f.tell()==0, index=False)
    
    # Return the updated output DataFrame
    return pd.concat([output_df, chunk])

def main():
    logging.info('Starting processing')

    # Read the existing output file into a DataFrame
    try:
        output_df = pd.read_csv(output_csv_file, index_col=0)
    except FileNotFoundError:
        output_df = pd.DataFrame()

    # read and process the CSV file in chunks
    for i, chunk in enumerate(pd.read_csv(input_csv_file, chunksize=chunk_size, index_col=0), 1):
        logging.info('Processing chunk #%d', i)
        output_df = process_chunk(chunk, output_df)

    logging.info('Finished processing')

if __name__ == '__main__':
    main()

