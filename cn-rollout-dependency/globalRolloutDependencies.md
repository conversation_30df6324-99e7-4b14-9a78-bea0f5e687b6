
```mermaid





flowchart LR
    %%{init: {"flowchart": {"defaultRenderer": "elk"}} }%%

    externalReturn([External Return to TSP/Store])
    click externalReturn "https://confluence.build.ingka.ikea.com/display/DCPD/Reverse+Flow%3A+External+Returns+via+TSP+to+Store+-+Launch+One+Pager"
    
    SPSOP([Sales Order Processing])
    click SPSOP "https://confluence.build.ingka.ikea.com/display/SP/Sales+Order+Processing"
    
    DSM([Delivery & Service Management])

    CENTIRO([Centiro])
    WOMGR([Work Order Manager])
    CAHUB([Capacity Hub])

    %% CHI Rollouts
    ARCOD([fa:fa-home CHI-1232 Auto Recovery of customer order deviation])
    GBOA([fa:fa-home CHI-1442 Goal based order allocation])
    ILOFFApps([fa:fa-home CHI-1844 ILOFF Apps])
    ILISF([fa:fa-home CHI-1677 Ingka Linehaul in Store Fulfilment])
    RIRIS([fa:fa-home CHI-1151 Receive internal returns in store])
    RFERTCU([fa:fa-home CHI-1629 Reverse Flow: External Returns via TSP to Central Unit])
    RFERTCU([fa:fa-home CHI-1630 Reverse Flow: External Returns via TSP to Store])
    SAMLACFF([fa:fa-home CHI-1790 SAMLA for CFF])
    STRUKTUR([fa:fa-home CHI-1351 STRUKTUR App])
    CHI_SOP([fa:fa-home CHI-1118 Sales Order Processing])

    CHI_SOP-->SPSOP

    %% 没有找到SMC
    ODM([Order Deviation Management])

    SAPI([fa:fa-check Selling API])
    
    CRE[Customer Returns] --> externalReturn
    SAMS[fa:fa-check Service Action Management System] --> CRE
    FOT[Fulfillment Options] --> CRE
    %% 没有OM，需要定义
    om[Order Management] --> CRE
    rpa[RPA] --> CRE
    iserve[fa:fa-check iServe] --> CRE



    FOT --> CAHUB
    FOT --> wlo([Waiting List Orders])
    FOT --> pc([Picking Capacity at Order Line Level])

    click fo "https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=147090503"

    gboa1([Goal based order allocation 1.0])

    click gboa1 "https://confluence.build.ingka.ikea.com/display/CNARCH/Goal+based+Order+Allocation"
    atp[Available to Promise] --> gboa1
    oalloc[Order Allocation] --> gboa1
    FOT --> gboa1

    pc --> gboa1

    fso([Full Serve Orders])
    click fso "https://confluence.build.ingka.ikea.com/display/DCPD/Full+serve+orders+-+Launch+One+Pager"

    speipos([Sales Price and Discount Calculation for iPOS])
    speipos --> fso
    fms([Fulfilment Management System])
    fso --> fms


    SPSOP --> fms

    gis([Global Inventory Service])
    cbd[fa:fa-check Corporate Base Data]
    mhs[fa:fa-check MHS]
    mhs --> gis
    cbd --> gis 

    sag[fa:fa-check Store Access Gateway] --> gis 

    fu[Fulfillment Units FLI/FUI]
    oi[One Inventory]
    fu --> gis 

    gis --> fso
    gis --> atp
    gis --> oi
    
    oi --> op
    oo[One Order]
    oo --> fms
    oob[One Order backwards]
    externalReturn --> oob
    oob --> oo

    os[One Selling]
    os --> oo

    ir[iRecover]
    slm[Sales Location Management]
    slm --> ir
    ir --> fms

    gis --> ir

    speipos --> os
    sp[Salja Pro]
    sp --> os
    id[iSell Decommission]
    sp --> id
    id --> os
    fso --> os
    op[One Promise]
    atp --> op
    op --> fms
    cia[fa:fa-check Customer Item Availablity ]
    cia --> op

    SPSOP --> id

    cbd --> pc
    om --> pc
    oalloc --> pc
    oalloc --> op

    dmp[Delivery Management Platform]
    dmp --> fms
    DSM --> dmp
    wlo --> os

    SAPI-->ODM

    CAHUB-->DSM
    CENTIRO-->DSM
    WOMGR-->DSM
    
    ODM-->ARCOD




    



    

```

Reference: [CHI projects](https://jira.digital.ingka.com/issues/?jql=project%20%3D%20CHI%20AND%20%22Push%2FPull%22%20%3DPush%20and%20type%3Dlaunch%20and%20resolution%20%3D%20Unresolved%20ORDER%20BY%20updated%20DESC%2C%20priority%20DESC)

