## System Master Catalog
load csv with headers from 'https://yangwushuang.com/system.smc.csv' as row 
MERGE (s:Systems {SystemMasterCatalogAbbreviation: row.SystemMasterCatalogAbbreviation})
SET
s.SystemName = row.SystemName,
s.SystemFullName = row.SystemFullName,
s.SystemMasterCatalogAbbreviation = row.SystemMasterCatalogAbbreviation,
s.SystemHome = row.SystemHome


## Rel_System_Component 
MATCH (s:Systems)
MATCH (b:Components{System: s.SystemMasterCatalogAbbreviation})
MERGE (a)-[:HAS]->(b);

## load CHIs
load csv with headers from 'https://yangwushuang.com/chi.csv' as row 
MERGE (chi:CHI {ChiId: row.ID})
SET chi.ChiID = row.ID
SET chi.name = row.Name


## Rel_CHI_System
load csv with headers from 'https://yangwushuang.com/CHIDependsOnSystem.csv' as row 
MERGE (chi:CHI {ChiID: row.ID})
MERGE (s:Systems {SystemName: row.DependsOnSystem})
SET chi.ChiID = row.ID
SET chi.Name = row.Name
Merge (chi)-[:DEPENDS_ON]->(s)

----------------------------------------------------------------------------------------------------
上面的暂时没用，正文由此开始

## load csv exported from SMC biz
```
load csv with headers from 'file:///ikeasystems/smc.biz.all.csv' as row 
MERGE (s:Systems {SystemCode: row.`System Master Abbreviation`})
Merge (du:DigitalUnits {name: row.`Owned by Digital Unit`})
Merge (aa:ArchitectureArea {name: coalesce(row.`Architecture Area`,"UNDEFINED")})
set s=row,
s.Name = row.`System Master Name`,
s.Title = row.`System Master Name`,
s.SystemCode=row.`System Master Abbreviation`,
s.SystemFullName = row.`Full Name`

merge (s)-[:OWNED_BY]->(du)
merge (s)-[:BELONGS_TO]->(aa)
```

## load csv exported from SMC tech
```
load csv with headers from 'file:///ikeasystems/smc.tech.all.csv' as row 
MERGE (s:Systems {SystemCode: row.`System Master Abbreviation`})
Merge (du:DigitalUnits {name: row.`Owned by Digital Unit`})
Merge (aa:ArchitectureArea {name: coalesce(row.`Architecture Area`,"UNDEFINED")})
set s=row,
s.Name = row.`System Master Name`,
s.Title = row.`System Master Name`,
s.SystemCode=row.`System Master Abbreviation`,
s.SystemFullName = row.`Full Name`
merge (s)-[:OWNED_BY]->(du)
merge (s)-[:BELONGS_TO]->(aa)
```

## set uniqueness on SystemCode
```
create constraint for (s:Systems) require s.SystemCode is Unique;
```

## load Components
```
load csv with headers from 'file:///ikeasystems/systemComponents.csv' as row 
MERGE (c:Components {RandID: row.RandId})
set c.Name = row.ComponentName,
c.Title = row.ComponentName,
c.System = row.System
```

## set uniqueness on Name and System
```
create constraint for (c:Components) require (c.Name, c.System) is Unique;
```

## create relations
```
match (c:Components) 
merge (s:Systems {SystemCode: c.System}) 
merge (s)-[r:HAS_COMPONENT]->(c)
```

## create index for search
CREATE FULLTEXT INDEX namesAndDescriptions 
FOR (n:Systems) 
ON EACH [
    n.`Full Name`, 
    n.`Technical Description`, 
    n.`Business Description`, 
    n.`System Master Abbreviation`
    ]

### example search
CALL db.index.fulltext.queryNodes("namesAndDescriptions", "China") YIELD node, score
RETURN node.`Full Name`, node.`System Master Abbreviation`, node.`Owned by Digital Unit`, score

## Create CN Rollouts
create (rollouts:Rollouts {Name:'DSM China'})

## Create Rollouts Dependencies
match (rollout:Rollouts {Name: 'DSM China'}), (systems:Systems {SystemCode: 'WOMGR'}) merge (rollout)-[:DEPENDS_ON]->(systems)
match (rollout:Rollouts {Name: 'DSM China'}), (systems:Systems {SystemCode: 'CENTIRO'}) merge (rollout)-[:DEPENDS_ON]->(systems)
match (rollout:Rollouts {Name: 'DSM China'}), (systems:Systems {SystemCode: 'CAHUB'}) merge (rollout)-[:DEPENDS_ON]->(systems)
match (rollout:Rollouts {Name: 'DSM China'}), (systems:Systems {SystemCode: 'SBSMANAGER'}) merge (rollout)-[:DEPENDS_ON]->(systems)
match (rollout:Rollouts {Name: 'DSM China'}), (systems:Systems {SystemCode: 'CENTSBS'}) merge (rollout)-[:DEPENDS_ON]->(systems)

### Example of Dependencies
match (n:Systems {SystemCode: 'WOMGR'})-[:DEPENDS_ON]-(r:Rollouts) return *