Internal Object ID,System Master Name,Full Name,System Master Abbreviation,Status,Owned by Digital Unit,Architecture Area,Business Owner,Product Owner,Engineering Manager,UX Design Lead,Data and Analytics Lead,System Home,System Access,Business Description,Technical Description,Nickname Description,Hosting,System Style,System Group,Service in NowIT,Jira Project,Target Group,User Interface,Related Digital Unit,Core Capability Contribution,Differentiation Ambition,Make vs Buy,Lifecycle,Lifecycle Date,Archived Due to,Archived in System Master Catalog,Retired in System Master Catalog,Created in System Master Catalog,System Master Identifier,System Master Identifier old,System Type,Inter Business Solution,Notes,Team,Script Message,Import Parameter,Updated,Created,Key
114383,7Zip,7Zip,ZIP7,Active,Digital Workplace,End User Enablement and Productivity,,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>,,,,,,,,Client,,,IKEA Software Tools,,,,,None,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100866,SM-100866,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114383
114132,Active Directory,Microsoft Active Directory,ADIR,Active,Identity and Access Management,Identity and Access Management,<PERSON>,<PERSON>,<PERSON>,,,,,Corporate network IAM directory service.,,,On-Premise,Platform,Identity & Access Management,,,,,,Strongly,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100612,SM-100612,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114132
114128,Active Directory Federation Services,Microsoft Active Directory Federation Services,ADFS,Active,Identity and Access Management,Identity and Access Management,,Emily Millnert,Emily Millnert,,,https://confluence.build.ingka.ikea.com/display/EKR234IA/ADFS+decommissioning,,"

End User SSO Authentication","On Prem Federation and Authentication for SSO within and outside (Internet) connectivity. Only for NIR and Inter Suppliers. 
All Ingka and remaining Inter authentication has been moved to Entra ID. ",,On-Premise,Platform,Identity & Access Management,,,,,Identity and Access Management,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100616,SM-100616,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-05 09:09,2020-Nov-06 15:29,SMC-114128
114122,Active Reports,Active Reports,ACTREP,Active,Engineering Services,Developer Enablement,,Jan Magnusson,Niclas Strandéus,,,,,,,,Central Private Hosting,,,development workbench,,Co-worker,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100610,SM-100610,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-21 16:36,2020-Nov-06 15:29,SMC-114122
318585,ADAPT,ADAPT,ADAPT,Active,Finance and Procurement,Finance and Tax,Jonathan Parton,Iman Nasser,Mohamed Amin,,,,,"

ADAPT is ML tool that will help INGKA to improve the invoice processing for Ingka.

This will reduce and/or eliminate manual interventions by re-engineering enriching invoice processing using AI/ML.","

ADAPT is IBM Tools hosted on IBM cloud&nbsp;

Implementing ADAPT module to automate business rules, invoice matching using, tax and GL prediction.&nbsp;&nbsp;&nbsp;&nbsp;

These enriched transactions will be ingested to VIM for further processing and posting into SAP.

The current manual intensive accounts payable process is being transformed to an automated solution.",,SaaS,Digital Product,BOT as Successor,sap basis,,Partner,,,Partly,,Buy,Invest,,,,,2021-Nov-11,SM-105177,SM-105177,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Nov-11 13:28,SMC-318585
114135,Adobe Acrobat DC,Adobe Acrobat DC,ADOACRDC,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100619,SM-100619,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114135
114131,Adobe Acrobat Family,Adobe Acrobat Family,ADOACRF,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100620,SM-100620,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114131
114130,Adobe After Effects,Adobe After Effects,ADOAEFF,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100615,SM-100615,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114130
114134,Adobe Captivate,Adobe Captivate,ADOCAP,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100622,SM-100622,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114134
114133,Adobe Creative Cloud Enterprise,Adobe Creative Cloud Enterprise (CC),ADOCCE,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client||SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100623,SM-100623,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114133
114138,Adobe Creative Suite Design and Web,Adobe Creative Suite Design and Web,ADOCSDW,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100618,SM-100618,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114138
114137,Adobe Creative Suite Production,Adobe Creative Suite Production,ADOCSP,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100625,SM-100625,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114137
114136,Adobe Design CC,Adobe Design CC,ADODESCC,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100626,SM-100626,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114136
114144,Adobe Flash Player,Adobe Flash Player,ADOFLPL,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,None,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100628,SM-100628,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114144
114141,Adobe FlashBuilder,Adobe FlashBuilder,ADOFLBU,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,None,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100621,SM-100621,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114141
114140,Adobe InDesign Server,Adobe InDesign Server,ADOIND,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100629,SM-100629,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114140
114139,Adobe Photoshop Elements,Adobe Photoshop Elements,ADOPHOEL,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100624,SM-100624,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114139
114143,Adobe Premiere Elements,Adobe Premiere Elements,ADOPREME,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100631,SM-100631,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114143
114142,Adobe Premiere Pro CC,Adobe Premiere Pro CC,ADOPREMP,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100632,SM-100632,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114142
114147,Adobe Reader,Adobe Reader,ADOREAD,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,IKEA Software Tools,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100627,SM-100627,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114147
114151,Adobe Sign,Adobe Sign,ADOSIGN,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100634,SM-100634,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114151
114145,Adobe Web CC,Adobe Web CC,ADOWEBCC,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100635,SM-100635,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114145
114382,Advantum Drawings,Advantum Drawings,ADVDRAW,Retired,Inter IKEA,,,,,,,,,,,,,,,advantum document handling ikea components eqb,,,,,,,,,,,,2023-Apr-18,2020-Nov-06,SM-100978,SM-100978,Technology System,,"

This is IMS (Indirect Material &amp; Services) system for which IKEA Components (Inter) took responsibility. So, retiring from Ingka SMC.",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114382
583512,Aha!,Aha! Roadmap Software,AHA,Active,Engineering Services,Developer Enablement,Christofer Anderberg,Kenneth Flatby,Kenneth Flatby,,,https://ingka.aha.io/,,"

Aha!’s&nbsp;primary function is to enable the&nbsp;Group Digital Roadmap Function&nbsp;with their high-level roadmapping and mapping the Portfolio Epics in our Digital roadmap towards different strategic objects such as&nbsp;Movements,&nbsp;Strategy pillars&nbsp;&amp;&nbsp;Top level OKR's. It also enables all of Group Digital users to view and interpret these views, increasing visibility; collaboration and strategic understanding throughout the Digital organisation.","

A&nbsp;Roadmap planning&nbsp;tool that brings a new level of transparency and collaboration that is critical to help transform an organization and deliver new digital customer experiences. It integrates with&nbsp;Jira&nbsp;to pull defined work from all areas of an organisation into a centralised repository for the purposes of increasing visibility; showing alignment to the strategy and useful reporting to embed agile ways of working.",,SaaS,Platform,,,Engineering Collaboration,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2023-Mar-08,SM-116584,SM-116584,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-22 11:28,2023-Mar-08 10:47,SMC-583512
177165,AIOPS Automation Framework,AIOPS Automation Framework,AIOPS,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Christian Kullendorff,Karan Honavar,,,https://automation.ingka.com/,,"

A custom automation framework created by AI Ops to enable orchestration and end to end automation in a self serviced manner for technology teams. The framework allows for direct monitoring &amp; recovery, ITSM based remediation and hybrid automation. The framework is created through available public cloud platforms and available automation tools at Ingka.","

Currently consists of:

Remediation Framework

Direct Monitoring &amp; Recovery

Hybrid Automation Framework","

opi",Public Cloud,Platform,,AI Ops Services,AI Ops - Operational Intelligence - CDI,Co-worker,,,Partly,,Make,Invest,,,,,2021-Sep-15,SM-105101,SM-105101,Technology System,,"

Created by script using data in Nursery",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-25 10:55,2021-Sep-15 09:44,SMC-177165
114146,AIX TP,AIX TP,AIXTP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Andreas Stehn,Andreas Stehn,,,https://confluence.build.ingka.ikea.com/x/PyPRBg,,Server Platform from IBM,"

Covers several internal products, AIX TS, AIX SITI, AIX Server TP, AIX Packagin.

Systems on AIX: IRW, WTP, SOM, ISI - IKEA Supplier Integration, GPS, InfoSphere Platform, JDA DSP Demand &amp; Supply Planning, CALC, CNS, ...","

dcp aix",Central Private Hosting||Distributed Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100630,SM-100630,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114146
126596,AKADDOS,Akamai Prolexic DDoS Protection,AKADDOS,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Stephan Agerley,Fredrik Åhlstedt,,,,,"

Provides protection and mitigation against DDOS Attack","

Provides protection and mitigation against DDOS Attack",,SaaS,Platform,,,,,,,Strongly,Commodity,Buy,Invest,,,,,2021-Mar-25,SM-101079,SM-101079,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Mar-25 15:34,SMC-126596
114154,Akamai CDN,Akamai Content Delivery Network,AKACDN,Active,Customer Meeting Point Web,Digital Platform and Core Infrastructure,,Peter Hansson Åkerblom,Peter Hansson Åkerblom,,,https://confluence.build.ingka.ikea.com/x/0kf6DQ,,"About​ 

Akamai CDN is an externally provided, internally managed, global Content Delivery Network service.

Value Proposition​ 

Accelerated and protected application deliveries over internet, with features that can enhance functionality and reduce backend infrastructure costs.​ 

Akamai CDN contributes to the Value Creation Goal Better Company since it is an enabling service.

Customer/Co-worker missions and needs ​

Enabling...​ 

...accelerated application deliveries over internet.​

...security for applications delivered over internet.​

...possibilities of enhanced/additional functionalities for applications delivered over internet.​

...reduced need for origin infrastructure scale.​

Available in Managed and Self-Service delivery models. ​

Support available on https://akacdn.ingka.com",,N/A,SaaS,Platform,,,,,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100637,SM-100637,Technology System,,Martin Persson8/4 changed to So,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-10 08:56,2020-Nov-06 15:30,SMC-114154
126593,Akamai Web Application Firewall,Akamai Web Application Firewall,AKAWAF,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Stephan Agerley,Fredrik Åhlstedt,,,,,"

Web Application firewall services",,,SaaS,Platform,,,,,,,Partly,Commodity,Buy,Invest,,,,,2021-Mar-25,SM-101076,SM-101076,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Mar-25 15:28,SMC-126593
374956,Algosec,Algosec,ASEC,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Hans Broomé,Venkatesh Narasingam Kuppusamy,,,https://confluence.build.ingka.ikea.com/display/FO/Firewall+Orchestration+Home,,"

Provides the documentation and orchestration capability for managing communication contracts for enterprise application and related firewall openings.","

Algosec is the product where which enables and manages the firewall orchestration.","

cnn Algosec",Central Private Hosting,Platform,,production security services,Firewall Orchestration,,,,Partly,Commodity,Buy,Tolerate,,,,,2022-Apr-11,SM-116184,SM-116184,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Apr-11 09:40,SMC-374956
114149,AliCloud,AliCloud,ALIC,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Felix Lv,Felix Lv,,,https://confluence.build.ingka.ikea.com/x/iomlB,,"

ALICLOUD is the preferred public cloud vendor for China mainland. China restricts public internet access from rest of world, thus China local cloud provider services can be accessed by customer &nbsp;without restrictions.","

ALICLOUD features IaaS/PaaS/SaaS solutions for a very large set of services and features their own Marketplace, being very much like AWS services.","

clh ccoe ali",Public Cloud||SaaS,,,,,,,,Partly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100638,SM-100638,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114149
114148,ALM,Application Life Cycle Management,ALM,Retired,Engineering Services,Developer Enablement,,,,,,,,"

ALM is a test management system that helps teams organize and analyze tests.","

Suited for Waterfall development methods.",,On-Premise,,Test Enablement,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2022-Sep-23,2020-Nov-06,SM-100633,SM-100633,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114148
114158,Amazon Web Services,Amazon Web Services,AWS,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Bonny Lindberg,Tobias Berg,,,https://confluence.build.ingka.ikea.com/x/iomlB,,"

AWS is a public cloud provider mainly used within IKEA for specific services (used more frequent within INTER than INGKA)","

AWS features IaaS/PaaS/SaaS solutions for a very large set of services and features their own Marketplace","

clh, aws, amazon cloud, ccoe",Public Cloud,Platform,,public cloud iaas TS,,,,,Partly,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100645,SM-100645,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-04 08:32,2020-Nov-06 15:30,SMC-114158
637776,AMSboard,Attendance Board,AMS,Active,France,Data and Integration,,,,,Thomas FAUCOEUR,https://confluence.build.ingka.ikea.com/display/DIGILPFR/Attendance+Board+-+Product+Information,,"

Digital attendance board to manage the presence of the management team in the units. Display duty rosters and teleworkers. This tool can be used on all devices, both mobile and in kiosk mode for corridor display. When used with a touch screen, it allows you to manage your presence from the screen, or to consult the presence in another unit.","

Digital power platform tool for managing presence in units. Kiosk use and available on all devices in a mobile way",,,Digital Product,,,France,,,Core Digital Infrastructure,,,,,,,,,2023-Jul-25,SM-116775,SM-116775,Technology System,,,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jul-25 14:51,SMC-637776
114405,Analytics Platform,Analytics Platform,IAP,Active,Data Integration and Middleware,Data and Integration,,Anders Ryberg,Anders Ryberg,,,https://confluence.build.ingka.ikea.com/display/DLP/Analytics+Platform,,"

Enabling a federated data platform based on guidelines, pattern, and tools for data to expose and build analytics-oriented products on, thus connecting to the data mesh principles.","

Google Cloud Platform (GCP)","

Was named IKEA Data Platform from the beginning, then changed to Ingka Data Platform (IDP).

AP - Analytics Platform

IAP - Ingka Analytics Platform",Public Cloud,,,,Analytics Platform,Co-worker,,,,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-101001,SM-101001,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114405
114127,Anglonas dictionary engl lith,Anglonas dictionary engl/lith,ADICLIT,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

English Lithuanian dictionary",,,Client,,,IKEA Software Tools,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100617,SM-100617,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114127
658211,Animaker,Using Animaker,ANIMAKER,Active,Canada,Developer Enablement,Melanie Trudeau,Melanie Trudeau,Brian Berneker,,,https://confluence.build.ingka.ikea.com/display/CANADAHUB/Animaker,,"

The Canadian Remote Customer Meeting Point (formerly known as the CSC) that would like to use a third-party tool to create videos for their co-workers.
The main subjects will be for work processes and other IKEA-relevant information.

The types of information are unit work procedures and IKEA-related news about products, stores, etc. We can be flexible if there is a need to limit what we upload to the site if that ends up being a restriction.

Website:&nbsp;https://www.animaker.com/\\","

Third party website:&nbsp;https://www.animaker.com/\\","

Name of the 3rd party vendors product.",SaaS,Digital Product,,,,Co-worker,Web browser,Digital Workplace,None,Differentiating,Buy,Tolerate,,,,,2023-Sep-14,SM-116801,SM-116801,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3790,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Sep-14 15:49,SMC-658211
485291,ANSIBLE TOWER,Anisble Tower,ATOWER,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Patrik Johnsson,Patrik Johnsson,,,,,"

Versatile automation and configuration management software primarily used within infrastructure domain.","

The focal point for infrastructure management and provisioning. Allows for re-use and repeatable actions through playbooks for a diverse set of actions and use-cases.",,Central Private Hosting,Platform,,,,,,,None,Commodity,Buy,Invest,,,,,2022-Jul-14,SM-116283,SM-116283,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Jul-14 12:10,SMC-485291
114381,API Catalog,API Catalog,APIM,Active,Data Integration and Middleware,Data and Integration,,Nihar Shah,Nihar Shah,,,https://confluence.build.ingka.ikea.com/display/APIM,,"

API management is the the framework of creating and publishing APIs, enforcing their usage policies, controlling access, nurturing the subscriber community, collecting and analyzing usage statistics, and reporting on performance. We in Ingka API Management&nbsp;help co-workers in Ingka provide and consume Business Web APIs.

Ingka API Management service including API Workbench, API Portal, API Gateways.","

API Catalog is a own developed catalog in GCP exposing APIs. The APIs can be discovered in the API Portal Plugins in the Allen portal.",,On-Premise||Public Cloud,,Integration Enablement Systems,ingka api management,,Co-worker,,,Strongly,Differentiating,Make||Buy,Invest,,,,,2020-Nov-06,SM-100985,SM-100985,Technology System,,,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114381
114417,API Design,API Design,SWAGGER,Active,Data Integration and Middleware,Data and Integration,,Akshi Agrawal,Akshi Agrawal,,,,,"

API Development portal. Contains API defintions in swagger/OpenAPI format and provides code generation and tools to trout an API. Also provides API documentation",,,,,Integration Enablement Systems,,,,,,Partly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-101015,SM-101015,Technology System,,,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Dec-04 10:04,2020-Nov-06 15:30,SMC-114417
607943,API Gateway,API Gateway,APIG,Active,Data Integration and Middleware,Data and Integration,,Akshi Agrawal,Akshi Agrawal,,,https://allen.ingka.com/apim,,"

Ingka API Gateway.","

Kong Enterprise&nbsp;has been selected as the partner of INGKA API Management with a high expectation on NGINX based Kong Proxy (API Gateway) and open source driven Kong Plugin repository for APIs.",,,Platform,Integration Enablement Systems,,API Gateway (API Management),,,,,,,Invest,,,,,2023-Apr-18,SM-116637,SM-116637,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2023-Dec-04 10:04,2023-Apr-18 14:23,SMC-607943
179427,AppDynamics,AppDynamics,APPD,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Christian Kullendorff,Johan Jacobsson,,,https://confluence.build.ingka.ikea.com/display/ITOI/Operational+Intelligence,,"

The technology platform AppDynamics provided by Cisco. Replaces the former entry of RTM - Real Time Monitoring.

Solution for real time application and business performance monitoring which gives end-2-end visibility into customer journeys / business processes.","

AppDynamics","

opi",SaaS,Platform,,real time monitoring,,,,,Partly,Commodity,Buy,Eliminate,,,,,2021-Sep-23,SM-105113,SM-105113,Technology System,,"

Created by script using data in Nursery",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Sep-23 16:42,SMC-179427
566112,Apple Business Manager,Apple Business Manager,ABM,Active,Digital Workplace,End User Enablement and Productivity,,Lukasz Sudak,Lukasz Sudak,,,https://business.apple.com/,,"

A System that enables us to enroll Apple&nbsp;devices to our different MDM Systems",,,SaaS,,,,Frontline Workplace,,,,Minor,Commodity,Buy,Invest,2023-Aug-22,,,,2022-Dec-20,SM-116485,SM-116485,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:09,2022-Dec-20 07:42,SMC-566112
114411,Application Delivery Manager,Application Delivery Manager (ADM),NMAS,Active,Digital Workplace,End User Enablement and Productivity,,,,,,https://confluence.build.ingka.ikea.com/x/SvW1E,,"

Netscaler Management and Analytics System","

Citrix ADM provides centralized network management, analytics and automation to support applications and ADCs deployed across hybrid cloud and containerized infrastructures.","

cwe adm nmas",On-Premise,,,,,,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-101007,SM-101007,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114411
609014,Application Hosting as a Service,Application Hosting as a Service,AHAAS,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Jonas Wennberg,Urban Martinsson,,,,,"

Application Hosting Self Service platform","

Self-service platform to fully manage your on-prem application. Create/modify/remove servers, manage configuration and manage your application in one portal

https://selfservice.apphosting01.ikea.com/

For onboarding, contact us in Slack channel #application-hosting-self-service

&nbsp;",,Central Private Hosting,Platform,,,Application Hosting,,,,Minor,Commodity,Make,Tolerate,,,,,2023-Apr-24,SM-116649,SM-116649,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-24 09:42,SMC-609014
702737,Ariadne Maps US,Ariadne Maps US,AMUS,Planned,USA,Data and Integration,Tiane Hill,Tiane Hill,,,Brendan Mcgeehan,,,"

Reveals insights about customer behavior, allows optimization of store layout, merchandising, and cust shopping tools; used to enhance overall operational efficiency

Analyze new/repeat visitation patterns

Evaluate ROI for marketing spend

Optimize labor expenditure

Implement real-time alerts for efficient resource allocation

Test/validate assumptions

Digitize/report manual flow processes

Create traffic and dwell heatmaps

Analyze media and HFB engagement

Analyze traffic flow

Recognize behavior patterns","

Customer location tracking solution with precise and anonymous tracking capabilities. Utilizes ‘surveyors’ to passively track visitors w/o relying on an app, network, proprietary hardware, or cameras. Surveyors sense signals emitted by smartphones. Thru signal intensity it approximates distance between users and surveyors. Multiple surveyors and enhanced data on area geometry and topology, provides improved/accurate approx of visitor positions, count, and dwell times in store. Shows people by area, avg. duration, conversion ratios, and heatmap","

Ariadne Maps US

Customer Analytics-Ariadne Maps",,Digital Product,,,,,,Store Co-Worker Experience,,,,,,,,,2024-Feb-02,SM-116957,SM-116957,Technology System,,,,Created by smc-magic using ticket SYSCATSD-6287,,2024-Feb-02 14:30,2024-Feb-02 14:30,SMC-702737
114387,Artifactory,Jfrog Artifactory,ARTFACT,Active,Engineering Services,Developer Enablement,,Jan Magnusson,Niclas Strandéus,,,,,"

Artifact repository/DML to store binaries, packages, containers etc that is a result of a build or recieved from a vendor","

FIXED",,Public Cloud||SaaS,,Continuous Integration,,,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100986,SM-100986,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Dec-21 17:08,2020-Nov-06 15:30,SMC-114387
114152,Artios XLGuide,Artios XLGuide,ARTCADXL,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise||Client,,,,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100636,SM-100636,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114152
114153,ArtiosCAD,ArtiosCAD,ARTCAD,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100640,SM-100640,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114153
114150,ArtiosCAD User settings,ArtiosCAD User settings,ARTCADUS,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100641,SM-100641,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114150
568371,Aruba Central,Aruba Central,ARCENTRAL,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Tobias Johnsson,Christian Winberg,,,,,"

Management Platform for distributed connectivity in distributed IKEA sites. Like stores, warehouses and other physical meeting points.","

Aruba Central provides a single point of control to oversee every aspect of wired and wireless LANs, for the distributed landscape.&nbsp;",,,Platform,,,,,,,,,,,,,,,2023-Jan-03,SM-116495,SM-116495,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jan-03 15:29,SMC-568371
528018,Atkomst,Åtkomst,ATKOMST,Active,Identity and Access Management,Identity and Access Management,Johan Finndahl,Jelena Sokolic,Johan Finndahl,,,https://confluence.build.ingka.ikea.com/x/VLR5HQ,,"

Our goal is to setup ONE authorization service that support Ingka’s need of fine-grained authorizations within our digital retail landscape.

Åtkomst Home - Atkomst - Confluence (ikea.com)","

Åtkomst is the name of the new Authorization Service for Ingka based on the consolidation between DFP and AuthService. The consolidation and setup of Åtkomst is a collaboration between the IAM team and ILO/FF.","

Åtkomst is the Swedish word for Access - to make contact with or gain access to; be able to reach, approach, enter, etc.",Public Cloud,Digital Product,Identity & Access Management,Identity & access management,Atkomst,Partner||Co-worker,,,Strongly,Innovating,Make,Invest,,,,,2022-Nov-22,SM-116441,SM-116441,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Nov-22 11:30,SMC-528018
614884,ATTACKFORGE,ATTACKFORGE,ATTACKFORG,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Rufus Järnefelt,Martin Svensson,,,,,"

Attackforge is a Collaboration &amp; Productivity Tool for cyber security teams.&nbsp;

By supplying consistent, relevant information about vulnerabilities in our assets in INGKA to cyber engineers and other stakeholders, we will be able to make risk-based decisions better. This data can be used to compare vulnerabilities &amp; risk between domains, products and different platforms. The data also could then be used to find other assets with the same setup that possibly are vulnerable to the same issues that were found in the Attackforge. By using the birds-eye view on the data in Attackforge, it is possible to find one issue, mitigate and remediate many vulnerabilities in INGKA, not just the specific tested asset.&nbsp;&nbsp;

The tool will be mainly used by pentesters in INGKA but also by consultants engaged by Ingka to perform pentests on our behalf.

Data collected with attackforge will provide an overview of what cyber training and research is needed in the company.","

Ingka attackforge portal

AttackForge - Pentest Management and Reporting Tool",,Public Cloud||SaaS,Platform,,,,,,,Strongly,Commodity,Buy,Invest,,,,,2023-May-26,SM-116701,SM-116701,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2927,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-May-26 11:26,SMC-614884
114157,Audio Meeting,Audio Meeting (Microsoft),AUDMEET,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,SaaS,,,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100643,SM-100643,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114157
114120,AutoCAD Architecture,AutoCAD Architecture,ACADARC,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100600,SM-100600,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114120
114117,AutoCAD Architecture Content Pack,AutoCAD Architecture Content Pack,ACADACP,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100605,SM-100605,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114117
114119,AutoCAD Architecture Settings,AutoCAD Architecture Settings,ACADAS,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100607,SM-100607,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114119
114118,AutoCAD Architecture User Settings,AutoCAD Architecture User Settings,ACADAUS,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100608,SM-100608,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114118
114123,AutoCAD Borrow License,AutoCAD Borrow License,ACADBOR,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100603,SM-100603,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114123
114121,Autodesk 3ds Max,Autodesk 3ds Max,ADESK3DS,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

3ds max",,,Client||On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100611,SM-100611,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114121
114126,Autodesk BIM 360,Autodesk BIM 360,ADESKBIM,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100606,SM-100606,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114126
114125,Autodesk Design Review,Autodesk Design Review,ADESKDES,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100613,SM-100613,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114125
114124,Autodesk DWG True View,Autodesk DWG True View,ADESKDWG,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100614,SM-100614,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114124
114129,Autodesk Showcase,Autodesk Showcase,ADESKSHO,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100609,SM-100609,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:29,SMC-114129
682683,Automation Framework API,Automation Framework API,AFAPI,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Christian Kullendorff,Karan Honavar,,,,,"

Our Platform&nbsp;provides various REST APIs, which are active by default. These APIs provide the ability to interact with various&nbsp;ServiceNow&nbsp;functionality within your application. Such functionality includes the ability to perform create, read, update operations on existing modules, insert data into, retrieve information from, and run transforms against a MetricBase database (MetricBase Time Series API, and many others.","

The NOW IT AF API stands as a gateway to unlock the capabilities of the IKEA ServiceNow backend, serving as a bridge between users and a spectrum of automated processes. This API not only simplifies access but also empowers users to orchestrate and streamline intricate workflows effortlessly.",,,Digital Product,,,,,,,,,,,,,,,2023-Dec-08,SM-116912,SM-116912,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4746,,2023-Dec-08 13:00,2023-Dec-08 13:00,SMC-682683
605808,Azure Active Directory Connect,Microsoft Azure Active Directory Connect,AADC,Active,Identity and Access Management,Identity and Access Management,Andreas Andersson,Daniel Fors,Andreas Andersson,,,,,"

Azure AD Connect is&nbsp;a bridge solution between Ingka's on-prem Active Directory instance and Ingka's cloud-based Azure Active Directory tenant.","

Azure AD Connect is&nbsp;a bridge solution between Ingka's on-prem Active Directory instance and Ingka's cloud-based Azure Active Directory tenant.",,On-Premise||SaaS,Platform,Identity & Access Management,,,,,,Strongly,Innovating,Buy,Invest,,,,,2023-Apr-03,SM-116616,SM-116616,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-03 11:53,SMC-605808
509732,Azure ARC,Azure Arc,AZARC,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,Karan Honavar,Karan Honavar,Patrik Johnsson,,,,,"

Management Platform which extends cloud management features to an on-premise environment.","

Management platform extending Microsoft Azure functionality to manage on-premise and hybrid resources","

Arc",Public Cloud||Client,Platform,,,,,,,None,Commodity,Buy,Tolerate,,,,,2022-Sep-27,SM-116361,SM-116361,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Sep-27 16:06,SMC-509732
114160,B2B Integration Platform,B2B integration platform,B2BIP,Active,Data Integration and Middleware,Data and Integration,,Anna Relkman,Anna Relkman,,,https://confluence.build.ingka.ikea.com/display/WMB2B/,,"

WebMethods (B2B) Integration&nbsp;refers to IT-mediated exchange of business data between IT systems of business partners which are part of business processes directly and indirectly&nbsp;related to the buying, selling and trading of products, services and information. Data exchange happens in public (Internet) or private networks (marketplaces, VANs). Examples of business partners are companies like suppliers and carriers and authorities like customs.","

IKEAs B2B Platform is mainly based on standard components named webMethods, bought from Software AG. The B2B Platform provides a rich set of built-in convenience functionalities and implements industry and W3C standards related to communication protocols, document formats, document contents, adapters, orchestration of integration logic and automated processes. On top of that the B2B infrastructure provides a framework which consists of components built by the B2BDevFactory team to support application – partner integrations. This can be viewed as an extra layer on top of the (bought) webMethods infrastructure software. This IKEA framework (IKEAFwk) provides protocols, concepts and adaptors for internal Integration for instance for JDBC, HTTP/S, File polling etc.","

IKEA Integration Platform (IIP)",On-Premise,,Integration Enablement Systems||IKEA Integration Platform,b2b integration platform,,None,,,,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100646,SM-100646,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114160
114159,BAO,BAO,BAO,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,,,,,,On-Premise,,,,,,,,,,Buy,Decommissioned,,,,2023-Aug-22,2020-Nov-06,SM-100647,SM-100647,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114159
114163,Barium,Barium Live,BARIUM,Active,Finance and Procurement,End User Enablement and Productivity,,Anders Jälén,,,,,,"

Process modeling and execution system",,,SaaS,,,process design and modeling,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100642,SM-100642,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-24 11:44,2020-Nov-06 15:30,SMC-114163
114162,Beyond Compare,Beyond Compare,BEYONDC,Retired,Engineering Services,Developer Enablement,,,,,,,,"

Tool to compare files and text",,,Client,,,development workbench,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Jun-05,2020-Nov-06,SM-100648,SM-100648,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114162
371568,BigIQ,BigIQ,BIQ,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,Johan Nordbeck,Johan Nordbeck,Johan Nordbeck,Janus-Paul Schimrosczik,Janus-Paul Schimrosczik,https://bigiqcl.ikea.com,https://bigiqcl.ikea.com,"F5 central management system to monitor, configure, provide licenses and backup devices like BIG-IP. It is used as API automation gateway to deploy configs. ",BIG-IQ consist of CM HA cluster and 6 collector DCDs,"

cnn F5&nbsp;",Central Private Hosting,Platform,,,,,,,None,Commodity,Buy,Tolerate,,,,,2022-Mar-31,SM-116169,SM-116169,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-05 08:30,2022-Mar-31 11:11,SMC-371568
114391,Bizagi,Bizagi,BIZAGI,Active,Digital Workplace,End User Enablement and Productivity,,Dino Omerhodzic,Dino Omerhodzic,,,https://iweof.sharepoint.com/teams/o365g_testgroup_issemal/SitePages/Bizagi.aspx,,Process modeling and execution system,,,Client||SaaS,,,process design and modeling,,,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100988,SM-100988,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2024-Jan-19 15:08,2020-Nov-06 15:30,SMC-114391
127668,Black Duck,Black Duck,BLACKD,Retired,Cyber Security,Developer Enablement,Pål Göran Stensson,Rajeev Kumar Jain,Gustav Lundsgård,,,,https://ingka.app.blackduck.com/,"Blackduck is a SCA tool.
SCA identifies all the open source software components in a codebase and maps that inventory to a list of current known &amp; publicly disclosed vulnerabilities and legal licensing issues.",,,SaaS,Platform,,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2024-Jan-05,2021-Apr-06,SM-101090,SM-101090,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2024-Jan-05 14:58,2021-Apr-06 09:10,SMC-127668
114155,BMC Application Visibility Management,BMC Application Visibility Management,AVM,Retired,Data Integration and Middleware,Digital Platform and Core Infrastructure,,,,,,,,Synthetic monitoring used for Availability measurements,"

BMC system for collecting availability figures",,On-Premise,Platform,,monitoring,,,,,Minor,Commodity,Buy,Decommissioned,,"

decom as part of BMC modernization",,2023-Aug-28,2020-Nov-06,SM-100639,SM-100639,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114155
114161,BMC Patrol,BMC Patrol - replaced by infra monitoring?,BMCPAT,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,,,Infrastructure monitoring Contains many self developed KM's,,"

iop",On-Premise,,,event management,,,,,,Commodity,Buy,Decommissioned,,,,2022-Mar-28,2020-Nov-06,SM-100649,SM-100649,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114161
114164,BMC Truesight Operations Manager,BMC Truesight Operations Manager,BMCTRUE,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,,,"

Event Management solution Contains integrations to many monitoring scripts/alert configurations created by application teams",,,On-Premise,,,event management,,,,,,Commodity,Buy,Decommissioned,,,,2022-Jul-14,2020-Nov-06,SM-100650,SM-100650,Technology System,,"

Set to retired (14/7 2022) after decommissioning during FY22 - MP",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114164
114166,BNA Brocade Network Advisor,BNA Brocade Network Advisor,BNANET,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,,,,https://confluence.build.ingka.ikea.com/x/4K4qBg,,"

Management software for providing business applications and platforms with block storage media.","

Management software for the central SAN environment. The software has over lcm cycles been re-labelled to SANNAV.","

dcp&nbsp;

Change this entry to SANNAV",,,,,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100651,SM-100651,Technology System,,,,Inter user should not have EM role on Ingka owned system,Setting System Master Identifier reference for Technology System,2024-Jan-17 15:32,2020-Nov-06 15:30,SMC-114166
522354,Brandwatch data product,Brandwatch data product,BRANDWDP,Active,Circular Ventures,Circular Sustainability,,Diederik Lemkes,Wieke de Wit,,Wieke de Wit,,,"

Data product for the Brandwatch system.

Brandwatch is the tool that is used to track mentions and sentiment on social media. The 4P dashboard is one example of this. For some Circular KPI's we also want to work with the perception ""P"" which means we're interested in mentions and sentiment around the circularity theme.

The goal of this dataset is to provide an interface to Brandwatch:from BigQuery such that other systems (e.g. Power BI) can connect directly to aggregated Brandwatch data.without having a Brandwatch account

In Circular we have several Power BI dashboards in which we want to show perception around circular services over time. Brandwatch can provide us with that data. The only way to get that inside Power BI before was using manual exports. By having this dataset on aggregated data, we can skip the manual exports.

Read more: https://confluence.build.ingka.ikea.com/x/tLsGG","

Please refer to the data catalog.",,,,,,,,,,,,,,,,,,2022-Oct-27,SM-116407,SM-116407,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Oct-27 14:55,SMC-522354
372860,BrightVerify,BrightVerify,BVERIFY,Planned,USA,Data and Integration,,,,,,,,"

To comply with US Law for Safer Homes compliance, we must verify a customer email address is a valid emailbox capable of receinv mail w/o NDR.","

BrightVerify is a real-time API that checks DNS and Email Server responses for a given email address. It responds with codes that can be returned as user feedback.

Example. The customer enters their email address in the Chest of Drawer Acknowledgement form. If the email account is invalid, locked out, or disabled, the form will provide feedback ""The email address you entered is not valid. Please enter a valid email address""",,,,,local service (USA),,None,Stand alone,Customer Meeting Point Web,,,,Invest,,,,,2022-Apr-06,SM-116181,SM-116181,Technology System,,Created by script using data in Nursery,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2024-Feb-02 17:00,2022-Apr-06 20:23,SMC-372860
353301,Browser Stack,BrowserStack,BROWSERSTK,Retired,Engineering Services,Developer Enablement,,Fredrik Folkeryd,Ferenc Horvath,,,,,"

BrowserStack is a cloud solution for managing web and mobile testing, part of the offering from Test Enablement within Engineering Services in Technology Services.",,,,,Test Enablement,Test Platform,,,,,,,,,,,,2023-May-03,2022-Feb-23,SM-105301,SM-105301,Technology System,,"

Created by script using data in Nursery",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Feb-23 17:14,SMC-353301
614608,Business Data Backbone,Business Data Backbone,BDB,Active,IKEA for Business,Business to Business,,Álvaro Hernández,Álvaro Hernández,,Matias Luis Avila,,,"

Business Data Backbone is a Gateway API layer aimed to expose in a clear way all the business customer Data information. It includes the full profile informaiton coming from the Business Customer Master (BCM) Database, the set of transactions and other informations that are relevant.&nbsp;","

The Business Customer BackBone (BDB) is a set of API gateways to access all the business customer profile Data. It has all the information aggregated and exposed via a single REST API architecture that can be graphically seen here.",,,Digital Product,,,B2B Assisted Shopping||Team Mangfaldas Engineering,,,IKEA for Business,,,,,,,,,2023-May-24,SM-116687,SM-116687,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2828,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-May-24 08:36,SMC-614608
181627,Camunda,Camunda,CAMUNDA,Active,Digital Workplace,End User Enablement and Productivity,,Dino Omerhodzic,Dino Omerhodzic,,,https://confluence.build.ingka.ikea.com/x/KHDLDg,,"

Camunda&nbsp;is a&nbsp;workflow and decision automation platform. Camunda ships with tools for creating workflow and decision models, operating deployed models in production, and allowing users to execute workflow tasks assigned to them.&nbsp;

Camunda enables designing, automating, and improving&nbsp;all components of the entire business process end-to-end – across different technologies, systems, infrastructures, people, and devices.","A complete process automation tech stack with powerful execution engines for BPMN workflows and DMN decisions paired with essential applications for modeling, operations and analytics.&nbsp;Typical use cases for Camunda can be microservices orchestration and human task management.&nbsp;Camunda&nbsp;is a lightweight, Java-based framework. It can be used as a standalone process engine server or embedded inside custom Java applications. It offers non-Java developers a REST API and dedicated client libraries to build applications connecting to a remote workflow engine.",,On-Premise,,,,,,,,Minor,Commodity,Buy,Invest,2023-Aug-22,,,,2021-Oct-05,SM-105127,SM-105127,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-19 15:20,2021-Oct-05 17:54,SMC-181627
114169,CAPE PACK,CAPE PACK,CAPEPACK,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,Pallet Optimization tool,"Need to find correct placement, meetin with K-Ã Hofer planned",,On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100653,SM-100653,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114169
181639,Catchpoint,Catchpoint,CATCHPOINT,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Christian Kullendorff,Johan Jacobsson,,,https://confluence.build.ingka.ikea.com/display/ITOI/Operational+Intelligence,,"

Synthetic monitoring that pinpoints performance, availability and users experience","

Catchpoint offers a number of different test types from their globally distributed nodes. At the time IKEA are primarily using the Transaction (real browser based) and API tests. Recently Catchpoint announced more network related test types that can help clarify the network related impact on the performance experiencedThe script language used in the Catchpoint tests are Selenium or JavaScript based","

opi",SaaS,Platform,,service monitoring,,,,,Partly,Differentiating,Buy,Invest,,,,,2021-Oct-06,SM-105129,SM-105129,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Oct-06 08:17,SMC-181639
114171,CCTV,CCTV,CCTV,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Annika Ederfors Aronsson,,,,https://confluence.build.ingka.ikea.com/display/COMPSERVIC/Tools+and+Solutions+IKEA+IT+DC,,"

Provides Video surveillance for Central Data Centers","

Provides Video surveillance for Central Data Centers based on technologies from Axis Communications and XProtect&nbsp;","

dcp",On-Premise,,,facility data centre,Facility Standards,,,,Strongly,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100655,SM-100655,Technology System,,"

Martin PerssonAdded",,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114171
116101,CDC-Qlik Replicate,Change Data Capture - Qlik Replicate,CDCQR,Active,Data Integration and Middleware,Data and Integration,,Siddharth Gupta,Siddharth Gupta,,,https://confluence.build.ingka.ikea.com/display/CDCQR,,"

Data replication is a part of the modernization of the Ingka technology landscape, from on-prem applications to a cloud landscape. CDC-tooling like Qlik replicate will be used primarily for modernization, from on-premise sources to cloud targets, one-direction; other cases are exceptions that should be evaluated case-by-case.

CDC technology and Qlik replicate is a important cornerstone&nbsp; on the way in a challenging modernization process, where we need to make business critical data available in the cloud as soon as possible, when these data sources have not been fully modernized into the cloud.","

Qlik Replicate aka Attunity",,,,Analytics & Insights,cdc replicate integration platform,Change Data Capture Replication Services,None,,,,Commodity,Buy,Tolerate,,,,,2020-Dec-15,SM-101026,SM-101026,Technology System,,,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Dec-15 17:19,SMC-116101
667512,CDI Entry Point,CDI Entry Point,CDIEP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Bonny Lindberg,Tobias Berg,,,,,"

Common landing area for Core Digital Infrastructure Content","

Web-page for displaying and elaborating content for Core Digital Infrastructure service portfolio",,Public Cloud,Platform,,,,,,,None,Commodity,Make,Tolerate,,,,,2023-Oct-12,SM-116827,SM-116827,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4088,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Oct-12 15:28,SMC-667512
114167,CDS Admin,Corporate Directory Services Admin,CDS,Active,Identity and Access Management,Identity and Access Management,,Joakim Prahl,Joakim Prahl,,,https://cdsweb.apps.ikea.com,https://cdsweb.apps.ikea.com,"

Corporate Directory Service&nbsp;",,,On-Premise,Platform,,Identity & access management,,,,,Partly,Differentiating,Make,Migrate,,,,,2020-Nov-06,SM-100656,SM-100656,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-04 08:48,2020-Nov-06 15:30,SMC-114167
523618,Celonis EMS,Celonis,CELONIS,Active,Digital Workplace,End User Enablement and Productivity,Tim Hills,Tim Hills,Dino Omerhodzic,,,https://ikea.eu-5.celonis.cloud/package-manager/ui/views/ui/spaces,https://ikea.eu-5.celonis.cloud/package-manager/ui/views/ui/spaces,Process Mining tool to reveal the reality of our operation through visualising the connected steps of our processes,An event processing SaaS service aimed at visualising what happens in our opertation,Celonis EMS,SaaS,,,,,,,,Minor,Commodity,Buy,Invest,2023-Aug-22,,,,2022-Nov-09,SM-116431,SM-116431,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-05 08:49,2022-Nov-09 15:14,SMC-523618
512548,Central Block Storage Platform,Central Block Storage Platform,DCBLKSTO,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,David Johansson,David Johansson,,,https://confluence.build.ingka.ikea.com/x/QnOYK,,"For mission-critical operation that require highest reliability. 

Block storage is the most commonly used storage type for most applications.  

This is a technology that is used to store data files on storage area networks (SANs). Developers favor block storage for computing situations where they require fast, efficient, and reliable data transportation.","Block Storage Service in Data Center with focus on performance and availability.

Block storage is technology that controls data storage and storage devices. It takes any data, like a file or database entry, and divides it into blocks of equal sizes. The block storage system then stores the data block on underlying physical storage in a manner that is optimized for fast access and retrieval.

SAN (Fiber Channel Fabric) used as the backbone for block storage service with high performance and low latency. Available only in Warehouse Technical Platform","

dcp",,Platform,,,,,,,,,,,,,,,2022-Oct-04,SM-116369,SM-116369,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-26 09:05,2022-Oct-04 14:31,SMC-512548
114181,Central SAN Monitoring,Central SAN Monitoring,CSANMON,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,,,,https://confluence.build.ingka.ikea.com/x/4K4qBg,,"

DC&amp;P Storage and Data Protection engineering team","

Software for managing the central storage area network infrastructure.","

dcp",Central Private Hosting,Platform,,monitoring,,,,,None,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100666,SM-100666,Technology System,,,,Inter user should not have EM role on Ingka owned system,Setting System Master Identifier reference for Technology System,2024-Jan-17 15:33,2020-Nov-06 15:30,SMC-114181
622894,CERTPKI,Certificates and Public Key Infrastructure (PKI) Services,CERTPKI,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Michael Keane,Fredrik Åhlstedt,,,https://confluence.build.ingka.ikea.com/display/CYBERSEC/PKI+-+Certificates+Public+Key+Infrastructure+Home,,"

Public Key Infrastructure (PKI) governs the issuance of digital certificates to protect sensitive data, provide unique digital identities for users, devices and applications and secure end-to-end communications.","

Certificate Issuance",https://confluence.build.ingka.ikea.com/display/CERTPKI/KeyFactor+Documentation,Public Cloud||On-Premise,Platform,,certpki,,,,,Strongly,,Buy,Invest,,,,,2023-Jun-09,SM-116732,SM-116732,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3043,Setting System Master Identifier reference for Technology System,2024-Jan-10 22:18,2023-Jun-09 08:53,SMC-622894
608105,Charge Point Electric Car Chargers,Charge Point Electric Car Chargers,CHARGPOINT,Active,Canada,Circular Sustainability,Cynthia Chau,Brian Berneker,Brian Berneker,,Guido DiCesare,https://jira.digital.ingka.com/servicedesk/customer/portal/60/LNM-3266,,"

Allow our customer with electric vehicles to charge their cars.
Fulfillment Service Providers (Trucks)
Co-workers with electric vehicles

Application tracks usage and demand.
Application can be programmed to schedule to control access.
Application can control user access.
Application can be used for payment.
Can track C02 emissions.

Open Charge Point Protocol (OCPP)","

Physical car chargers in the IKEA parking lots.&nbsp;","

Charge Point Electric Car Chargers",SaaS,Platform,,,,Co-worker||Customer,Stand alone,Customer Fulfilment Insights,Minor,Innovating,Buy,Invest,,,,,2023-Apr-19,SM-116646,SM-116646,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-19 21:34,SMC-608105
114390,Chatbot,Chatbot,CHATBOT,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100984,SM-100984,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114390
114170,Chef,Chef,CHEF,Retired,Engineering Services,Developer Enablement,,,,,,,,Infrastructure Automation Platform,,,On-Premise,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Nov-09,2020-Nov-06,SM-100658,SM-100658,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114170
181851,China AI Design Recommendation,China AI Design Recommendation,AIDRECOM,Active,China,Content and Inspiration,,Aaron Zuo,Danny Xu,Serene Chu,Bella Xu,https://confluence.build.ingka.ikea.com/display/AIDRECOM/AI+Design+Home+Furnishing+Recommendation+Home,,"

A foundation to support AI Design related recommendation features","It contains product configuration, combination generator and recommendation applications with machine learning embed",AI Design Engine,Public Cloud,Digital Product,,,PAX,Co-worker,Web browser,Customer Engagement,Strongly,Differentiating,Make,Tolerate,,,,,2021-Oct-12,SM-105146,SM-105146,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-10 03:50,2021-Oct-12 11:26,SMC-181851
181780,China DAP,China Data Analytics Platform,CNDAP,Active,China,Data and Integration,,Tiger Wang,Ewen Guo,,Michael Zhang,https://confluence.build.ingka.ikea.com/display/CNARCH/Data+and+Analytic+Platform,,"

As a big part of IKEA China digitalization, Data and Analytic Platform is the centralized Data platform to bulk ingest, store, integrate and process data.

And also, it provide big data capability and Analytic capability to whole IKEA China.","

Build on Alicloud big data eco system, using dataphin as the big data process platform.","

DAP",Public Cloud,Platform,Analytics & Insights,,DAP Data Management Project,Co-worker,Web browser,Data Integration and Middleware,Partly,Commodity,Make,Tolerate,,,,,2021-Oct-11,SM-105135,SM-105135,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Oct-11 14:07,SMC-181780
610794,China Engineering Platform,China Engineering Platform,CNEP,Active,China,Developer Enablement,,Sara Xie,Michael Huang,,,https://confluence.build.ingka.ikea.com/display/CDEP/China+Digital+Engineering+Platform+Home,,"

We create a smooth experience for developers, to improve the effectiveness of end-to-end delivery of features to customers","

Platform team provides below listed capabilities through self-service APIs, tools, services, knowledge and support, which are arranged as a compelling internal product. Autonomous delivery teams can make use of the platform to deliver product features at a higher pace, with reduced duplicate efforts .&nbsp;Pipeline - CI/CD solutionsGithub - official Principles and Process of Source Code ManagementBranch Management -Consolidated branching strategy in China Digital HubGithub Actions - Run Continuous Integration on Self-host Github actions runnerJFrog Artifactory - Centralized artifacts management for all China Digital Hub productsOfficial Docker base Images - Enforce Security Best PracticesOrchestration - Kubernetes EcosystemsKong - is a lightweight, fast, and flexible cloud-native API gateway. An API gateway is a reverse proxy that lets you manage, configure, and route requests to your APIs. We use different Kong instance for different scenarios.Istio - is the service mesh solution enables us to secure, connect, and monitor microservices, so they can modernize their enterprise apps more swiftly and securely. Istio manages traffic flows between services, enforces access policies, and aggregates telemetry data, all without requiring changes to application code.Rancher - unites them with centralized authentication, access control and observability.Runtime - Application runtime dependenciesMonitoring and Observability - Everything about application monitoring and loggingTechnical Spec - Standardised engineering team technical specifications",,Public Cloud,Platform,,,CN Engineering Platform,Co-worker,,Engineering Services,Partly,Commodity,Buy||Make,Tolerate,,,,,2023-May-05,SM-116665,SM-116665,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2626,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-May-05 11:25,SMC-610794
681320,China Sales Item,China Sales Item,CNSALESITM,Active,China,Retail Range,,Leo Yu,Leo Yu,,,https://confluence.build.ingka.ikea.com/display/ECOMMOP/Sales+Item+Overview,,"

Sales item provides sales items information storage and query functions. Its goal is to provide sales item information data to other local systems.

&nbsp;","

The main data source of sales item is the selling range API, which defines the synchronization and stores the full amount of data in the database.

Sales item provides multiple query interfaces, which are applicable to different scenarios.",,Public Cloud,Digital Product,,,CN App,Customer,,Data Integration and Middleware,Partly,Commodity,Make,Tolerate,,,,,2023-Nov-28,SM-116881,SM-116881,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4599,,2023-Nov-28 09:37,2023-Nov-28 08:27,SMC-681320
634585,China Search,China Search,CNSEARCH,Active,China,Customer Meeting Point,,Ken Huang,Kyle Li,Jesse Bi,,https://confluence.build.ingka.ikea.com/display/ECOMMOP/Search,,"

As Chinese is different with English, we need a search solution that suits the search needs of Chinese customers. And it is also hard to transmit Chinese customers’ online behavior out of China in compliance perspective, so we need a localized search algorithm to rank search result according to Chinese customers’ online behavior.","

CN Search includes core search service, data collect and cleansing service, &nbsp;nlp service, algorithm service(GPU) , ranking service and portal tools.&nbsp;

Provide search capacity include keyword search, catalog search and &nbsp;promotion activity search for end user. &nbsp;

Support co-worker interfere search results with biz rules.&nbsp;",,Public Cloud,Digital Product,,,CN Search,Customer,Mobile App,Customer Meeting Point App,Strongly,Differentiating,Make,Invest,,,,,2023-Jul-07,SM-116769,SM-116769,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3321,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jul-07 09:07,SMC-634585
566107,Chrome Kiosk,Chrome Kiosk,CHRMKSK,Active,Digital Workplace,End User Enablement and Productivity,,Lukasz Sudak,Lukasz Sudak,,,https://devices.ingka.com/index.php/chrome-kiosk/,,"

The self-service kiosk definition is a small, self-standing structure, used to display information or facilitate an action. The self-service kiosk meaning will be different for everyone, depending on the specific planned application.","

Chrome Kiosk is based on ChromeOS kiosk platform from Google admin portal:
&nbsp;


The self-service kiosk definition is a small, self-standing structure, used to display information or facilitate an action. The self-service kiosk meaning will be different for everyone, depending on the specific planned application.",,SaaS,,,,Frontline Workplace,,,,Partly,Commodity,Buy,Invest,2023-Aug-22,,,,2022-Dec-20,SM-116480,SM-116480,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:08,2022-Dec-20 07:28,SMC-566107
114168,ClearCase,IBM ClearCase,CCASE,Retired,Engineering Services,Developer Enablement,,,,,,,,"

Version control",,,On-Premise,,,development workbench,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Nov-15,2020-Nov-06,SM-100654,SM-100654,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114168
349211,Clearpass,Clearpass,CPAS,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Christian Winberg,,,,,Provides Network Access Control Functionality,Provides Network Access Control Functionality for users connecting to distributed cabeled and Wi-Fi infraxtructure on distributed sites,"

cnn&nbsp;",On-Premise,Platform,,,,,,,Partly,Differentiating,Make,Invest,,,,,2022-Feb-07,SM-105270,SM-105270,Technology System,,"

Created by script using data in Nursery",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2024-Jan-08 08:58,2022-Feb-07 16:26,SMC-349211
114175,ClearQuest,IBM ClearQuest,CLEARQ,Retired,Engineering Services,Developer Enablement,,,,,,,,Issue tracking,,,On-Premise,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Nov-15,2020-Nov-06,SM-100659,SM-100659,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114175
573313,Cleveron,Cleveron Lockers,CLVRN,Active,Delivery and Services,Delivery and Service Management,Per Christoffersson,Aditya Kuppireddy,Diego Lopez Osuna,Leo De Souza,Rui Nunes Martins Costa,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=590433584,,"

Cleveron is a software solution required to operate Cleveron pickup lockers. IKEA has selected Cleveron as a global supplier and full service partner for its internal pickup lockers placed at IKEA locations (Store, CDC, etc). Cleveron is a Estonia based pickup lockers manufacturer, they have now been contracted by INGKA as Internal Store pickup lockers hardware and software development, installation, training and maintenance partner for INGKA markets.

&nbsp;","

Cleveron is a software solution required to operate Cleveron pickup lockers.",,SaaS,Digital Product,,Pickup Point Manager,Fulfillment DSM Available to Offer,,,,Strongly,,,,,,,,2023-Jan-31,SM-116529,SM-116529,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jan-31 15:37,SMC-573313
509547,Cloud Portal,Ingka Cloud Portal,CLOUDPORT,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Tobias Berg,Tobias Berg,,,https://cloud.ingka.com,,"

The starting point for Ingka Cloud Journeys, account creation and follow-up for our chosen cloud providers.","

The entry point and also the hub for financial distribution and reporting of our cloud consumption.&nbsp;","

cloud portal",,Platform,,,,,,,,,,,,,,,2022-Sep-26,SM-116356,SM-116356,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Sep-26 14:11,SMC-509547
152717,Cloud Print,Workplace Cloud Print,CWEPRINT,Active,Digital Workplace,End User Enablement and Productivity,,,Kristina Weberg,,,https://confluence.build.ingka.ikea.com/x/SvW1E,,"

Workplace Cloud Print - product allows to print from any kind of IKEA client, like ICC computer, Light Managed Device, mobile device (e.g. phone, tablet) no matter if that device is connect over IKEA network or the Internet. Printing and scanning will be done in a secure way requiring users to authenticate with IKEA account.

&nbsp;","

Workplace Cloud Print technical solution can be hosted both on Cloud and on prem, regarding the need.&nbsp;

Solution consists at the moment of these two machines (in the future there will be more) which serve as central printer queue definition and driver database. Client computers will have a piece of software installed, so called agents, which will pick queue definitions and drivers from the central server and will create local queues on them.

There will also be a Pull Print (known also as Follow-Me Print or Secure Print) functionality. Print jobs sent as Pull Print jobs will be stored on the central server until a user authenticates at a printer and chooses the job to print.

Solution will also be used for scanning to mail, Sharepoint, OneDrive or perhaps some other solution.

Vendor for the solution is LRS (https://www.lrs.com/) and the products name is VPSX (which consist of various subcomponents) -&nbsp; https://www.lrsoutputmanagement.com/products/

&nbsp;","

cwe cloud print",SaaS||On-Premise,Digital Product,,,,,,,Minor,Commodity,Buy,Invest,,,,,2021-Jul-02,SM-105053,SM-105053,Technology System,,,,Moved from Digital Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Jul-02 17:25,SMC-152717
674887,Cloud Security Infra,Cloud Security Infra,CLSDINFRA,Active,Cyber Security,Cyber Security,,Daniel Ehn,Fredrik Åhlstedt,,,,,"

System used to group cloud assets and accounts belonging to cloud security team","

System used to group cloud assets and accounts belonging to cloud security team","

cDinkar Notes - Conditional Approved to be added as system due to limitation/structure for cloud project governance which needs SMC ID. Urgently its needed but together with Daniel its decided we need to revisit the structure to ensure SMC only captures what falls in system boundaries.",,Digital Product,,,Cloud Protection,,,,,,,,,,,,2023-Nov-08,SM-116856,SM-116856,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4317,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Nov-08 11:04,SMC-674887
370165,CloudVision,CloudVision,CLOUDVSN,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Johan Nordbeck,,,https://confluence.build.ingka.ikea.com/x/Ynk6CQ,,"

The business contribution of Cloud Vision is to support, design and manage the data center network fabric. This allowing any service to consume connectivity from the central data centers.","

CloudVision is a platform for network-wide workload orchestration and work flow automation. Designed to complement SDN (virtualization) to control and orchestrate virtual network overlays, this by focusing on work flow visibility, automation tasks, and initial or ongoing network provisioning across the underlying physical network.","

cnn Arista",Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Invest,,,,,2022-Mar-28,SM-116165,SM-116165,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Mar-28 17:37,SMC-370165
181879,CN CIAM,China Customer Identity & Access Management,CNCIAM,Active,China,Customer Information Management,,Eric Zheng,Eric Zheng,,,https://confluence.build.ingka.ikea.com/display/IKEAP/IKEA+One+ID,https://admin.ingka-internal.cn/app/customer/home,"

Provide IKEA CN customer identity service","

Basing on Alicloud, allow user create new account or migrate from legacy system if there's old accounts","

China Customer Identity &amp; Access Management",Public Cloud,Platform,Identity & Access Management,ciam (local china),,Customer,Mobile App,Customer Engagement,Strongly,Commodity,Make,Tolerate,,,,,2021-Oct-13,SM-105150,SM-105150,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Oct-13 11:19,SMC-181879
509550,CNI Cilium,CNI Cilium,CNI,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Ola Olsson,Ola Olsson,,,,,"

Network component in for providing Kubernetes based hosting services&nbsp;","

Cilium is an open source software for providing, securing and observing network connectivity between container workloads using the Kernel technology eBPF.

Being used in the Nebula Platform Construct","

Cilium",,,,,,,,,,,,,,,,,2022-Sep-26,SM-116357,SM-116357,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Sep-26 15:10,SMC-509550
668302,CODC,Cognitive Operational Data Center,CODC,Active,Selling,Sales,,Adam Prescott,Annika Nordberg,,,,,"

Test automation tool for iSell - An application that is used within iSell project , where it stores all the data of automation test execution.","

All the technical details can be found on the below confluence link

https://confluence.build.ingka.ikea.com/pages/viewpage.action?spaceKey=ISELL&amp;title=CODC+-+Cognitive+Operational+data+Center",,,,,,iSell Test Automation (ISELLAUTO),,,,,,,,,,,,2023-Oct-16,SM-116831,SM-116831,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3946,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Oct-16 11:54,SMC-668302
474795,CommonSalesRU,Common_sales_RU,CMNSLSRU,Retired,Russia,Sales,,Evgeny Alekseev,,,,,,"

Data about sales to provide analytics reports","

GCP common_sales - Azure server - PowerBI

[RUCDP-340] Common Sales RU - Jira Digital (ingka.com)",Common Sales RU PowerBI reporting,,Digital Product,,,,,Web browser,Customer Fulfilment Insights,,,,Decommissioned,,,,2023-May-15,2022-Jul-01,SM-116244,SM-116244,Technology System,,Created by script using data in Nursery,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Jul-01 08:19,SMC-474795
114173,Component One FlexGrid,Component One FlexGrid,CONEFLEX,Active,Engineering Services,Developer Enablement,,Jan Magnusson,Niclas Strandéus,,,,,,,,Central Private Hosting,,,,,Co-worker,,,None,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100662,SM-100662,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-21 16:41,2020-Nov-06 15:30,SMC-114173
114388,Confluence,Attlassian Confluence,CONFLU,Active,Engineering Services,Developer Enablement,Christofer Anderberg,Sajid Chaudhari,Kenneth Flatby,,,https://confluence.build.ingka.ikea.com/,,"Confluence is content collaboration software.

Users can create pages and blogs which can be commented on and edited by all members of the team.","Confluence is part of Atlassian platform eco system.
Ingka is using DataCenter version of Confluence.
Confluence is hosted and maintained in AWS by the Atlassian Premium Partner Stretch Addera.
Confluence has its own license tier solution.",,Public Cloud,,Planning & Collaboration,collaboration services,,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100990,SM-100990,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Dec-21 17:11,2020-Nov-06 15:30,SMC-114388
564976,CONTEXT,Context Observability Platform,CONTEXT,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,Niclas Nilsson,Javier Ubillos,Niclas Nilsson,,,https://allen.ingka.com/docs/default/Component/context,,"

CONTEXT will provide an observability platform that provides increased reliability of digital products, assure low MTTA (Mean Time To Acknowledge) and MTTR (Mean Time to Repair) to reduce customer impact when outages happen, in real time understand how critical flows (customer journeys) perform towards customers and co-workers and use that data for continuously improve the experience","

CONTEXT is a platform based on open source observability products. Initially we have:&nbsp; Loki will provide a store for Logs. Tempo will provide a store for Traces. Mimir will provide a store for Metrics.&nbsp; Grafana will be the product to join data sources in one location to get insights. These will be modular and decoupled and you can choose to either onboard on the distributed central Grafana (as code) or spin up your own and connect to the datasources you need.","

Previously named KONTEXT",,,,,OI Next-Gen,,,,,,,,,,,,2022-Dec-13,SM-116470,SM-116470,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2024-Jan-24 22:30,2022-Dec-13 12:29,SMC-564976
672796,Controlup,Controlup,CONTROLUP,Active,Digital Workplace,End User Enablement and Productivity,Henrik Johansson,John Kalkbrenner,Kristina Weberg,,,,,"

ControlUp’s management and analytics platform enable administrators to monitor, troubleshoot, analyze, and directly remediate issues in real-time in their&nbsp; infrastructure using a simple interface.

With ControlUp, administrators can quickly spot performance issues across multiple layers – from hypervisors and datastores to Citrix Netscaler appliances, delivery brokers, VMs, and VDAs, all the way to specific user sessions and processes. ControlUp monitors and troubleshoots all Citrix Virtual Apps and Desktops versions, even multiple farms, enabling a streamlined management experience in a non-homogeneous environment.","

ControlUp Real-Time DX collects metrics in real time, so you can identify, troubleshoot, and remediate problems before they negatively affect your employees’ digital experience and productivity.

ControlUp Automation changes the game for Citrix administrators, enabling them to automate script actions that dynamically optimize end users’ experience, maximize computing resources, and reduce troubleshooting time. Using Automation, administrators can also automatically remediate recurring issues to prevent them from happening ever again.

ControlUp Remote DX collects performance metrics from off-the-network client devices running Windows, macOS or IGEL OS, without the need for line-of-sight to the ControlUp Real-Time DX infrastructure in your datacenter.

&nbsp;

Controlup is used to monitor the VIAD/IWTS and VDI deliver services.",,,Digital Product,,viad (virtual applications and desktops),,,,Digital Workplace,,,,,,,,,2023-Oct-23,SM-116843,SM-116843,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4095,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Oct-23 11:17,SMC-672796
494351,Copado,Copado,COPADO,Active,Engineering Services,Developer Enablement,,,,,,,,Copado is a DevOps tool for Salesforce,"

Provides CI/CD, DevOps, Code Quallity, Testing, etc.",,SaaS,,,,,None,Stand alone,Engineering Services,None,Commodity,Buy,Invest,,,,,2022-Aug-10,SM-116310,SM-116310,Technology System,,"

Created by script using data in Nursery",,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2024-Feb-02 17:01,2022-Aug-10 16:35,SMC-494351
114394,Coverity,Synopsis Coverity,COVERITY,Retired,Cyber Security,Developer Enablement,,,,,,,,Provides Static Code Analysis and quality checks. Used in delivery pipelines on source code to find vulnerabilities and other code related vulnerabilities,,,Public Cloud,,,it security,,,,,Partly,Commodity,Buy,Decommissioned,,,,2023-May-30,2020-Nov-06,SM-100991,SM-100991,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114394
607155,Coworker Platform,Coworker Platform,COWORKRPF,Planned,Store Co-Worker Experience,Store Operations and Experience,Carina Isberg,Kyle Hopkins,Carina Isberg,Dimo Mouzouris,,https://confluence.build.ingka.ikea.com/display/IDE/Store+Co-worker+Experience,,"

Coworker Experience Platform will support the frontline coworker with daily operations in a store.

Objective is to be the&nbsp;digital merchandising and operations tool for store co-workers, replacing pen and paper/excel/hacks, to help them keep the store in shape as new and ready for customers.

Interactions between coworker to coworker should be easy and simplified.

Interaction between coworker and customer should be simplified.
https://confluence.build.ingka.ikea.com/display/IDE/Store+Co-worker+Experience","

This is going to be a webapp and/or mobile app which will run on Honeywell CT30.

This app will be for frontline coworker to easily interact with other coworkers, perform day to day task, and help customers along the way.

This app will utilize the location, bluetooth.","

Domain: Physical Meeting Point

SubDomain: Coworker Platform",Public Cloud,Digital Product,,,,,,,,,,,,,,,2023-Apr-14,SM-116635,SM-116635,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-14 08:33,SMC-607155
114393,CRIPF,Critical Product Information Flow,CRIPF,Active,Service Management and Operations,Retail Range,,Pramod Kompella,Pramod Kompella,,,https://confluence.build.ingka.ikea.com/display/CRIPFSD/CRIPF+BA%27s+Solution+Design,,"

Tools for improving showability and buyability on the web through monitoring all components in the Critical Product Information Flow","

Parts on-prem (OpenShift) and part GCP",,On-Premise,,Analytics & Insights,cripf tools (critical product information flow),,,,,Minor,Commodity,Make,Invest,,,,,2020-Nov-06,SM-100992,SM-100992,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114393
519173,Critical Situation Hub,Critical Situation Hub,CSH,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,Per-Ola Eliasson,Karan Honavar,Karan Honavar,,,https://iobserve.oi.ingka.com/CSH,,"

CSH is a inhouse build solution which would enable &nbsp;Digital Resilience to prevent minimizing customer harm, reputational damage, and financial loss .&nbsp;
CSH also act as a Single glass pane view of all the supporting insights (Splunk /GitHub/NowIT&nbsp; about the business outages adding to the above CSH boost MTTR &amp; Auto audit and documentation of war room","

The cloud-based application helps to trace, track, and manage business outages and predict the outage of other critical services related to it with the help of artificial intelligence",,Public Cloud,Digital Product,,,,,,,Partly,Differentiating,Make,Invest,,,,,2022-Oct-26,SM-116404,SM-116404,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Oct-26 16:23,SMC-519173
114180,Crystal Reports Pro for ClearQuest,Crystal Reports Pro for ClearQuest,CRYREP,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,On-Premise,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Nov-15,2020-Nov-06,SM-100665,SM-100665,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114180
114392,CSA Monitoring,CSA Monitoring,CSASCR,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Fredrik Åhlstedt,Fredrik Åhlstedt,,,,,"

Monitoring tool used from CySec P&amp;P&nbsp;","

Monitoring tool used from CySec P&amp;P&nbsp;","

Monitoring tool used from CySec P&amp;P&nbsp;",On-Premise,Digital Product,,,,,,,Strongly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100993,SM-100993,Technology System,,"

Martin PerssonAdded owner",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114392
389606,CSM,Security Manager,SMGR,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Venkatesh Narasingam Kuppusamy,,,,,"

Being able to manage the firewall estate (Cisco) with features like:enable reuse of security rules and objectsManage process compliance and error-free deploymentsImproves ability to monitor security threats","

The Cisco Security Manager is the focal point for managing and handling security capabilities related to Cisco estate in the Data Center.","

CSM",,,,,,,,,,,,,,,,,2022-May-09,SM-116202,SM-116202,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-May-09 11:19,SMC-389606
316105,Customer Order Storage Self Serve Trolley,Customer Order Storage Self Serve Trolley based solution,COSST,Active,Delivery and Services,Delivery and Service Management,Per Christoffersson,Aditya Kuppireddy,Diego Lopez Osuna,Leo De Souza,Rui Nunes Martins Costa,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=590433595,,"The Customer Order Store Solution (COSS-T) with self-serve or assisted handout is a solution that secures the extra capacity for customer meeting points need to store these orders. It increases capacity by providing storage for orders on furniture trolleys on multiple levels. IKEA has partnered with UK based Tornado Storage Solutions to develop an Trolley based automated customer order storage solution (COSS-T). The ambition is to develop this as a standard solution that can be rolled out across multiple INGKA markets. Tornado Storage Solutions is a supplier based out of UK, which produces hardware for storing parcels. This tool is used by the co-worker to create new orders, review the status of each order, send text messages to customers, take the order to completion (i.e mark it delivered/cancelled). If you are further interested to know about the supplier, you can visit their website http://tornadostorage.com/","This tool is used by co-workers to create new orders, review the status of each order, send text messages to customers, and take the order to completion (i.e mark it delivered/canceled). For the moment, we are pushing orders to Tornado and receiving order updates",,SaaS,Digital Product,,Pickup Point Manager,,,,Delivery and Services,Strongly,Commodity,Buy,Invest,,,,,2021-Nov-03,SM-105170,SM-105170,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Nov-03 13:28,SMC-316105
475964,Cw Design Library,Co-worker Design Library,CDL,Planned,Delivery and Services,Delivery and Service Management,Nikki Blaze,Jelena Sokolic,,Leo De Souza,,https://kaskad.ingka.com/co-worker-design-library/first-page,,"

Design source of truth&nbsp;where product teams find visual components and guidance to build applications for&nbsp;ILO&amp;FF Co-workers.

It is&nbsp;neither a new Skapa nor a Co-worker library&nbsp;.

It is being&nbsp;build on the top of Skapa and the Co-worker library&nbsp;to fulfil the needs of ILO&amp;FF Co-workers applications.

&nbsp;","

Portal to keep the library of the design components. A Storybook,&nbsp;descriptive&nbsp;website&nbsp;with Angular and React components.","

storybook design library

&nbsp;

&nbsp;

&nbsp;",,Digital Product,,,,,,,,,,,,,,,2022-Jul-05,SM-116270,SM-116270,Technology System,,Created by script using data in Nursery,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2024-Feb-02 16:59,2022-Jul-05 14:10,SMC-475964
611774,Cyber Security Portal,Cyber Security Portal,CSP,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Björn Gullstrand,Gustav Lundsgård,,,https://cybersec.ingka.com/,,"

The Cyber Security Portal houses links to all the services Cyber Security offers.&nbsp;

It is also the portal to onboard to Vuln Scan.&nbsp;","

Node.js and React project hosted in GCP.&nbsp;",,Public Cloud,Digital Product,,,Cyber Sec Portal,,,,Minor,Innovating,Make,Invest,,,,,2023-May-11,SM-116672,SM-116672,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2595,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-May-11 08:41,SMC-611774
114156,Dashboards,Availability Dashboards,AVDASH,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Christian Kullendorff,Johan Jacobsson,,,,,Dashboarding solution consolidating the Avilability measurements and other dedicated dashboards for different teams,Interlink,,SaaS,Platform,Analytics & Insights,monitoring,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100644,SM-100644,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114156
114178,Data Center Connectivity,Data Center Connectivity,CNCONN,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,,,Network - Central Network are responsibly for life-cycle-management operations and support for Central Local Area Network,"Cisco Nexus, Cisco Catalysts
Cisco Prime, Cisco DCNM","

cnn",On-Premise,,,,,,,,,,Buy,Decommissioned,,,,2023-Feb-27,2020-Nov-06,SM-100661,SM-100661,Technology System,,"

Retired, as the individual components are added.",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114178
114184,Data Center Load Balancing,Data Center Load Balancing,DCLOADBA,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Johan Nordbeck,Johan Nordbeck,,,,,Used for data center load balancing and reverse proxy functionality,"

Platform based on F5 Big IP software","

cnn",Central Private Hosting,Platform,,,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100668,SM-100668,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114184
588683,Data Center Network Fabric,Data Center Network Fabric,DCNF,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Johan Nordbeck,,,,,"

The network fabric in the data centers delivers the hardware and software&nbsp;following benefits:

It abstracts base physical topology to create virtualized layers that simplify deployment and operations.

It can&nbsp;apply services and enforce&nbsp;policies uniformly across wired connectivity in the data centers.","

This is the hardware and software which allows us

Moving from Cisco Fabric Path to Arista VXLAN&nbsp;for the central and regional data centers.

&nbsp;",,,Platform,,,,,,,,,,,,,,,2023-Mar-24,SM-116608,SM-116608,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 14:29,SMC-588683
370164,Data Center Network Manager,Data Center Network Manager,DCNM,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Johan Nordbeck,,,,,"

Cisco Data Center Network Manager (DCNM) is&nbsp;a management system for the Cisco Unified Fabric. It enables you to provision, monitor, and troubleshoot the data center network infrastructure","

Management platform for Cisco network fabric","

Cisco",Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2024-Jan-08,2022-Mar-28,SM-116164,SM-116164,Technology System,,"

Created by script using data in Nursery",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-08 09:01,2022-Mar-28 17:14,SMC-370164
588681,Data Center Network Segment Protection,Data Center Network Segment Protection,DCNWSEGP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Ola Sandin,Johan Nordbeck,,,,,"

The central segment protection platform, i.e firewall platform which allows us to protect network segments in the central and regional data centers.","

The hardware and software implementing the segmentation protection i.e firwalls.

Based on technologies from Palo Alto.

Managed by PNRM - Panorame, which is a separate entry.

&nbsp;

&nbsp;",,,Platform,,,,,,,,,,,,,,,2023-Mar-24,SM-116606,SM-116606,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 14:24,SMC-588681
637775,Data Distribution Catalog,Inter-Ingka Data Distribution Catalog,DDCAT,Planned,Data Integration and Middleware,Data and Integration,,Anna Relkman,Anna Relkman,,,https://confluence.build.ingka.ikea.com/x/TtJjD,,"

Catalog for data exchange with Inter.","

Catalog for all data distributions to and from Inter, showing involved systems and data concept(s) for each Inter data distribution Interface.",,,Platform,Integration Enablement Systems,,Cross Company Data Distribution,,,Data Integration and Middleware,,Commodity,Make,Invest,,,,,2023-Jul-25,SM-116774,SM-116774,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3355,Setting System Master Identifier reference for Technology System,2023-Dec-13 13:31,2023-Jul-25 12:46,SMC-637775
609013,Data Distribution Framework,Inter-Ingka Data Distribution Framework,DDFRW,Active,Data Integration and Middleware,Data and Integration,,Anna Relkman,Anna Relkman,,,https://confluence.build.ingka.ikea.com/x/TtJjD,,"

Tools and guidelines for data exchange with Inter.","

Tools, reusable component, solution patterns and guidelines to support and speed up the process of implementing data distributions to and from Inter.",,,Platform,Integration Enablement Systems,,Cross Company Data Distribution,,,,,,,Invest,,,,,2023-Apr-24,SM-116648,SM-116648,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-24 09:38,SMC-609013
487659,Data Modeling Tool,Ingka Data Modeling Tool,DMT,Active,Data Integration and Middleware,Data and Integration,,Anders Ryberg,Hedvig Alling,,Anders Pauli,https://confluence.build.ingka.ikea.com/display/DATAMODEL/Data+Modeling,,"

This tool supports the data modeling practise. It is used when&nbsp;defining data entities, attributes and the relationships between them to ensure that data is understood, standardised and re-usable across the company. Data modeling as a practice has a wider scope than the strategic data assets. Any type of data can be modeled, from conceptual level to logical and physical level.","

The tool is based on the software ER/Studio Data Architect from Idera. It consists of a Windows client, a central repository and a web application for publishing data models among other things.&nbsp;

The server side component run&nbsp;on a Windows server&nbsp;hosted by Ingka.","

ER/Studio",,Platform,,,Data Modeling Tool,,,,,,,,,,,,2022-Jul-26,SM-116300,SM-116300,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2024-Jan-11 11:56,2022-Jul-26 14:20,SMC-487659
172384,Data Pipeline Platform,Data Pipeline Platform,DPP,Active,Data Integration and Middleware,Data and Integration,,Siddharth Gupta,Siddharth Gupta,,Frederick Lubbe,https://confluence.build.ingka.ikea.com/display/DPP,,"

Data Pipeline Platform is delivering a&nbsp;unified, standardized&nbsp;platform of tools and frameworks for both&nbsp;data analysts&nbsp;and&nbsp;data engineers. It supports data-for-analytics use cases solving the challenges of&nbsp;automation, orchestration, data quality, connectivity, monitoring and ease of use. It makes it easy to create data pipelines that has multi-platform coverage, gain insights to identify errors and potential improvements. It is provided as a&nbsp;self-serve platform with a no-code first&nbsp;approach and still allow for own-code extensibility.","

The tech stack is not finalized yet. PoC is ongoing autumn 2021.

As from a host platform view it is cloud agnostic and on premise capable.","

Data Pipeline Platform (DPP) is replacing Data Pipeline Framework (DPFW).",,,,,Data Pipeline Platform,,,,,,,Invest,,,,,2021-Sep-10,SM-105090,SM-105090,Technology System,,"

Created by script using data in Nursery",,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Sep-10 16:17,SMC-172384
346686,Dataprep,Cloud Dataprep,DPREP,Active,People Management,People,,DurgaPrasad Tirumala,Siv Almryd,,,https://confluence.build.ingka.ikea.com/x/Y8g-Eg,,"

Dataprep by Trifacta is a&nbsp;data service for visually exploring, cleaning, and preparing structured&nbsp;and unstructured data for analysis, reporting, and machine learning.","

https://cloud.google.com/dataprep","

Cloud Dataprep by Trifecta",SaaS,Digital Product,,,HR_PAOM – People Administration and Organizational Management,None,Web browser,People Management,Minor,Commodity,Buy,Tolerate,,,,,2022-Feb-01,SM-105256,SM-105256,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Feb-01 10:29,SMC-346686
568296,DBaaS Platform,Database as a Service Platform,DBAAS,Active,Data Integration and Middleware,Data and Integration,,Dinesh Adhikari,Dinesh Adhikari,,,https://confluence.build.ingka.ikea.com/x/JYNjD,,"

Database as a Service (DBaaS), a complete software defined infrastructure which all capabilities needed to host a database are built in and infrastructure is managed through APIs. A self service portal (https://mydb.ikea.com) is built where application/product teams can create and manage databases themselves. DBaaS hosting is available in Ingka datacenters in Sweden and China.","

Database Platform Services provides relational and no-SQL databases via traditional channels or deployed through self-service models.",,Central Private Hosting,Platform,,Database Platform Services,Databases Platform Services,,,,Minor,Commodity,Make||Buy,Invest,,,,,2023-Jan-02,SM-116493,SM-116493,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jan-02 10:03,SMC-568296
523157,DELA,DELA,DELA,Active,Strategy and Business Planning,Identity and Access Management,,Daniel Dahlberg,Claudio Medina,,,,,"

The business value we aim to create with DELA is to have one standardised data sharing framework for how Ingka are sharing data. That will make it easier for us all to have a consistent and secure approach to data sharing. The framework we have brought forward and all its benefits can be found on this confluence page: https://confluence.build.ingka.ikea.com/x/rZqlFQ and https://confluence.build.ingka.ikea.com/x/xfcvH","

The below section drills a bit more into what capabilities DELA core engine, based on the architecture which can be found on Confluence here:&nbsp;https://confluence.build.ingka.ikea.com/x/xfcvHEngine is separated from configurationCreates DataSet(s) and View(s) to be sharedApplies configured IAM permissions on the DataSet(s) and/or View(s).Infrastructure as Code (IaC) is automated using TerraformGitHub Checks / ToolsGitLeaksStatic Token And Credential ScannerCommit linting (conventional commits)Changelog Management (semantic versioning)Renovate to automatically update dependenciesBlackDuck ScanningSlack notification systemData access retention checksSecretsOPerationS&nbsp;(SOPS) is available for encryption/decryption for sensitive data in GitHub - on the fly.

You can find the DELA Engine GitHub Repository here:&nbsp;DELA Engine","

DELA, BQAM (BigQuery Access Management), DSF (Data Sharing Framework)",Public Cloud,Digital Product,,,Cross Product Engineering,,,Identity and Access Management,Minor,Innovating,Make,Invest,,,,,2022-Nov-04,SM-116425,SM-116425,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Nov-04 12:47,SMC-523157
577513,Delivery Fee Calculator,Delivery fee search function,DEFE,Active,Japan,Customer Information Management,Takanori Iseki,Takanori Iseki,,,,,,"

A delivery fee caluclator which is build by the vender using javascript on our JP ec site","

Js, AWS s3 bucket",,,,,,,,,Customer Meeting Point Web,,,,,,,,,2023-Feb-21,SM-116561,SM-116561,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Feb-21 03:19,SMC-577513
114187,Densify,Densify,DENSIFY,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,,,Capacity &amp; Optimization tool,,,SaaS,,,it capacity management (itcm),,,,,,Commodity,Buy,Decommissioned,,,,2022-Jul-14,2020-Nov-06,SM-100670,SM-100670,Technology System,,"

Decom. in Feb 2022",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114187
136313,Developer Portal,Developer Portal,DEVPORT,Active,Engineering Services,Developer Enablement,,Magnus Christiansson,Fredrik Tell,,,https://portal.dev.ingka.com/docs/default/Component/developer-portal,https://allen.ingka.com/home,"

The Developer portal (Allen) is a product that provides an interface for development teams to create new services, get insight to the current state of them, and discover API's and services across the landscape.

https://allen.ingka.com/home","

Based on Backstage.io, it's a web based solution that provides scaffolding capabilities, and the ability to ingest metrics and data through plugin.&nbsp;","

Allen",Public Cloud,,,,Developer portal,Co-worker,Web Extension,Engineering Services,Strongly,Commodity,Buy||Make,Invest,,,,,2021-Apr-22,SM-101112,SM-101112,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Dec-20 09:24,2021-Apr-22 17:25,SMC-136313
114191,Development Workbench,Development Workbench,DWB,Active,Engineering Services,Developer Enablement,,Jan Magnusson,Niclas Strandéus,,,,,Development Workbench is a collection of tools supporting the development process,,,Central Private Hosting,,,development workbench,,Co-worker,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100676,SM-100676,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-21 16:34,2020-Nov-06 15:30,SMC-114191
114182,Development workbench change and version control,Development workbench change and version control,DEVWBVC,Active,Engineering Services,Developer Enablement,,Ghyath Alrukabi,Niclas Strandéus,,,,,change and version control for development workbench,,,On-Premise,,,,,Co-worker,,,None,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100671,SM-100671,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-21 16:08,2020-Nov-06 15:30,SMC-114182
114188,DFP,Data Filtering and Preferences,DFP,Active,Identity and Access Management,Identity and Access Management,Andreas Andersson,Marcus Nyh,Johan Finndahl,,,https://confluence.build.ingka.ikea.com/display/DFP/,,"

Dynamic Filtering and Preferences connected to user roles.

The Application Rights Service is fulfilled through the product called Data Filtering and Preferences (DFP). This product enables easy user management and data filtration together with user and system preference management when integrated in other systems.","

DFP API for JAVA and .NET together with the EBCDFP solution based on ITF standards",,On-Premise,Platform,Identity & Access Management,application rights,,,,,Partly,Differentiating,Make,Migrate,2024-May-01,,,,2020-Nov-06,SM-100672,SM-100672,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114188
114397,Digital Signatures,Digital Signatures,DIGSIGN2,Active,Digital Workplace,End User Enablement and Productivity,,Mattias Delbeck,Dino Omerhodzic,,,,,,,,SaaS,,,,,,,,Partly,Differentiating,Buy,Invest,,,,,2020-Nov-06,SM-100995,SM-100995,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-22 05:27,2020-Nov-06 15:30,SMC-114397
114186,Digital Signing,Digital Signing,DIGSIGN1,Active,Digital Workplace,End User Enablement and Productivity,,Mattias Delbeck,Dino Omerhodzic,,,,,Digital Signing provides and manage internal or external electronic signatures,,,SaaS,,,digital signing,,,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100673,SM-100673,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-22 05:39,2020-Nov-06 15:30,SMC-114186
376016,Director,Director,DIRECTOR,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Francis Yeung,,,,,"

Versa Director is a virtualization and service creation platform that enables the creation, automation and delivery of services using Versa WAN&nbsp;","

The management software for managing our SD-WAN infrastructure","

cnn Versa Director",SaaS,Platform,,,,,,,Partly,Commodity,Buy,Invest,,,,,2022-Apr-14,SM-116189,SM-116189,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Apr-14 09:54,SMC-376016
114190,Distributed Connectivity,Distributed Connectivity,DISTCON,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Christian Winberg,,,,,Providing Wi-Fi aswell as routing and switching connectivity within a distributed site,"Cisco catalyst, cisco, Wi-Fi, Prime DNA Center
Aruba Switches and routers, Aruba Airwave, Aruba Central",CNI,On-Premise,Platform,,,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100675,SM-100675,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-05 08:58,2020-Nov-06 15:30,SMC-114190
588682,Distributed Network Fabric,Distributed Network Fabric,DISTNWF,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Nino Pijaca,Christian Winberg,,,,,"

The network fabric in a distributed site delivers the hardware and software&nbsp;to create the following benefits:

It abstracts base physical topology to create virtualized layers that simplify deployment and operations.

It can&nbsp;apply services and enforce&nbsp;policies uniformly across wired and wireless connectivity in the site.","

The distributed network fabric is implemented using hardware and software deliverd by HPE Aruba for switching and wirelss controller:

&nbsp;

&nbsp;",,,Platform,,,,,,,,,,,,,,,2023-Mar-24,SM-116607,SM-116607,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 14:27,SMC-588682
523045,Distributed Private Compute,Distributed Private Compute,DPC,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Hilmar Beck,Hiong Seng Goh,,,,,"

Software with specific purpose to support compute need for hosting business systems and infrastructure components at Ingka distributed sites&nbsp; .","

Automation and configuration software related to enabling the hosting functionality in our distributed offerings. The system is depending on Nutanix software defined in HCI Core and HCI Management.","

DPC",,Platform,,,,,,,,,,,,,,,2022-Nov-03,SM-116421,SM-116421,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-12 15:54,2022-Nov-03 14:28,SMC-523045
523046,Distributed Private Compute Metro,Distributed Private Compute Metro,DPCMETRO,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Hilmar Beck,Dag Ehnbom,,,,,"

Software with specific functionality to allow for migration of compute resources between clusters, primarily used for IKEA deployment for warehouse management solution.","

Automation and configuraton software related to enabling the metro functionality in Nutanix AHV / AOS

This system is depending on the base delivery in Distributed Private Compute.",,,Platform,,,,,,,,,,,,,,,2022-Nov-03,SM-116422,SM-116422,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-12 15:56,2022-Nov-03 14:32,SMC-523046
613570,DOR,Digital Operational Review,DOR,Active,Food,Food,,Diego Martín,Maria Lahoz,,,,,"

Digital Operational Reviews is a new product designed to conduct comprehensive digital audits in food operations.

The Digital Operational Reviews product is specifically tailored to the needs of food operations.

Digital Operational Reviews can help food operations to streamline their processes, optimize their performance, and ensure compliance with regulatory requirements.","

It is a webapp developed internally",,Public Cloud,Digital Product,,,,,,,Partly,,,,,,,,2023-May-17,SM-116678,SM-116678,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2636,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-May-17 09:20,SMC-613570
315251,DPBS Finance and Tax,Data Pipelines Business Support Finance & Tax,DPBSFT,Active,Finance and Procurement,Finance and Tax,,Mohamed Amin,Mohamed Amin,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=*********,,"

DPBSFT provides integrations for Data Pipelines Business Support Finance &amp; Tax.","

This is a landing area that is used to setup topics / brokers to Event mesh / Data pipelines.","

DPBSFT is the short name for Data Pipelines Business Support Finance &amp; Tax. It comes under the Business Support Foundation&nbsp;stream in the Business Support domain.",Public Cloud,Platform,IKEA Integration Platform,data pipelines business support,DPBS F&T (Apollo),Co-worker||None,,,Minor,Innovating,Make,Invest,,,,,2021-Nov-01,SM-105162,SM-105162,Technology System,,"

Created by script using data in Nursery",,Moved from Business Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Nov-01 11:34,SMC-315251
314259,DPBS Procurement,DPBS Procurement,DPBSPRC,Active,Finance and Procurement,Finance and Tax,,Vineeth Elattuvalappil,Mohamed Amin,,,https://confluence.build.ingka.ikea.com/display/BODI/DPBS+Procurement,,"Our initiative aims to establish a robust platform/system serving as a central hub for data convergence through the (ABBE-EBBA) / Cross-company data distribution framework within the business support domain, specifically tailored for the procurement stream. The orchestration of this platform falls under the purview of the data pipelines team within SBP: Finance &amp; Procurement stream.","This designated landing area functions as a warehouse for data received from INTER via the data distribution concept. It plays a pivotal role in disseminating this information across INGKA through a variety of API formats, ensuring seamless accessibility and integration.","DPBSPRC, a concise moniker for Data Pipelines Business Support on Procurement, represents an integral component within the SBP: Finance &amp; Procurement Data Pipelines stream in the expansive Business Support domain.",Public Cloud,Platform,IKEA Integration Platform,data pipelines business support,DPBS Procurement (OM),None||Co-worker,,,Minor,Innovating,Make,Invest,,,,,2021-Oct-26,SM-105158,SM-105158,Technology System,,"

Created by script using data in Nursery",,Moved from Business Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Oct-26 15:44,SMC-314259
475962,DVM Automation Framework,Domain Value Map Automation Framework,DVMAUTO,Active,Data Integration and Middleware,Data and Integration,Fely Vacalares Sabaldana,Fely Vacalares Sabaldana,Fely Vacalares Sabaldana,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=240356729,,"

Enable the customer to configure IKEA (Ingka &amp; Inter) business units (CDC, LSC, STO, Retail Unit, Compartment and so on..) in IKEA Oracle&nbsp;Integration platform (OIP).","

This is an automation initiative and our solution is running on Jenkins pipeline which is deployed in OpenShift platform. The solution is fetching data from Jira service desk (https://jira.digital.ingka.com/servicedesk/customer/portal/125) and based on the requirement it updates xml files in github and then deploy the jar files in weblogic servers.",,,,Integration Enablement Systems,oracle integration services,ISC DVM AUTOMATION,,,,,,,Eliminate,2027-Dec-31,,,,2022-Jul-05,SM-116269,SM-116269,Technology System,,"

Created by script using data in Nursery",,Removed Jira Service Desk project (not allowed since not all have access),Setting System Master Identifier reference for Technology System,2023-Dec-27 09:37,2022-Jul-05 11:26,SMC-475962
675769,ECOSIA FR,ECOSIA France - Sustainable Search Engine,ECOSFR,Active,France,Circular Sustainability,Florence ICARD,Yann DA SILVA,Jerome DESOUBEAUX,,,,,"

Ecosia is an eco-friendly search engine that uses advertising revenue generated from online searches to plant trees worldwide. By utilizing its services, users indirectly support reforestation projects while conducting their daily internet searches.","

Ecosia has been implemented in France through a custom alias deployed on all Business units via IKEA MANAGEMENT UTILITY.

Custom alias has been created with the help of&nbsp;Dig Co-worker Enablement team at Global support.

The alias is a script that will change the Group policies in EDGE.

As per France ISDP teams recommendation, the alias will add ECOSIA as availbale search engine in Microsoft EDGE. Users are not forced to use ECOSIA but they are encouraged to do so if they will.&nbsp;

&nbsp;",,,Digital Product,,,,,,Digital Workplace,,,,,,,,,2023-Nov-16,SM-116871,SM-116871,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4542,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Nov-16 17:04,SMC-675769
523600,EcoStruxure IT,EcoStruxure IT,ECOSTX,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Hilmar Beck,Hiong Seng Goh,,,,,"

Facility Management software supporting the physical hosting cabinet for distributed sites.","

Software to&nbsp;Monitor an extensive range of Schneider Electric and 3rd party devices.

The inventory shows details for all devices, and provides you with a quick overview of e.g.:Input/output powerBalanceRuntime

Allows you to make data driven decisions on the performance, efficiency and health of your equipment",,,Platform,,,,,,,,,,,,,,,2022-Nov-09,SM-116428,SM-116428,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Nov-09 08:30,SMC-523600
675588,Edge Event Streaming,Edge Event Streaming Platform,EESP,Planned,Data Integration and Middleware,Data and Integration,,Tusar Ranjan Pattnaik,Tusar Ranjan Pattnaik,,,https://confluence.build.ingka.ikea.com/x/6WAaJQ,,"

Platform provision and operational support for a message streaming platform at the edge of an application workload that could enable Ingka sub-domains to build an Anti Corruption Layer for their respective applications to secure data quality and develop he capabilities of treating this platform as a single source of truth for upstream and down stream data flows","

Platform provision and operational support for a message streaming platform at the edge of an application workload that could enable Ingka sub-domains to build an Anti Corruption Layer for their respective applications to secure data quality and develop he capabilities of treating this platform as a single source of truth for upstream and down stream data flows",,,Platform,Integration Enablement Systems,,,,,,,Commodity,Buy,Invest,,,,,2023-Nov-15,SM-116864,SM-116864,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4500,Setting System Master Identifier reference for Technology System,2023-Dec-13 13:32,2023-Nov-15 14:59,SMC-675588
371570,EfficientIP,EfficientIP,EIP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Venkatesh Narasingam Kuppusamy,,,,,"

Platform for providing user devices or other network connected devices&nbsp;with fundamental network services like DNS resolution, DHCP for providing IP-addresses and being able to control the IKEA IP plan","

Platform for managing the the DDI (DNS, DHCP, IPAM) functionality&nbsp;for the IKEA landscape. Provided by EfficientIP","

cnn",Central Private Hosting,Platform,,,,,,,Partly,Commodity,Buy,Invest,,,,,2022-Mar-31,SM-116170,SM-116170,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Mar-31 11:31,SMC-371570
126597,EKMS,Enterprise Key and Encryption Management Solution,EKMS,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Michael Keane,Fredrik Åhlstedt,,,https://confluence.build.ingka.ikea.com/display/CYBERSEC/EKMS+-+Encryption+Key+Management+System+Home,,"

Provides secret key management services","

&nbsp;Secret key management services","

DRAFT- Onboarding Process - Cyber Security - Confluence (ikea.com)

IKEA- Encryption Key Management System - Cyber Security - Confluence",On-Premise,Platform,,,,,,,Partly,Commodity,Buy,Tolerate,,,,,2021-Mar-25,SM-101080,SM-101080,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Mar-25 15:36,SMC-126597
114202,Emerson-Sitescan,Emerson-Sitescan,EMERSON,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Annika Ederfors Aronsson,,,https://confluence.build.ingka.ikea.com/x/UjevB,,"

Data Center Monitoring","

Monitoring and control tool for facility support systems in data centers","

dcp aix",On-Premise,Platform,,,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100682,SM-100682,Technology System,,"

Sitescan is still used as monitoring/control system for DC7 and DC8",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114202
114197,Endpoint protection,"Endpoint protection, Cylance",ENDPROT,Retired,Cyber Security,Cyber Security,,,,,,,,"

End point protection and detection suits",,,,,,production security services,,,,,Strongly,Commodity,Buy,Decommissioned,,,,2023-Sep-06,2020-Nov-06,SM-100684,SM-100684,Technology System,,Martin PerssonChanged name to match excel,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114197
695143,Engineering Baseline Frontend,The Engineering Baseline Skapa Frontend,BASELINE,Active,Engineering Services,Developer Enablement,Lars Lindkvist,Hygo Da Silva Reinaldo,Fredrik Tell,,,https://baseline.ingka.dev,,"

This is Engineering Baseline Frontend, the App running on the URL https://baseline.ingka.dev.

For more information about the Engineering Baseline, including how to participate and to join the baseline workgroup, please check the Engineering Baseline.

The Business goal for creating a Baseline Frontend is to make the Engineering Baseline easier for Product Teams understand and to apply the ADR's on their daily basis work. Additionally, the Baseline Frontend also aims to connect Product Teams with Subject Matter Experts.","

The Baseline frontend App uses the following tech stack:

- hugo framework with skapa support;

- javascript is the main language;

- Github as SCM: https://github.com/ingka-group-digital/Engineering-Baseline

- Google Cloud Provider for hosting the App.",,,System of Truth,,,,,,Engineering Services,,,,,,,,,2024-Jan-11,SM-116941,SM-116941,Technology System,,,,Created by smc-magic using ticket SYSCATSD-5806,,2024-Jan-11 15:47,2024-Jan-11 15:47,SMC-695143
114356,Enterprise Auth0,Enterprise Auth0 - Token Provider Solution,TOKPROV,Active,Identity and Access Management,Identity and Access Management,Andreas Andersson,Emily Millnert,Emily Millnert,,,https://confluence.build.ingka.ikea.com/display/EKR234IA/Auth0,,"Co-woker user authentication for SSO identity broker service and API security. 
Note: API security is being moved to Entra ID. ","

Auth0",,Public Cloud,Platform,Identity & Access Management,,,,,Identity and Access Management,Strongly,Innovating,Buy,Invest,,,,,2020-Nov-06,SM-100835,SM-100835,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-05 09:28,2020-Nov-06 15:30,SMC-114356
114192,Enterprise Job Scheduling,Enterprise Job Scheduling,EJOBSCH,Active,Service Management and Operations,Data and Integration,Henrik Nielsen,Henrik Nielsen,Henrik Nielsen,,,,,"Control-M serves as a comprehensive solution for automating and managing the end-to-end execution of business processes and IT workflows, contributing to increased efficiency, reduced operational costs, and improved overall business agility.","Control-M is an enterprise-grade workload automation and job scheduling solution designed to manage, monitor, and automate various business processes and IT workflows","

Countrol-M",On-Premise,,,Job Scheduling,,,,,,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100681,SM-100681,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-03 14:14,2020-Nov-06 15:30,SMC-114192
621489,Enterprise Metadata Backbone,Enterprise Metadata Backbone & Repository,EMB,Planned,Data Integration and Middleware,Data and Integration,,Jonathan Gabor,Dzumali Salmani,,,https://confluence.build.ingka.ikea.com/x/KtZaHw,,"The enterprise metadata backbone (a.k.a. Ryggrad) enables discovery, governance, and management of all things metadata, and aims to fulfill the metadata management capability as defined by the Ingka Data Foundation.","

Metadata is ingested and transformed using the Data Pipeline Platform (StreamSets &amp; dbt), stored using multiple database technologies (GCS, BQ, SQL, Graph, Vector), and exposed via multiple APIs (Django, FastAPI). Semantic matches are discovered and established with the use of LLMs (SBert, but additional model evaluations are planned).","

Ryggrad: Swedish for “backbone”",,Platform,,,Data Discovery,,,,,,,,,,,,2023-Jun-01,SM-116716,SM-116716,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2855,Setting System Master Identifier reference for Technology System,2024-Jan-10 13:57,2023-Jun-01 11:13,SMC-621489
606048,Enterprise Schema Registry,Enterprise Schema Registry,SCHREG,Active,Data Integration and Middleware,Data and Integration,,Nihar Shah,Nihar Shah,,,https://confluence.build.ingka.ikea.com/x/YQugEg,,"

Schema Registry provides a repository for storing, managing and validating schemas for message data. Applications can use schemas to ensure data consistency and compatibility as schemas evolve. Schema Registry is a key component for data governance, helping to ensure data quality, adherence to standards, visibility into data lineage, audit capabilities, collaboration across teams, efficient application development protocols, and system performance","

Schema Registry is a headless platform using RedHat Service Registry (backed by open source Apicurio project). GitHub and Allen (Backstage OSS) forms the user experience &amp; tooling layer",,,Platform,Integration Enablement Systems,,,,,,,,,Invest,,,,,2023-Apr-05,SM-116621,SM-116621,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-05 09:46,SMC-606048
114385,Entra ID,Microsoft Entra ID,AZUREAD,Active,Identity and Access Management,Identity and Access Management,,Emily Millnert,Emily Millnert,,,https://confluence.build.ingka.ikea.com/display/EKR234IA/Identity+and+Access+Management,,"

Primary Identity Provider for Ingka","

Cloud Identity Provider for Ingka",,Public Cloud,Platform,Identity & Access Management,,,,,,Strongly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100987,SM-100987,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114385
126594,EPM,Enterprise Password Management,PASSMAN,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Michael Keane,Fredrik Åhlstedt,,,https://confluence.build.ingka.ikea.com/display/CYBERSEC/1Password+-+Enterprise+Password+Manager+Home,https://confluence.build.ingka.ikea.com/display/CYBERSEC/1Password+-+Enterprise+Password+Manager+Home,"

Enterprise Password Management Solution - 1Password","

Password management solution",,SaaS,Platform,,,,,,,Partly,Commodity,Buy,Invest,,,,,2021-Mar-25,SM-101077,SM-101077,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-11 03:11,2021-Mar-25 15:30,SMC-126594
577507,ES920LRTH2-K,Iot Air Flow and Temperture Management and Monitoring,LOTAIR,Retired,Japan,Content and Inspiration,Takashi Kaneko,Takashi Kaneko,,,,,,"

At IKEA Tachikawa Store, current Air flow management system is old and not enough air bucket to control the air flow and temperature of each sections.

Also, it is difficult to monitor and control closed spaces, crowded places and close-contact from data gathered from current air flow system.","

IoT to controll Air flow and temp",,,,,,,,,Real Estate and Expansion,,,,,,,,2023-Feb-21,2023-Feb-21,SM-116559,SM-116559,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Feb-21 02:42,SMC-577507
606046,Event Catalog,Event Catalog,EVENTCAT,Active,Data Integration and Middleware,Data and Integration,,Nihar Shah,Nihar Shah,,,https://allen.ingka.com/api-docs,,"

An Event Catalog is a solution that let engineering &amp; product teams design, create, discover, catalog, share, visualize, secure, and manage their Event APIs in the paradigm of Event-driven Architecture (EDA)","

Event Catalog is a cloud native application using mostly serverless Google Cloud services for backend. Front end is realized in Allen (Backstage OSS) using React.js and Typescript",,,Platform,Integration Enablement Systems,,Schema Registry,,,,,,,Invest,,,,,2023-Apr-05,SM-116619,SM-116619,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-05 09:33,SMC-606046
127672,Event Mesh,Event Mesh,EVENTM,Active,Data Integration and Middleware,Data and Integration,,Nihar Shah,Nihar Shah,,,https://confluence.build.ingka.ikea.com/display/EVENTM/,,"

Event Mesh is a layer for distributing events among decoupled applications, cloud services and devices in a hybrid multi cloud system landscape. An Event Mesh is created and enabled through a network of interconnected event brokers. It allows events from one application to be dynamically routed and received by any other application no matter where these applications are deployed (IKEA Data Centers, Public Cloud providers or any distributed IKEA site).","

Event Mesh is a platform powered by Solace PubSub+ mostly in a headless manner. Event Mesh consists of a control plane that is managed by Solace and a data plane. The data plane is the actual event mesh that consists of a network of globally distributed and connected event brokers. The tooling and user experience layer is managed via GitHub and Allen (Backstage OSS).","

Previously called Event management",,,Integration Enablement Systems,ingka event mesh,,,,,Partly,Differentiating,Buy,Invest,,,,,2021-Apr-06,SM-101091,SM-101091,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Apr-06 09:18,SMC-127672
606967,Fastly CE,Fastly Compute@Edge,FASTLYCE,Active,Customer Meeting Point Web,Digital Platform and Core Infrastructure,,Peter Hansson Åkerblom,Peter Hansson Åkerblom,,,,,"

Fastly Compute@Edge is used for Edge Computing capabilities.","

Fastly Compute@Edge using distributed serverless technologies to provide web content",,,Platform,,,,,,,,,,,,,,,2023-Apr-12,SM-116626,SM-116626,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-12 10:35,SMC-606967
512552,File Backup for Retail,File Backup for Retail,RETBKUP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,,,,,,"

Software for managing file backup at distributed retail siteas","

Software for managing file backups for distributed sites using&nbsp;Veritas Backup Exec&nbsp;",,Distributed Private Hosting,Platform,,,,,,,None,Commodity,Buy,Eliminate,,,,,2022-Oct-04,SM-116372,SM-116372,Technology System,,Created by script using data in Nursery,,Inter user should not have EM role on Ingka owned system,Setting System Master Identifier reference for Technology System,2024-Jan-17 15:33,2022-Oct-04 15:06,SMC-512552
114396,File Integrity Monitoring,File Integrity Monitoring,FILINMON,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Fredrik Åhlstedt,Fredrik Åhlstedt,André Jee,André Jee,,,"

Provides file integrity monitoring capability","

file integrity monitoring capability",,On-Premise,Platform,,production security services,,,,,Partly,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100996,SM-100996,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114396
664903,File Mesh,File Mesh,FMESH,Planned,Data Integration and Middleware,Data and Integration,,Anna Relkman,Anna Relkman,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=647131884,,"

This catalog entity is created to plug and play a pivotal role in assessing products capable of facilitating file-to-file transfers. The Application File Mesh provides a mechanism for applications to publish and subscribe to files

The objective is to establish a product that can provide abstraction for both the sender (publisher) and the receiver (subscriber) applications, eliminating the need for them to handle file transfer protocols, file locations, and endpoints

To begin with, the Application File Mesh isn't a fully comprehensive Managed File Transfer solution. Its primary aim is to offer product engineering teams a straightforward and secure means of distributing/transferring files within our hybrid multi-cloud system environment.","

This would serve as a catalog entity to initiate the evaluation process of FileMesh

Beyond just standard file transfers, we are also striving to create a product that can deliver an Application File Mesh with enhanced functionalitiesGuaranteed delivery&nbsp;that leverages ‘retry’ and ‘resume’ options to ensure a successful delivery of files and recovery from failed transfers.File integrity&nbsp;to certify that the file was not altered accidentally or voluntarily (think “man-in-the-middle”) during its transit.Non-repudiation&nbsp;or the ability to prove that a file was sent by one party to another by using digital signatures for each participant.End-to-end reporting&nbsp;on file transfers. Notification of successful file transfers (acknowledgement) all the way to the sending business applications.Global visibility and&nbsp;audit-ability&nbsp;on administrative (configuration) and runtime (transfer) operations.Secure file transfer","

IFM- Ingka File Mesh",,Platform,Integration Enablement Systems,,,,,Data Integration and Middleware,,Commodity,Make,Invest,,,,,2023-Sep-28,SM-116818,SM-116818,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3909,Setting System Master Identifier reference for Technology System,2023-Dec-13 13:30,2023-Sep-28 09:43,SMC-664903
114200,Fileshares,Fileshares,FILESHAR,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,Central and Distributed on-premises fileshares,,,On-Premise,,,,,,,,Minor,Commodity,Make,Migrate,,,,,2020-Nov-06,SM-100687,SM-100687,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114200
334838,FlexDeploy,FlexDeploy,FLEXDEPLOY,Active,Data Integration and Middleware,Data and Integration,,Fely Vacalares Sabaldana,Fely Vacalares Sabaldana,,,https://confluence.build.ingka.ikea.com/x/241TDg,,"

FlexDeploy CICD application used to support Oracle Middleware Integration components","

FlexDeploy is a bought product. It is a DevOps platform for Continuous Integration, Continuous Delivery, and Release Automation, enabling enterprises to deliver better software faster. It is deployed on-prem using Tomcat.",,,,Integration Enablement Systems,oracle integration services,DIM FlexDeploy,,,,,,,Tolerate,,,,,2021-Dec-14,SM-105211,SM-105211,Technology System,,"

Created by script using data in Nursery",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Dec-14 09:41,SMC-334838
660275,FMC,Cisco Firepower Management Center,FMC,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Ricky Lu,Johan Nordbeck,,,,,"

Being able to manage the firepower IPS (Cisco) with features like:Manage&nbsp;intrusion&nbsp;rules and objectsManage deploymentsManage deviceMonitor intrusion event and security event.","

The Cisco Firepower Management Center is the central tool for managing all firewpower IPS at all distributed sites.",,Distributed Private Hosting,Platform,,,,,,,None,Commodity,Buy,Tolerate,,,,,2023-Sep-20,SM-116806,SM-116806,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3855,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Sep-20 10:52,SMC-660275
585560,FMS Data Platform,FMS Data Platform,FMSDP,Planned,Store Fulfilment Operations,Fulfillment Locations,,,,,,,,"

A data platform to govern data moving from the operational to the analytical plane.

Data scope is for this data platform is for the the following (sub-)domains:Store Fulfilment OperationsWarehouse Fulfilment Operations

Principles embedded in the architecture:Decentralized ownership supported by central Data FoundationUniform access management to support the model/data governance.No garbage in - garbage out =&gt; Imperative for the quality of the data products.","

This platform provides the capability for data product teams to store data contracts on the data they are producing for wider consumption. This contracts specify where the data is published, the structure of the data, and other technical details about the data required for the platform to ensure that produced data is indeed adhering to the published contract.



The platform will provide a fast data (i.e. streaming) implementation of the validation to keep the possibility open for operational system to operational system contract enforcement.



Monitoring and alerting capabilities will be provided to provide both producers and consumers with a view on their production data streams. Early warnings close to the source is expected to significantly reduce the cascading effects of undesired data quality issues.",,,Platform,Fulfilment Management Solution,,FMS Data Platform,,,,,,,,,,,,2023-Mar-15,SM-116594,SM-116594,Technology System,,Created by script using data in Nursery,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-15 09:56,SMC-585560
365394,Food Cowo Data,Food Cowo Data,FCOW,Retired,Food,Food,,Ciara Dolan,,,,https://confluence.build.ingka.ikea.com/display/IKEAFOOD/Food+COWO+Data,,"

One of the strategical pillars of the Food Digital Subdomain is to make the life easier of the co-workers that deal with food and free them from repetitive tasks. By doing so, co-workers can spend more time helping and serving the customers that go to IKEA to enjoy a delicious break while eating the food that IKEA offers them.

As part of the Data and Analytics team of the Food Digital subdomain, the team is interested in understanding three different topics:How many co-workers work daily in the food areas, what are their areas of expertise. The food area is unique in this sense, as there might be some co-workers that may not be able to serve hot food, others that may not have the needed skill needed to prepare the food, etc.Understand the impact that the co-workers have on the customers and hence, the sales figures. The rationale here is that having not enough or the right staff would lead to unhappy customers and lower sales figures.Understand the impact that the new digital products are having in their daily work. Gather feedback directly from the co-workers could help the development of new versions or even to roll out.","

Access to the co-workers data within the GCP enviroment",,,,,,D&A Food Digital ,,,,,,,,,,,2022-Oct-31,2022-Mar-25,SM-116163,SM-116163,Technology System,,"

2022-10-31 This was created by the D&amp;A team in Food but is no longer a priority or something that will be worked on.

Created by script using data in Nursery",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Mar-25 15:47,SMC-365394
518641,FoodProductInformationAPI,FoodProductInformationAPI,FPI,Planned,Food,Food,,Daria Di Leonardo,Manuel Rosa,,,https://confluence.build.ingka.ikea.com/display/IKEAFOOD/FPI+-+Artifacts,,"

The Food Product Information (FPI) API will allow to present the commercial and legal product information, for the full Food Range we receive from iFoodFacts/EBBA (INTER) via eRIX (Ingka entry data point) and feed with reliable, consistent and up-to-date information any current and future Digital Products.


A customer&nbsp;expect that the product information displayed on digital devices show the correct information so that they&nbsp;can be sure that the ingredients and allergens in the food are safe for their health.


A co-worker expect to&nbsp;have one single source of data to secure food safety and that we always display the correct ingredients and allergens in every digtal touchpoints.","• FPI is a product developed in-house.
 • It consists of an API and an event consumer.
 • The API instance is hosted on a GCP CloudRun
 • The listener is hosted in a separate GCP CloudRun, so that both instances don't interfere with each other.
 • The API pulls the data from a CloudSQL instance in which the listener writes the data as it is received from eRIX.
 • Only Food Product Information data flows through the system. This information intent is to be displayed to the public. No customer or co-worker related data is handled in this product.
 • The API offers read-only endpoints. Writes are only performed by the listener instance.",,Public Cloud,Digital Product,,,Food Digital Services,,,,Partly,,,,,,,,2022-Oct-21,SM-116395,SM-116395,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Oct-21 10:53,SMC-518641
114199,Forklift clients,Forklift clients (FLT),FORKLIFT,Active,Digital Workplace,End User Enablement and Productivity,,Lukasz Sudak,Lukasz Sudak,,,https://devices.ingka.com/index.php/forklift/,,"

Computers supporting forklift and truck operations in Stores and Warehouses",,,Central Private Hosting,,,,,,,,Strongly,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100688,SM-100688,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:06,2020-Nov-06 15:30,SMC-114199
114205,FreeMind,FreeMind,FREEMIND,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

Open source alternative to the Mindmapping tool Mindmanager",,,Client,,,IKEA Software Tools,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100689,SM-100689,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114205
495647,FulIIP,Fulfilment IIP,FULIIP,Active,Order Management,Order Management,,Per Thorén,,,Yan Jia,,,"

Order Management, Delivery &amp; Service Management and Order Promising And Allocation are the area covered by this middleware platform.","

It predominated by the Oracle SOA, OSB and ODI. All the integrations are built in this technoligies.",,On-Premise,Platform,IKEA Integration Platform,FULINT Fulfilment,Fulfilment Integration,,,Delivery and Services,Strongly,Commodity,,Eliminate,2023-Aug-31,,,,2022-Aug-17,SM-116315,SM-116315,Technology System,,"

Created by script using data in Nursery",,Inter user should not have EM role on Ingka owned system,Setting System Master Identifier reference for Technology System,2024-Jan-17 15:35,2022-Aug-17 09:40,SMC-495647
614606,GAP,Global Admin Portal,GAP,Active,Food,Food,,Paola Piastra,Maria Lahoz,Gema Montero,,,,"

Global Admin Portal is the digital solution to cover the empty space between a final food digital product and its configuration/customization:will be unique for all ikea food web appswill be the container of the back-offices to customize all ikea food web appswill be reusable to quick add-on new back-offices ikea food web apps","

Global Admin Portal is based on:Micro Frontend architecture based on Module FederationNextJS framework to develop frontend, backends and microservicesAuth0 to authenticate users and define an implicit authorization based on rules",,Public Cloud,Digital Product,,,Ingka Digital Food,,,,Minor,,,,,,,,2023-May-24,SM-116686,SM-116686,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2853,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-May-24 07:42,SMC-614606
114401,Github,"Github Enterprise Server, Github Enterprise Cloud",GITHUB,Active,Engineering Services,Developer Enablement,,Jan Magnusson,Niclas Strandéus,,,https://portal.dev.ingka.com/docs/default/Component/github-guidelines,,Source Code Management solution that emphasizes collaboration in development.,FIXED,,Public Cloud,,Continuous Integration,,,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100998,SM-100998,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Dec-21 17:04,2020-Nov-06 15:30,SMC-114401
114400,Global Customer Internet Service,Global Customer Internet Service,GLOCIS,Active,Store Co-Worker Experience,Store Operations and Experience,,Camilla Larsson,Carina Isberg,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=196353762,,"

To provide internet access to customers in the IKEA stores","

Connectivity provided by Service Operations but managed by Customer domain.","GCIS, Customer wifi, store wifi ",SaaS,,,global customer internet service,,,,,Partly,,Buy,Invest,,,,,2020-Nov-06,SM-100999,SM-100999,Technology System,,"

Martin Persson20190628 - changed to SO",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2024-Feb-02 15:57,2020-Nov-06 15:30,SMC-114400
114212,Global Environment Ordering,Global Environment Ordering,GLOWENV,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Patrik Johnsson,,,https://confluence.build.ingka.ikea.com/x/NVo6CQ,,"Delivery of environments, supported by EAB Toolbox","

Build Stream Service is responsible for delivery of new server installations, decommissions and modifications of Servers, Databases, Network Segment with related infrastructure dependencies (like monitoring, Backup, CI etc.). Build Service is responsible for deployment only in central datacenters, i.e.: DH2, DC5,6,7,8,9 &amp; Russia. The service is also responsible to update &amp; manage the product OpAM’s (Operational Architectural Model) for IKEA.","

dcp, EAB, Build Stream Service, EAB Toolbox",On-Premise,Platform,,global environment ordering,,,,,None,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100694,SM-100694,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114212
114203,Global Fax Solution,Global Fax Solution,GLOFAX,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100691,SM-100691,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114203
114209,Global SMS solutions,Global SMS solutions (Hay systems lmtd),GLOSMS,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,SaaS,,,,,,,,Strongly,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100692,SM-100692,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114209
114207,Global Telco,Global Telco,GLOTEL,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,Global Telecom platform using VOIP,,,On-Premise,,,global telco,,,,,Strongly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100698,SM-100698,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114207
114206,Global translation,Global translation (Translationcom),GLOTRANS,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,Translation tools and support Not yet a IT service,,,,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Aug-24,2020-Nov-06,SM-100693,SM-100693,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114206
114208,Google Cloud Platform,Google Cloud Platform,GCP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Bonny Lindberg,Tobias Berg,,,https://confluence.build.ingka.ikea.com/x/iomlB,,"

GCP is a public cloud provider, used by INGKA as the primary public cloud provider (main footprint EU)","

GCP features IaaS/PaaS/SaaS solutions for a very large set of services and features their own Marketplace","

ccoe, clh, Google Cloud Provider, primary public cloud",Public Cloud,Platform,,,,,,,Partly,Differentiating,Buy,Invest,,,,,2020-Nov-06,SM-100690,SM-100690,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114208
610491,Grafana DBMS,Grafana DBMS,GRAFANADB,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,Pontus Skog,Amit Verma,Pontus Skog,,,https://confluence.build.ingka.ikea.com/display/DBMS/Grafana,,"Grafana is an open source tool for visualization.
Currently DBMS is using grafana for real-time performance and other DB metrics to monitoring Postgres databases.
","Grafana open source is open source visualization and analytics software. It allows you to query, visualize, alert on, and explore your metrics, logs, and traces no matter where they are stored.",,,Platform,,DBMS - Database Management Services,,,,,,,,,,,,,2023-May-02,SM-116663,SM-116663,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2606,Setting System Master Identifier reference for Technology System,2023-Dec-04 19:41,2023-May-02 13:58,SMC-610491
485292,HCI Core,Hyper Converged Infrastructure Core,HCICORE,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Annika Ederfors Aronsson,,,,,"

The core software parts which tie compute, network and storage together to enable a hyper converged infrastructure offering","

HCI software which is implemented using Nutanix AOS.

This is the software layer which is foundational in any HCI deliverable.

The core system is also including the hypervisor namned AHV. AHV will never be deployed outside this system boundary.",,Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Invest,,,,,2022-Jul-14,SM-116284,SM-116284,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Jul-14 12:13,SMC-485292
485307,HCI Management,Hyper Converged Infrastructure Management,HCIMGMT,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Annika Ederfors Aronsson,,,,,"

The management software used to manage the hyperconverged infrastructure. Focal point for administration, monitoring, billing and software upgrades.

Implemented with Nutanix Prism Central.","

Management software for Nutanix HCI stack. Prism Central&nbsp;&nbsp;provides an option to monitor and manage multiple clusters through a single web console. This multi-cluster view,is a centralized management tool that runs as a separate instance comprised of either a single VM or a set of VMs. This also contains the Calm service handling the provisioning and Blueprinting of deployments.

This is used to manage they system HCI Core.

&nbsp;","

Prism Central, Nutanix Prism",Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Invest,,,,,2022-Jul-14,SM-116285,SM-116285,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-12 15:57,2022-Jul-14 14:13,SMC-485307
114211,HD Tool,HD Tool,HDTOOL,Retired,Digital Workplace,End User Enablement and Productivity,,Martin Strandmark,Martin Strandmark,,,,,"

webpage for helpdesk employees",,,On-Premise,,,hd knowledge and information tools,,,,,Minor,Commodity,Make,Decommissioned,,,,2024-Feb-05,2020-Nov-06,SM-100696,SM-100696,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Feb-05 10:51,2020-Nov-06 15:30,SMC-114211
629915,HNAS,Hybrid Network Attached Storage,HNAS,Planned,Core Digital Infrastructure,Digital Platform and Core Infrastructure,David Johansson,Arif Shahjehan,David Johansson,,,,,"

A File Storage System used by business to store files","

Hybrid NAS (Network Attached Storage) is a dedicated file storage system that makes data continuously available for employees/applications to collaborate effectively over a network.&nbsp;&nbsp;

&nbsp;","

Hammerspace",,Platform,,central nas,,,,Core Digital Infrastructure,,,,,,,,,2023-Jun-27,SM-116760,SM-116760,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3248,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jun-27 11:08,SMC-629915
140972,Hololens Forklift High Level,Hololens for High Level racking operationsion,HOLOFRKLFT,Active,Belgium,Fulfillment Locations,,,,,,,,"

Hololens for forklift driver, connected with cameras on forklift beams.

Approved in Development and Innovation Network at Inter / supply - Leader (Peter Ac) to proceed with the first exploration case in Winterslag DC","

Microsoft Hololens and device mounted on forklift.

Camera's on forklift beams, conencted with cable to hololens device.

Hololens headset for forklift driver.","

Hololens",On-Premise,,,,Belgium Digital Projects,Co-worker,Stand alone,Warehouse Fulfilment Operations,Minor,Innovating,Buy,Invest,,,,,2021-May-17,SM-105008,SM-105008,Technology System,,"

Pending participation of Toyota (producer of the reachtrucks the hololens camera/infra is mounted on) we start a phase 2 to integrate equipment in reachtrucks and develop more AR capabilities. &nbsp; IF not would es topped because fragility of the equipment.",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-May-17 09:15,SMC-140972
569689,Home Furnishing Projects,Home Furnishing Projects in the Upper Funnel,HFPRO,Retired,Explore,Content and Inspiration,Tom Atema,Grese Konjufca Boltezar,,Elena Bernad San Jose,,,,"

Projects team (within Explore sub-domain) was created in T1FY23 as a response to:

customer needs to be guided in planning and visualizing their home furnishing projects on ikea.com, and

the business opportunity to increase market share and sales of products and ranges that fall within complex buying customer journeys (such as home furnishing projects).

The aim of Projects is to create solutions for ikea.com customers that will help them start and progress with their home furnishing journey, by offering step-by-step guidance, and enabling them to visualize ideas in their own spaces.

The goal is to validate product-market fit through an MVP, which is planned to be released in test markets (TBD) in T3FY23.","

Not yet decided in detail","

Projects",,Digital Product,,,Projects,,,,,,,,,,,2023-Oct-12,2023-Jan-11,SM-116503,SM-116503,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jan-11 13:38,SMC-569689
694744,Hoppscotch,Hoppscotch,HOPPSCOTCH,Planned,Engineering Services,Developer Enablement,,Leon Luu,Pär Svensson,,,,,"

Hoppscotch is an API development platform for making HTTP requests, test APIs and organize API collections.

Hoppscotch enables users to share/collaborate requests, making it a great tool for developers and teams working with APIs.

By having a self-hosted solution, we can avoid high licensing costs that the enterprise version of Postman uses, which we would like to move away from.","

Hoppscotch is an open-source API development platform that allows users to make HTTP requests, test APIs, and organize API collections.

It provides a user-friendly interface for sending various types of requests such as GET, POST, PUT, DELETE, etc., and allows users to set headers, parameters, and payloads easily.

Hoppscotch offers features like authentication handling, WebSocket support, GraphQL queries, environment variables, and sharing/collaboration options, making it a versatile tool for developers and teams working with APIs.",,,Digital Product,Test Enablement,,,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2024-Jan-08,SM-116934,SM-116934,Technology System,,,,Created by smc-magic using ticket SYSCATSD-5424,,2024-Feb-05 10:24,2024-Jan-08 09:14,SMC-694744
526884,HRBRB Simployer NO,Human Resources Business to Business Application Programming Interface,HRB2BSIPNO,Planned,Norway,Business to Business,Hanne Emilson,Evy Johansen,,,,,,"

This system will be used to replace a file transfer upload system from global view to simployer that is being decommissioned, and will be in use until the success factor project from global is ready.

Used to transfer organizational data and sick leave from IKEA's global solution and into Simployer so we are legally compliant","

Simployer has a REST API, and this solution will enable communication between&nbsp;IKEA and Simployer. Projecting to run once a month",,On-Premise||Client,Digital Product,Continuous Integration,,,Co-worker,Stand alone,People Management,Minor,Commodity,Make,Tolerate,,,,,2022-Nov-16,SM-116436,SM-116436,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Nov-16 13:17,SMC-526884
559725,Hypercube,Hypercube,HYPERCUBE,Active,Engineering Services,Developer Enablement,,Martin Norin,Martin Norin,,,https://allen.ingka.com/catalog/default/system/hypercube,,"

Onboard Ikea employee and co- workers into Test Enablement tools through Developer Portal and own Web application.","

Hypercube exposed few REST APIs to onboard users to Test Enablement tools and groups. Azure aithentication is required to access those apis for users and tokens for system to system integration. Deloyed in GCP K8s.",,Public Cloud,,Test Enablement,Test Platform,Test Enablement,Co-worker,,,Minor,Commodity,Make,Invest,,,,,2022-Dec-08,SM-116465,SM-116465,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-21 17:16,2022-Dec-08 09:50,SMC-559725
114215,IAGS - ADC,IKEA Access Gateway Service (ADC),IAGS,Active,Digital Workplace,End User Enablement and Productivity,,Tyler Zhou,Ferenc Horvath,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=298750793,,"

The IAGS service provides access through an Access Gateway/Terminal Server model to centrally hosted published terminal server based applications to users located on the Internet, ROIGs and internally on ICN","

Citrix ADC based on Access Gateway platform","

cwe citrix adc",On-Premise,,,,,,,,Partly,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100704,SM-100704,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-04 03:56,2020-Nov-06 15:30,SMC-114215
340722,iAssist,iAssist,IASSIST,Active,Service Management and Operations,End User Enablement and Productivity,,Remi Winther,Martin Strandmark,,,https://iassist.ingka.com,https://nowit.ingka.com/kb_view.do?sysparm_article=KB0042720,"iAssist is an internal tool (not customer facing) that is used by our global IT Service Desk and the global IT Country organization when solving IT tickets from IKEA co-workers. It is a service that collects multiple scripts and automations, which decrease the need of manual work by our Supporting Co-Workers, making their work faster and more reliable. ","

The tool is a cloud-based web application that enables users (IT support co-workers) to execute scripts from the browser.

iAssist is utilizing the Thor’s Hammer framework to run powershell scripts through a web service (API).",,Public Cloud,,Enterprise service management,iAssist,,,,,Minor,Commodity,Make,Invest,,,,,2022-Jan-18,SM-105233,SM-105233,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2024-Jan-18 11:39,2022-Jan-18 14:34,SMC-340722
114214,IBM Power,IBM Power,IBMPOWER,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Andreas Stehn,,,https://confluence.build.ingka.ikea.com/x/VNvXBQ,,"

Hardware Server platform from IBM, not compatible with x86 hardware platform.","

Compute platform based on IBM Power Systems hardware.

Used both in central and distributed deployments.","

dcp",Central Private Hosting||Distributed Private Hosting,Platform,,,,,,,None,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100700,SM-100700,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114214
114219,IBM Power-VM,IBM Power-VM,IBMPOWVM,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Andreas Stehn,,,https://confluence.build.ingka.ikea.com/x/VNvXBQ,,"

Virtualization engine for Power based workloads from IBM","

Virtualization software for IBM Power Systems

Used both in central and distributed deployments","

DCP",Distributed Private Hosting||Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100706,SM-100706,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114219
114399,IBM Qradar SOAR,Qradar SOAR,IBMRES,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Matthew Itkin,Martin Svensson,,,,,"

Cyber incident management tool used for our Cyber Incident Response Team, mainly used for data breach management and for incidents that involve our markets.","

Cyber incident management tool used for our Cyber Incident Response Team, mainly used for data breach management and for incidents that involve our markets.",,SaaS,Platform,,,,,,,Strongly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-101000,SM-101000,Technology System,,Martin PerssonAdded owner and Solution Area,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114399
114217,IBM Rational Installation Manager,IBM Rational Installation Manager,IBMRATIM,Active,Selling,Sales,,Annika Nordberg,,,,,,,,,,,,,,,,,,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100707,SM-100707,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-20 10:44,2020-Nov-06 15:30,SMC-114217
114223,ICC IDEM IMU Client,ICC IDEM/IMU (Client),ICCIDEMC,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,Mechanism for manage ikea common clients (eg laptops) and distribute/automate installation of software),,,On-Premise,,,desktop,,,,,Minor,Commodity,Make,Eliminate,,,,,2020-Nov-06,SM-100709,SM-100709,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114223
114222,ICC IDEM IMU Server,"IKEA Common Client, IKEA Management Utility",ICCIDEMS,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Patrik Johnsson,,,https://confluence.build.ingka.ikea.com/x/jg7YDg,,"

Mechanism for manage IKEA common servers (virtual and physical).

Managed by Automation and Common tools engineering team.","

Server side part of ICC solution, user to manage Servers and Workplaces, install the software and do the configuration.

Part of this system is also the Thors Hammer API.","

dcp, ICC, Thors Hammer",Central Private Hosting,Platform,,,,,,,None,Commodity,Make,Migrate,,,,,2020-Nov-06,SM-100702,SM-100702,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114222
566109,ICC Kiosk,ICC Kiosk,ICCKIOSK,Active,Digital Workplace,End User Enablement and Productivity,,Mikael Torres,Lukasz Sudak,,,https://icchandbook.ikea.com/,,"

The self-service kiosk definition is a small, self-standing structure, used to display information or facilitate an action. The self-service kiosk meaning will be different for everyone, depending on the specific planned application.","

Kiosk based on Windows platform managed by IMU(ICC):


The self-service kiosk definition is a small, self-standing structure, used to display information or facilitate an action. The self-service kiosk meaning will be different for everyone, depending on the specific planned application.",,On-Premise,Platform,,,Frontline Workplace,,,,Minor,Commodity,Make,Eliminate,,,,,2022-Dec-20,SM-116482,SM-116482,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:16,2022-Dec-20 07:36,SMC-566109
114221,ICC Mac TP,ICC Mac TP,ICCMAC,Active,Digital Workplace,End User Enablement and Productivity,,Yogesh Sharma,Ferenc Horvath,,,,,IKEA Common Client technical platform for Mac,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100710,SM-100710,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-24 10:01,2020-Nov-06 15:30,SMC-114221
114220,ICC Windows Client,ICC Windows Client,ICCWINC,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,"The Desktop service provides a standardized way for IKEA co-workers and partners to access IKEA business solutions through supported devices, iccwin is the application service for the Microsoft OS windows Includes windows servers windows clients and management tool",,,Client||On-Premise,,,,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100705,SM-100705,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114220
571891,iCheck in SE,iCheck Product Facts SE,ICHK,Active,Sweden,Food,Annica Carlsson,Linus Stenberg,,,,,,"

Used to check temperature in fridges in the stores.","

System to monitor temperature sensors in freezers. Cloud based.","

NA",,,,,,,Web browser,Food,,,,Invest,,,,,2023-Jan-24,SM-116515,SM-116515,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jan-24 13:05,SMC-571891
114226,iDesk,iDesk,IDESK,Retired,Service Management and Operations,End User Enablement and Productivity,,Ingela Möller,Emelie Ernegard,,,,,BMC Remedy based IT Service Management Tool,,,On-Premise,,,iDesk,,,,,,Commodity,Buy,Decommissioned,,,,2022-Dec-19,2020-Nov-06,SM-100712,SM-100712,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114226
114225,iDesk Application,iDesk Application,IDESKAPP,Retired,Service Management and Operations,End User Enablement and Productivity,,Ingela Möller,Emelie Ernegard,,,,,,,,On-Premise,,,iDesk,,,,,,,Buy,Decommissioned,,,,2022-Dec-19,2020-Nov-06,SM-100713,SM-100713,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114225
114228,iDesk Automation,iDesk Automation (BMC Atrium Discovery and Dependency Mapping),IDESKAUT,Retired,Service Management and Operations,End User Enablement and Productivity,,Ingela Möller,Emelie Ernegard,,,,,,,,On-Premise,,,iDesk automation,,,,,,Commodity,Buy,Decommissioned,,,,2022-Dec-19,2020-Nov-06,SM-100708,SM-100708,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114228
114224,iDesk Chat,iDesk Chat,IDESKCHA,Retired,Service Management and Operations,End User Enablement and Productivity,,Ingela Möller,Emelie Ernegard,,,,,,"

Chat capability built inhouse using a 3rd party SaaS solution, and customized to suit IKEA business needs. This solution is currently integrated with iDesk and will in future integrate with the new ESM solution replacing iDesk (BMC Remedy ITSM).&nbsp;",,On-Premise,,,,,,,,,,Buy,Decommissioned,,,,2022-Dec-19,2020-Nov-06,SM-100715,SM-100715,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114224
114230,iDesk Reporting,iDesk Reporting,IDESKREP,Retired,Service Management and Operations,End User Enablement and Productivity,,Ingela Möller,Emelie Ernegard,,,,,BMC Remedy based IT Service Management Tool - reporting,,,On-Premise,,,,,,,,,Commodity,Buy,Decommissioned,,,,2022-Dec-19,2020-Nov-06,SM-100716,SM-100716,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114230
114229,iDesk Supporting services,iDesk Supporting services,IDESKSUP,Retired,Service Management and Operations,End User Enablement and Productivity,,Ingela Möller,Emelie Ernegard,,,,,"iDesk Supporting Services is an IT Service that contains Application Services that supports IT Service iDesk iDesk Atrium Orchestrator Application Service, A tool that supports integrations and Automation",,,On-Premise,,,it support chat,,,,,,Commodity,Buy,Decommissioned,,,,2022-Dec-19,2020-Nov-06,SM-100711,SM-100711,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114229
114227,iDesk Translation,iDesk Translation,IDESKTRA,Retired,Service Management and Operations,End User Enablement and Productivity,,Ingela Möller,Emelie Ernegard,,,,,"

Translation Hub that assigns the most appropriate translator (eg. Google, Microsoft, Deep Live etc.). It has IKEA tone of language customized in tool, and profanity filter.&nbsp;","

The translation service is delivered using a 3rd party SaaS tool 'Intento'. The translation hub capability will not be migrated, only the integration with iDesk will move to new ESM solution. &nbsp;",,On-Premise,,,iDesk,,,,,,,Buy,Decommissioned,,,,2022-Dec-19,2020-Nov-06,SM-100718,SM-100718,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114227
114233,IDRS,IKEA Data Routing System,IDRS,Active,Data Integration and Middleware,Data and Integration,,Tusar Ranjan Pattnaik,Tusar Ranjan Pattnaik,,,https://confluence.build.ingka.ikea.com/display/IDRS/,,"

IDRS (IKEA Data Routing System) is an Infrastructure service that will add data replication capabilities to any Oracle database or filesystem/Sequential (flat file) / Transrouter ( MHS format ). It's simple and robust and has been used for more than 28 years at IKEA. It's used by more than 500 systems and is compatible with all currently running AIX, Linux, VMS and Oracle TS. Monitoring of IDRS transactions can be enabled through LogCheck as a separate service offering","

Oracle database and filesystem/Sequential (flat file) / Transrouter",,On-Premise,,Integration Enablement Systems,idrs,,None,,,Partly,Commodity,Make,Tolerate,,,,,2020-Nov-06,SM-100719,SM-100719,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114233
114404,IIP Datacache,IKEA Intergration Platform Datacache,IIPDC,Active,Data Integration and Middleware,Data and Integration,,Fely Vacalares Sabaldana,Fely Vacalares Sabaldana,,,https://confluence.build.ingka.ikea.com/display/CCBV,,"Data ingestion component for Fulfilment systems that consolidates BU information from CBD, product information from PCM, PIA, CRA, supplier matrix information from SCM and consolidates, transforms applying business logic and distributes this information to ISOM and Centiro",Oracle Fusion + Oracle RDBMS,,On-Premise,,IKEA Integration Platform||Integration Enablement Systems,oracle integration services,,None,,,,Commodity,Make,Eliminate,,,,,2020-Nov-06,SM-101002,SM-101002,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114404
114237,IKB,IKEA Kiosk Base,IKB,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,Infrastructure platform for Kiosk applications,,,On-Premise,,,ikea kiosk base,,,,,Minor,Commodity,Make,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100722,SM-100722,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114237
114177,IKEA Container Platform,IKEA Container Platform,CONTPLA,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Bonny Lindberg,Alvaro Jesus Vaquero Del Castillo,,,https://confluence.build.ingka.ikea.com/x/PuPACw,,"

Container based runtime orchestration platform. Commonly used in hybrid Cloud environments.","

RedHat Openshift container platform

A bit outdated documentation home:

https://confluence.build.ingka.ikea.com/x/eoBj

&nbsp;","

ccoe clh kubernetes, k8s",Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100663,SM-100663,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114177
114216,IKEA e-guides,IKEA e-guides,HANGEL,Active,Digital Workplace,End User Enablement and Productivity,Cyril de Avellar,Cyril de Avellar,Martin Strandmark,,,,,"E-learning authoring solution from Info-Caption (e-guides) used for quick support guides, learning journeys and learning contents. ",,IKEA e-guides,Central Private Hosting,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100701,SM-100701,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-08 11:07,2020-Nov-06 15:30,SMC-114216
114240,IKEA Inside Sharepoint,IKEA Inside (Sharepoint),INSSHARE,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,,,,,,Minor,Differentiating,Buy,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100727,SM-100727,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114240
114239,IKEA Inside Statistics,IKEA Inside Statistics (Webtrends),INSSTAT,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,"ikea inside statistics is a web statistic tool for IKEA Inside, to measure traffic and usage Users are mainly IKEA Inside Publishers",,,On-Premise,,,"Inside, Ingka News, Ingka Play",,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100728,SM-100728,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114239
114231,IKEA Internet TP,IKEA Internet TP,IITP,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,,,Collection of standards for web and mobile solutions,,,On-Premise,,,Content Delivery Network (CDN) TS,,,,,,Commodity,Buy,Decommissioned,,,,2023-Aug-28,2020-Nov-06,SM-100721,SM-100721,Technology System,,"

Replaced by systems in CMP Web",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114231
573995,IKEA MT,IKEA MT,IMACHT,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,Johan Sporre,Brona Nilsson,Johan Sporre,,,https://confluence.build.ingka.ikea.com/display/LIRTT/1.+IKEA+MT+Introduction,,"

IKEA MT is a SaaS solution which makes machine translation, solely on proprietary data.&nbsp;","

IKEA MT is a SaaS solution which makes machine translation, solely on proprietary data.&nbsp;",,,Digital Product,,,Global Language Services,,,,,,,,,,,,2023-Feb-03,SM-116533,SM-116533,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-05 11:48,2023-Feb-03 15:22,SMC-573995
114241,IKEA Password Manager,IKEA Password Manager,IPWMAN,Active,Identity and Access Management,Identity and Access Management,,Emily Millnert,Emily Millnert,,,https://confluence.build.ingka.ikea.com/display/EKR234IA/IKEA+Password+Manager+decommissioning,,Self-service password reset service. Only used by NIR and Inter suppliers. ,Self-service password reset sservice for user network account. All Ingka self-service password reset has been moved to Azure SSPR.  ,,On-Premise,,,hd knowledge and information tools,,,,,Strongly,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100726,SM-100726,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-05 09:15,2020-Nov-06 15:30,SMC-114241
114351,IKEA thin clients,IKEA thin clients,THINCLI,Active,Digital Workplace,End User Enablement and Productivity,,,,,,https://devices.ingka.com/index.php/thin-clients/,,"IKEA Thin Client is a solution built on Red Hat Enterprise Linux Desktop, designed to run on low-cost hardware from HP",,,Central Private Hosting,,,,,,,,Minor,Commodity,Make,Eliminate,,,,,2020-Nov-06,SM-100839,SM-100839,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:21,2020-Nov-06 15:30,SMC-114351
114357,IKEA Unicode Fonts,IKEA Unicode Fonts,UNICODEF,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

Old IKEA standard fonts, still used",,,Client,,,IKEA Software Tools,,,,,None,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100845,SM-100845,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114357
114248,IKEA Windows Terminal Server,IKEA Windows Terminal Server,IWTS,Active,Digital Workplace,End User Enablement and Productivity,,John Kalkbrenner,Ferenc Horvath,,,https://confluence.build.ingka.ikea.com/display/CE/IWTS6+Home,,"

Technology for running Terminal Server based applications centrally or in distributed sites. Set to sunset, to be replaced by way more modern and efficient VIAD technology.","

Distributed&nbsp;Terminal server based delivery model for delivering published applications&nbsp;allowing users to access applications and data on a remote computer&nbsp;over the network. IWTS technical platform is based on ICC infrastructure and&nbsp;Citrix&nbsp;Virtual Apps &amp; Desktops&nbsp;technology.&nbsp;","

cwe Microsoft TS",On-Premise,,,,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100736,SM-100736,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-15 03:54,2020-Nov-06 15:30,SMC-114248
577656,IKEABOOKING,IKEABOOKING,IKEABOOK,Active,France,Customer Information Management,Vasiliki MARKOPOULOU SUDRE,Florence DEGHAYE,,Florence DEGHAYE,Thomas FAUCOEUR,https://devices.minutpass.com/iframe.html?header=1&context=IKEA&configuration=2988&placeId=600,,"

This is the front end, customer facing which allow our customer to book an appointment with IKEA on various service area (Coaching, plan, order, resolution...)","

One configuration for each service and which gather all stores. The solution displays availability, The customer can pay online (if it's not a free service)

We can also ask customer question before the booking, and he is allowed to upload files (proof of payment, plan, pictures to illustrate the issues for after-sales...)",,,Platform,,,,,,Customer Meeting Point Web,,,,,,,,,2023-Feb-21,SM-116563,SM-116563,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Feb-21 15:43,SMC-577656
177166,iLert,iLert,ILERT,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,Karan Honavar,Christian Kullendorff,Karan Honavar,,,https://jira.digital.ingka.com/secure/ObjectSchema.jspa?id=17&typeId=1380&objectId=177166,,"iLert is a solution that enables all technology teams to go beyond alerting, covering basic needs to increase uptime, including uptime monitoring, alerting, incident comms, status pages and on-call management. The solution is also available through android &amp; ios apps to allow for seamless on call support.","

Hosted as a SaaS solution","

opi",Public Cloud,Platform,,,,,,,Minor,Commodity,Buy,Invest,,,,,2021-Sep-15,SM-105102,SM-105102,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-25 07:56,2021-Sep-15 10:00,SMC-177166
114236,ILMT,ILMT,ILMT,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Andreas Stehn,,,,,Licence Metric Tool ILMT provides the possibility to follow-up on the utilization of installed IBM software,"

Software system to collect accurate metrics for IBM software installed in IKEA landscape.",,Distributed Private Hosting||Central Private Hosting,Platform,,,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100717,SM-100717,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114236
528773,ILOFF Apps,Inventory Logistics Operations & Fulfilment Apps,ILOFFAPPS,Active,Store Fulfilment Operations,Fulfillment Locations,,Vince Vollebergh,Wadie Assal,Di Hercowitz,Liza Staravoitava,https://iloff.ingka.com,,"

A platform portal housing several applications from different INGKA&nbsp;domains, including ILO &amp; FF. In the future, the scope may increase and the platform will change its name to be more inclusive.

&nbsp;","

One of the most integrate components of the portal is the NX monoreposistory&nbsp;frodo&nbsp;which contains&nbsp;several Angular shell applications from the DSM domain, and externally linking to React applications in other domains.

The platform as a whole has features such as; (google) analytics, observability via sentry.io, in-house authorization/authentication service with multi-tenancy support, a developer application for viewing and testing OpenAPI/AsyncAPI requests, shareable Angular/React components published in npm packages, changelog generation, consistent UX following Skapa guidelines etc.",ILOFF Apps: current name of FE portal,,Platform,,,ILO & FF Portal ,,,,,,,,,,,,2022-Nov-30,SM-116457,SM-116457,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Nov-30 18:35,SMC-528773
115800,IMPA,IKEA Model Platform for Analytics,IMPA,Active,Data Integration and Middleware,Data and Integration,,Karan Honavar,Karan Honavar,,,https://confluence.build.ingka.ikea.com/display/IMPA/,https://impa.ingka.dev/,"

IMPA is a platform to enable IKEA data scientists/analysts to easily deploy Python models on GCP with a simple but professional front-end and make them available as self-service tools for internal stakeholders.

You can check out IMPA as an end user on https://impa.ingka.dev.","

The IMPA platform is built on various GCP services, mainly Google Kubernetes Engine, Cloud Storage, Cloud Function, and PubSub for hosting the frontend, model deployment, executing scenarios, and scenario result lifecycle mgmt. Models are developed in their own Github Cloud repositories and deployed to IMPA through Github Actions. Access management to model web apps is integrated with SSO through Azure AD.",,,,,,IMPA,Co-worker,,,Minor,Commodity,Make,Invest,,,,,2020-Dec-04,SM-101021,SM-101021,Technology System,,"

2021-03-29 Anna N: Info from Product Engineering manager Heidi that we will keep investing.",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Dec-04 14:23,SMC-115800
702202,IN-FY24-SERVICE-CHECK-BY-ZIPCODE,IN SERVICE CHECK By ZIPCODE,INSERVZIP,Active,India,Digital Platform and Core Infrastructure,Bhavana Jaiswal,Sandeep K,,,,,,"

We are soon launching online delivery in over 2600 new zipcodes on Feb 1, 2024. While availability and delivery options are visible on PIP, there is no way for us to show whether or not assembly and installation is available at these zipcodes, since India does not have ISOM 2C and these services are not integrated with online checkout.

We expect this to provide complete clarity to our customers on what all services are available at their zipcode in a single place. We also expect this solution to prevent increase in NVACs at RCMP from the new serviceable zipcodes.","

Static Data is stored in variera with zipcodes with available services like parcel and truck delivery, and assembly services support by partners.&nbsp;",,,System of Record,,,,,,Customer Meeting Point Web,,,,,,,,,2024-Jan-31,SM-116955,SM-116955,Technology System,,,,Created by smc-magic using ticket SYSCATSD-6348,,2024-Feb-02 16:54,2024-Jan-31 14:27,SMC-702202
114235,Infomatica Powercenter,Infomatica Powercenter,INFPOWER,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,On-Premise,,,,,,,,,Commodity,Buy,Decommissioned,,,,2022-Jun-02,2020-Nov-06,SM-100724,SM-100724,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114235
114234,Infrastructure Monitoring,Infrastructure Monitoring,INFRMON,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,,,"Technology for infrastructure monitoring, Infrastructure monitoring contains many self developed KM's",BMC Patrol,,,,,,,,,,,,Buy,Decommissioned,,,,2023-Feb-27,2020-Nov-06,SM-100725,SM-100725,Technology System,,"

Retired, phased out Patrol.",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114234
588673,Ingka Centres China API Gateway,Ingka Centres China API Gateway,ICAPIGTWCN,Active,Ingka Centres,,Daniel Demytrie,Sebastien Peirone,Sebastien Peirone,,,https://confluence.build.ingka.ikea.com/display/ICIPA,https://portal.azure.cn,Expose data and services,Microsoft Azure APIM,,Public Cloud,Platform,Ingka Centres Technology Stream,,Ingka Centres Integration Platform,Co-worker||Partner,,,None,Commodity,Make||Buy,Invest,,,,,2023-Mar-24,SM-106027,SM-106027,Technology System,,,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588673
588675,Ingka Centres CMP EU,Consent Management EU,ICCMPEU,Active,Ingka Centres,,Maksim Tuigunov,ANDRE CARDOSO,ANDRE CARDOSO,,,https://confluence.build.ingka.ikea.com/display/CONSENT,https://console.gigya.com/#/29753241/4_oEUknnPFW8CHaBNpW57yPw/sites/site-selector,Consent Management for customers,SAP Customer Data Cloud Consent Management,,SaaS,Platform,Ingka Centres Interact With Visitors,,,Customer||Partner||Co-worker,,,Minor,Differentiating,Buy,Invest,,,,,2023-Mar-24,SM-106029,SM-106029,Technology System,,,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588675
588649,Ingka Centres CMS EU,Content Management System EU,ICCMSEU,Active,Ingka Centres,,Maksim Tuigunov,Robi Morro,Maksim Tuigunov,,,https://confluence.build.ingka.ikea.com/display/iccms/,https://be.contentful.com/login,"

Content Managenebt system hosting the content for both b2b and  b2c products","

Platform as a service provided by Contentful",,Public Cloud,Platform,Ingka Centres Interact With CoWorkers,,Ingka Centres CMS Platform,Co-worker||Partner||Customer,,,Partly,Differentiating,Buy,Invest,,,,,2023-Mar-24,SM-106030,SM-106030,Technology System,,,,,Setting System Master Identifier reference for Technology System,2023-Nov-23 10:09,2023-Mar-24 09:22,SMC-588649
588668,Ingka Centres Customer Wi-Fi portal and analytics,Customer Wi-Fi portal and analytics,ICCUSTWIFI,Active,Ingka Centres,,Per Lorin,Per Lorin,Per Lorin,,,,https://analytics-wifi.net/#/login,,,,SaaS,Digital Product,Ingka Centres Technology Stream,,,Co-worker,,,Minor,Commodity,Buy,Tolerate,,,,,2023-Mar-24,SM-106008,SM-106008,Technology System,,,,,Setting System Master Identifier reference for Technology System,2024-Jan-16 17:38,2023-Mar-24 09:22,SMC-588668
588662,Ingka Centres Domain Management portal,Domain management portal,ICDM,Active,Ingka Centres,,Radek Válko,Radek Válko,Radek Válko,,,,https://login.cscglobal.com/cscglobal-login/public/login,,,,SaaS,Digital Product,Ingka Centres Technology Stream,,,Co-worker,,,Strongly,Commodity,Buy,Tolerate,,,,,2023-Mar-24,SM-106010,SM-106010,Technology System,,,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588662
588672,Ingka Centres Event Hub,Ingka Centres Event Hub,ICEVNTHUB,Active,Ingka Centres,,Daniel Demytrie,Sebastien Peirone,Sebastien Peirone,,,https://confluence.build.ingka.ikea.com/display/ICIPA,https://portal.azure.com,Real-time data ingestion service,Microsoft Azure Event Hub,,Public Cloud,Platform,Ingka Centres Technology Stream,,Ingka Centres Integration Platform,Co-worker,,,None,Commodity,Buy||Make,Tolerate,,,,,2023-Mar-24,SM-106024,SM-106024,Technology System,,,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588672
588671,Ingka Centres Global API Gateway,Ingka Centres Global API Gateway,ICAPIGTWGL,Active,Ingka Centres,,Daniel Demytrie,Sebastien Peirone,Sebastien Peirone,,,https://confluence.build.ingka.ikea.com/display/ICIPA,https://portal.azure.com,Expose data and services,Microsoft Azure APIM,,Public Cloud,Platform,Ingka Centres Technology Stream,,Ingka Centres Integration Platform,Partner||Co-worker,,,None,Commodity,Make||Buy,Invest,,,,,2023-Mar-24,SM-106026,SM-106026,Technology System,,,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588671
588666,Ingka Centres Global Data Platform,Global Data Platform,ICGDP,Active,Ingka Centres,,Daniel Demytrie,Gautam Gayatri,Daniel Demytrie,,Gautam Gayatri,https://confluence.build.ingka.ikea.com/display/ICGDP/,https://portal.azure.com,"Centralized repository of data, blending and sharing data with product consumers and business stakeholders",Microsoft Azure,,Public Cloud,Platform,Ingka Centres Technology Stream,,Ingka Centres Global Data Platform,Co-worker,,,None,Commodity,Make,Invest,,,,,2023-Mar-24,SM-106014,SM-106014,Technology System,,,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588666
588674,Ingka Centres IM EU,Ingka Centres Identity Management EU,ICIMEU,Active,Ingka Centres,Identity and Access Management,Maksim Tuigunov,Savvas Drakoulis,Savvas Drakoulis,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=428811842,,Authentification platform enabling users from different products to use multifactor authentifaction for sign-up and login.,"MS Azure b2c, sendgrid",,Public Cloud,Platform,Ingka Centres Technology Stream,,Ingka Centres Identity Management (ICIM),Co-worker||Customer||Partner,,,Minor,Differentiating,Make,Invest,,,,,2023-Mar-24,SM-106028,SM-106028,Technology System,,"

Added Architecture Area",,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588674
588664,Ingka Centres Local Data Hub China,Local Data Hub,ICLDHCH,Active,Ingka Centres,,Daniel Demytrie,Daniel Demytrie,Swathi Dhanisetty,,Anna Utkina,https://confluence.build.ingka.ikea.com/display/ICIPA,https://portal.azure.cn,"Regional Data hub, Locally compliant which needs to stay in the regional boundaries",Microsoft Azure China,China DataHub,Public Cloud,Platform,Ingka Centres Technology Stream,,,Co-worker,,,None,Commodity,Make,Invest,,,,,2023-Mar-24,SM-106012,SM-106012,Technology System,,,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588664
588677,Ingka Centres MallCoo Platform,MallCoo Platform,ICMCP,Active,Ingka Centres,,Maksim Tuigunov,Irven Jin,Irven Jin,,,https://iweof.sharepoint.com/:u:/r/teams/o365g_roadmaptest_itsemal/SitePages/CRM-CN.aspx?csf=1&web=1&e=qPHwvf,https://mp.mallcoo.cn/,"Grouping of platforms that includes Intelligent Management System CN, BI Report Analyze System, MallCoo Ingka Centres Identity Management CN, MallCoo Consent Management CN, Customer relationship management system CN and Content Management System CN","Mongodb, CentOs 7.6 , Windows Server 2016 Datacenter,wechat, Alicloud",,Public Cloud,Platform,Ingka Centres Interact With CoWorkers,,B2C Platform China,Co-worker,,,Minor,Differentiating,Buy,Invest,,,,,2023-Mar-24,SM-106045,SM-106045,Technology System,,,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588677
588670,Ingka Centres Service Bus,Ingka Centres Service Bus,ICSRVCBUS,Active,Ingka Centres,,Daniel Demytrie,Sebastien Peirone,Sebastien Peirone,,,https://confluence.build.ingka.ikea.com/display/ICIPA,https://portal.azure.com,Data routing and transfer across services and applications,Microsoft Azure Service Bus,,Public Cloud,Platform,Ingka Centres Technology Stream,,Ingka Centres Integration Platform,Co-worker,,,None,Commodity,Make||Buy,Tolerate,,,,,2023-Mar-24,SM-106025,SM-106025,Technology System,,,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588670
588676,Ingka Centres SMS Notifications,SMS Notifications,ICSMSN,Active,Ingka Centres,,Jesper Romell,Pernilla Bjurman,Pernilla Bjurman,,,,,,,,,,Ingka Centres Technology Stream,,,,,,,,,,,,,,2023-Mar-24,SM-106044,SM-106044,Technology System,,,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588676
588659,Ingka Centres Ultra DNS portal,Ultra DNS portal,ICUDNS,Active,Ingka Centres,,Radek Válko,Radek Válko,Radek Válko,,,,https://ingka.sso.ultradns.com,,,,SaaS,Digital Product,Ingka Centres Technology Stream,,,Co-worker,,,Strongly,Commodity,Buy,Tolerate,,,,,2023-Mar-24,SM-106009,SM-106009,Technology System,,,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 09:22,SMC-588659
624200,Ingka Data Market Place,Ingka Data Market Place,IDMP,Planned,Data Integration and Middleware,Data and Integration,,Nina Novakovic,Nina Novakovic,,,,,"

The Ingka data marketplace is a place where any data persona should have a seamless and same UI experience to search, find, and understand different types of data; technical, business, operational and social metadata.

Enabling the data marketplace requires a set-up of how to ingest, store, transform and expose different metadata, meaning the metadata backbone concept is the ""engine"" behind the front end.

With time, this will evolve into a production-ready technical service offering fulfilling the enterprise metadata management &amp; Data Marketplace capability of the&nbsp;Ingka Data Foundation.","

Currently, no implementation exists we are building the prototype of the Ingka Data Market Place from scratch using Fast API, Python, Vue and other modern technologies.",,,Platform,,,Data enablement,,,Data Integration and Middleware,,,,,,,,,2023-Jun-14,SM-116737,SM-116737,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3128,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jun-14 10:31,SMC-624200
114249,Ingka Play,Ingka Play,INGKAVID,Active,Co-worker Engagements and Insights,People,Henrik Åkerberg,Susanne Rolf,Emelie Lundh,Emmanuel Orduna,Anna Welander Tärneberg,https://confluence.build.ingka.ikea.com/display/PEMGT/Ingka+Play,https://ingkaplay.ingka.com/,"Ingka Play is our internal video platformwhere co-workers can explore and browse internal video content presented in different country channels and topics. Videos from Ingka Play can also be embedded on other sites such as mylearning and Inside.

Ingka Play should be the primary choice to store and distribute videos intended to reach the many co-workers, but it shall not be used for video content intended for only a small group of people such as a team.","Ingka Play is built on licensed product from Kaltura. Customizations and configurations are done by Internal communications team or requested from the vendor.
Vendor Portal: https://corp.kaltura.com/","

Previously named Ingka Videos",SaaS,Digital Product,,video cms,Internal Communication - Internal channels,Co-worker,Web browser,People Management,Strongly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100737,SM-100737,Technology System,,,,Removed Unknown Jira profile,Setting System Master Identifier reference for Technology System,2024-Feb-02 16:28,2020-Nov-06 15:30,SMC-114249
114238,ingkacom,ingkacom,INGCOM,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,Exernal coorporate website for Ingka,,,,,,,,,,,,,Make,Decommissioned,,,,2023-Aug-21,2020-Nov-06,SM-100720,SM-100720,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114238
486760,Insights Hub,Insights Hub,IHUB,Retired,Strategy and Business Planning,Finance and Tax,,Lakshmi Sridhar Movva,Dacian Horescu,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=*********,,"

Insights Hub would act as a single point of entry for all Data and Insights (modern web portal) AI-powered* search capabilities. It is a Community and collaboration platform and Integrates with existing Ingka platforms such as SMC, Data catalogue etc","

Will be built on GCP, will update more details soon",,Public Cloud,,,,,,,,Strongly,Innovating,Make,Decommissioned,,,,2023-Nov-06,2022-Jul-21,SM-116287,SM-116287,Technology System,,Created by script using data in Nursery,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Jul-21 11:37,SMC-486760
114244,Instant messaging,Instant messaging (Skype for Business),INSTMSG,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,SaaS,,,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100723,SM-100723,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114244
114176,Integration - CPI,Integration - CPI,CPI,Active,Data Integration and Middleware,Data and Integration,,,,,,,,,,,On-Premise,,,,,,,,,Commodity,Make,Eliminate,,,,,2020-Nov-06,SM-100664,SM-100664,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114176
611952,Integration SITI,Integration SITI,SITI,Active,Data Integration and Middleware,Data and Integration,Fely Vacalares Sabaldana,Fely Vacalares Sabaldana,Fely Vacalares Sabaldana,,,https://confluence.build.ingka.ikea.com/display/AI/Integration+Shared+IT+Infrastructure+-+SITI,,"

Integration Shared IT Infrastructure - SITI

The Integration SITI is a set of software components that are put together to support the Oracle Integration Platform in handling the lifecycle of an integration. It is composed of a variety of products:


Sonatype Nexus
Jenkins
GitLab - service discontinued
Redmine - service discontinued","

Integration Shared IT Infrastructure - SITI

The Integration SITI is a set of software components that are put together to support the Oracle Integration Platform in handling the lifecycle of an integration. It is composed of a variety of products:


Sonatype Nexus
[Open Source] A web based software (Maven) repository manager. &nbsp;It is used as a deployable artefact repository for Oracle Integration implementations.

Jenkins
[Open Source] A web based automation engine, suitable for assisting in achieving continuous integration, automated testing or continuous delivery
PROD,PPE/TEST,PTE

GitLab - service discontinued
[Open Source] A web based repository manager for Git (“On-premise”). &nbsp;It is used as Source Code management system for Oracle Integration implementations.

Redmine - service discontinued
[Open Source] A web-based project management and issue tracking tool. It allows users to manage multiple projects and associated subprojects (“On-premise”). It used for ticket and instance handling",,,Platform,Integration Enablement Systems,oracle integration services,Integration Products,,,,,,,Eliminate,2027-Dec-31,,,,2023-May-12,SM-116673,SM-116673,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2622,Setting System Master Identifier reference for Technology System,2023-Dec-27 09:39,2023-May-12 09:26,SMC-611952
566111,Intune mobile management,Intune mobile management,INTUNEMOB,Active,Digital Workplace,End User Enablement and Productivity,,Lukasz Sudak,Lukasz Sudak,,,,https://intune.microsoft.com,"

this service offering enables mobile devices for our frontline workers to have a personal device at hand when they need it. and easily access needed INGKA apps and resources wherever the worker are at, and it enables a work profile and a private profile to be used. The service offering includes secure management of the devices and security features to have a secure device.","

Managed mobile devices from Microsoft Intune",,SaaS,Platform,,,Frontline Workplace,,,,Minor,Commodity,Buy,Invest,,,,,2022-Dec-20,SM-116484,SM-116484,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:23,2022-Dec-20 07:41,SMC-566111
114243,Inventor Publisher,Inventor Publisher,INVPUBL,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise,,,,,,,,None,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100730,SM-100730,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114243
177164,iObserve,iObserve,IOBS,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Christian Kullendorff,Karan Honavar,,,https://confluence.build.ingka.ikea.com/display/ITOI/iObserve,,"

iObserve is an information management system that we develop to visually track, analyse and display key performance indicators (KPI), metrics and key data points to visualise the health of our technology products or key systems that critical to eCommerce. iObserve allows for customisation to meet the specific needs of different teams.","

iObserve is SaaS solution","

opi",SaaS,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2021-Sep-14,SM-105097,SM-105097,Technology System,,"

Created by script using data in Nursery",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Sep-14 17:30,SMC-177164
114242,IP Services,IP Services,IPSERV,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,,,"Provides services for address handling (DNS, DHCP), time synchronising, IP Plan and distribution of modules.",Efficient IP,"

cnn",On-Premise,,,network distributed sites,,,,,,,Buy,Decommissioned,,,,2023-Feb-27,2020-Nov-06,SM-100731,SM-100731,Technology System,,"

Retired and replaced by EIP entry",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114242
576872,IPM,IKEA Power Maintenance Platform,IPM,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Christian Kullendorff,Karan Honavar,,,https://ipm.ingka.com,,"

IPM&nbsp;is a custom-developed platform that will help teams to schedule, track, analyze and act during power maintenance of a store. PMP will help to boost up the process of stopping and starting critical services,servers 6 devices in a store&nbsp;esulting in fewer deadlines missed and reduced manual efforts and less operational load .&nbsp;&nbsp;","

IPM&nbsp;is a one click application to help the users to schedule, track, analyze and act on power maintenance activities which are integrated with multiple tools like NOWIT - ITSM, iLert - notification and ansible - automation with RBAC to execute the relevant operations.&nbsp;",,Public Cloud,Platform,,,,,,,Minor,Commodity,Make,Tolerate,,,,,2023-Feb-17,SM-116548,SM-116548,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Feb-17 08:06,SMC-576872
349209,ISE,Identity Service Engine,ISE,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Christian Winberg,,,,,"

Provides network access control functionality",Provides network access control functionality on Cisco wired and Wi-Fi distributed infrastructure.,CNI,Central Private Hosting,Platform,,,,,,,None,Commodity,Buy,Eliminate,,,,,2022-Feb-07,SM-105269,SM-105269,Technology System,,"

Created by script using data in Nursery

Be replaced by Clearpass&nbsp;",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2024-Jan-08 08:54,2022-Feb-07 15:41,SMC-349209
114679,iShop B2B,iShop (B2B Integration),ISHOPB2B,Active,Finance and Procurement,Finance and Tax,,Tomas Gustafsson,Ulrika Olsson,,,,,"

Routing message to and from iSHOP",,,On-Premise,Digital Product,,isi/edi,,,,,Partly,,Make,Eliminate,,,,,2020-Nov-06,SM-100324,SM-100324,Technology System,,,,Moved from Business Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:32,SMC-114679
114183,IT Delivery Wiki,IT Delivery Wiki,DELWIKI,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Aug-24,2020-Nov-06,SM-100669,SM-100669,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114183
114414,IT Security - Folder Encryption McAfee,"IT Security - Folder Encryption, McAfee",SECFENC,Retired,Cyber Security,Cyber Security,,Fredrik Åhlstedt,Fredrik Åhlstedt,,,,,"

Provides encryption capability for files/folders","

Provides encryption capability for files/folders",,On-Premise,Platform,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Sep-06,2020-Nov-06,SM-101017,SM-101017,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114414
114413,IT Security - SOC Professional Services,IT Security - SOC Professional Services,SECSOC,Retired,Cyber Security,Cyber Security,,,,,,,,"

Cyber Security Operations Centre",,,,,,,,,,,,,Buy,Decommissioned,,,,2023-Jun-08,2020-Nov-06,SM-101012,SM-101012,Technology System,,Martin PerssonAdded19/5 - Changed to Cyber Sec (prev CA - DpCi) after alignment with TCM,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114413
114246,IT Service desk Telephony,IT Service desk Telephony,ISDTEL,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Aug-24,2020-Nov-06,SM-100732,SM-100732,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114246
569889,iTads,iTads,ITADS,Active,Service Management and Operations,End User Enablement and Productivity,Israel SANCHEZ,Israel SANCHEZ,Israel SANCHEZ,Israel SANCHEZ,Israel SANCHEZ,https://confluence.build.ingka.ikea.com/x/zgIAEw,,"

iTAds is ticket dispatching tool used to assign tickets to the Service Desk agents which are in a specific&nbsp;activity.

iTads has a dispatching logic developed for the last two years and it is extremely adapted to the IT Service Desk needs.

The purpose of the tool is to enable an smart dispatch of the tickets that are assigned to the Service Desk queues in NowIT to the available agents according to Verint.

&nbsp;

iTADS - Tickets Dispatching System Home - iTADS - Tickets Dispatching System - Confluence (ikea.com)","

The tool&nbsp; is developed in c# and used in a windows desktop environment. We have one Dispatching tool by supporting Area. The tool needs access to the NowIT RestAPI to find and assign the tickets and access to Verint Schedules to find the available agents.

&nbsp;

iTADS - Tickets Dispatching System Home - iTADS - Tickets Dispatching System - Confluence (ikea.com)",,Client,Digital Product,Enterprise service management,,,,,,Minor,Commodity,Make,Tolerate,,,,,2023-Jan-12,SM-116504,SM-116504,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jan-12 15:26,SMC-569889
517263,ITCNTOS,IKEA Thin Clients NTOS,ITCNTOS,Active,Digital Workplace,End User Enablement and Productivity,,Lukasz Sudak,Lukasz Sudak,,,https://devices.ingka.com/index.php/thin-clients/,,"

This is the infrastructure replacing redahat for thin client / Sales desktop this is the software managing the thin clients",,,Central Private Hosting,,,,Frontline Workplace,,,,Minor,Commodity,Buy,Invest,,,,,2022-Oct-14,SM-116381,SM-116381,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:22,2022-Oct-14 11:26,SMC-517263
114247,ITF,IT Foundation,ITF,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Urban Martinsson,Urban Martinsson,,,https://confluence.build.ingka.ikea.com/display/AH,,"

IT Foundation technology platform","

Service Oriented architecture platform supporting development, deployment, testing and monitoring of Enterprise Business Components using a service bus called EBB.","

app",On-Premise,,,it foundation,IT Foundation,Co-worker,,,,Commodity,Make,Migrate,,,,,2020-Nov-06,SM-100733,SM-100733,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114247
114245,iTracs,iTracs,ITRACS,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Annika Ederfors Aronsson,,,https://confluence.build.ingka.ikea.com/x/UjevB,,"

DC&amp;P DCIM tool","

Tracs is a datacenter infrastructure management (DCIM) product used for managing deployed infrastructure in our central data centers.","

dcp dcim",Central Private Hosting,Platform,,facility data centre,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100729,SM-100729,Technology System,,"

Martin PerssonAddedMartin PerssonAdded
2021-05 iTracs is still used in DC&amp;P",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114245
114251,iTransition,iTransition,ITRANS,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,SaaS,,,itransition,,,,,,Commodity,Buy,Decommissioned,,,,2023-May-16,2020-Nov-06,SM-100734,SM-100734,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114251
114250,IWAD,IWAD,IWAD,Active,Digital Workplace,End User Enablement and Productivity,,Martin Anklam,Ferenc Horvath,,,https://confluence.build.ingka.ikea.com/display/CE/IWAD,,"IKEA Web Application Delivery, web portal for external applications The IWAD service provides access through a reverse proxy model to centrally hosted web applications to users located on the Internet and internally on ICN",,,On-Premise,,,,,,,,Strongly,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100735,SM-100735,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-15 03:44,2020-Nov-06 15:30,SMC-114250
577886,Java Runtime Environment,Java Runtime Environment,JRE,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Urban Martinsson,Urban Martinsson,,,https://confluence.build.ingka.ikea.com/x/H5dAAg,,"Unlicensed use of Oracle Java SE (Java Runtime) is to be removed as soon as possible, it must be replaced with any free open-source Java, suitable for the specific use case.","

The Java Runtime Environment (JRE) is&nbsp;software that Java programs require to run correctly. Java is a computer language that powers many current web and mobile applications. The JRE is the underlying technology that communicates between the Java program and the operating system.",,,Digital Product,,,Application Hosting,,,,,,,,,,,,2023-Feb-23,SM-116572,SM-116572,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-13 15:47,2023-Feb-23 15:27,SMC-577886
114403,Jenkins,"Jenkins Enterprise, Jenkins open-source",JENKINS,Retired,Engineering Services,Developer Enablement,,,,,,,,"

Orchestration Engine for delivery pipelines.",,,Public Cloud,,Continuous Integration,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Nov-15,2020-Nov-06,SM-101003,SM-101003,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114403
114402,Jira,Atlassian Jira Software,JIRA,Active,Engineering Services,Developer Enablement,Christofer Anderberg,Sajid Chaudhari,Kenneth Flatby,,,https://jira.digital.ingka.com/,https://confluence.build.ingka.ikea.com/x/kM96Cw,"Jira is an agile project management tool that supports any agile methodology.

Through agile boards and reports you can plan, track, and manage your agile software development projects from a single tool","Jira is part of Atlassian platform eco system.
Ingka is using DataCenter version of Jira.
Jira is hosted and maintained in AWS by the Atlassian Premium Partner Stretch Addera.
Jira has its own license tier solution.",Jira Digital,Public Cloud,,Planning & Collaboration,,Engineering Collaboration,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-101008,SM-101008,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Dec-21 16:54,2020-Nov-06 15:30,SMC-114402
588678,Jira Assets,Jira Assets,JIRAASSETS,Active,Engineering Services,Developer Enablement,Christofer Anderberg,Sajid Chaudhari,Kenneth Flatby,,,https://confluence.build.ingka.ikea.com/x/VZdrI,,"

Assets brings Enterprise asset and configuration management to Jira. Create any type of asset structure and use the automation framework for total control. Visualize dependencies and make qualified business decisions.","

Assets is Jira Service Management’s native asset and configuration management tool. It gives teams a flexible and dynamic way to track all kinds of assets and configuration items (CIs), enabling teams to easily link them to service requests, incidents, problems, changes, and workloads.","

Jira Insight",Public Cloud,Platform,,,Engineering Collaboration,Co-worker,Web browser,,Minor,Commodity,Buy,Invest,,,,,2023-Mar-24,SM-116605,SM-116605,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-22 11:18,2023-Mar-24 09:50,SMC-588678
114408,Jira Service Management,Atlassian Jira Service Management,JIRASD,Active,Engineering Services,Developer Enablement,Christofer Anderberg,Sajid Chaudhari,Kenneth Flatby,,,https://jira.digital.ingka.com/servicedesk/customer/portals,https://jira.digital.ingka.com/servicedesk/customer/portal/1/create/3912,"Jira Service Management is a helpdesk request tracker.

It lets you receive, track, manage and resolve requests from your team's customers.","Jira Service Management (JSM) is part of Atlassian platform eco system.
JSM is part of the core product Jira (DataCenter) and is hosted and maintained in AWS by the Atlassian Premium Partner Stretch Addera.
JSM has its own license tier solution for JSM Agents.","Jira Service Desk
Jira Service Management
Jira SD",Public Cloud,,Planning & Collaboration,collaboration services,,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-101004,SM-101004,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Dec-21 17:06,2020-Nov-06 15:30,SMC-114408
528633,Kata,Kata,KATA,Planned,IKEA for Business,Business to Business,Germán Retamosa,Germán Retamosa,Germán Retamosa,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?spaceKey=IFB&title=Kata,,"

Security and Privacy standards are mainly based on controls,&nbsp;NIST&nbsp;or&nbsp;CIS&nbsp;are really good examples to consider and a good reference to determine the maturity levels of teams. However, are they understandable and close to the product teams? Unfortunately no. Most of these controls are overwhelming for product teams, and this ends with its non-commissioning.

Cyber Assurance team&nbsp;wants to turn this trend around with a new proposal based on aggregated areas that make them easy to understand and apply to teams. The name of that proposal is&nbsp;Kata. However, in V&amp;S we want to take a step further and operationalize this proposal with some artifacts included in the different areas to speed up the adoption.

The only way to see this proposal's viability, feasibility, and affordability is by putting it into practice within teams and different maturity levels. Try, fail and learn. That's the reason why we're here.

While we were developing some products in B2B, we saw the need not only to create some documentation for specific artifacts or procedures in order to be maintainable but also to keep this documentation up-to-date and extensible to other supporting functions.

Some of the benefits that we obtained from this:Easily understand your product from newcomers and supporting functions.Accelerate privacy and security assessments.Increase the visibility and transparency of your product to stakeholders.","

The idea of Katalogen is based on documentation as code and open-source principles (ADR-10) where the single source of truth will be stored on&nbsp;Github&nbsp;and mirrored into Confluence and BigQuery through our CICD pipelines.

&nbsp;",,,Digital Product,,,,,,,,,,,,,,,2022-Nov-29,SM-116451,SM-116451,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Nov-29 14:10,SMC-528633
606047,Katalogen,Katalogen,CTRL,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Mike Christiansen,Frederik Janos Braun,,,https://confluence.build.ingka.ikea.com/display/CYAS/Katalogen,,"

Katalogen is a Cyber Security &amp; Privacy&nbsp;catalog based on NIST SP800-53 Security and Privacy Controls&nbsp;and includes&nbsp;refined controls which have been set to an&nbsp;Ingka context.","

Jira Insight Object Schema",,SaaS||Client,Digital Product,,,Cyber Assurance,,,,Minor,Innovating,Make,Invest,,,,,2023-Apr-05,SM-116620,SM-116620,Technology System,,"

Created by script using data in Nursery, changed to Tech System as this app doesn't qualify to be in Business System group... it enables business systems.",,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-05 09:37,SMC-606047
695260,Kreativ Analytics,Kreativ Analytics,KREATIVBI,Active,Explore,Data and Integration,Philip Guindi,Philip Guindi,Noah Kantrowitz,,,https://confluence.build.ingka.ikea.com/x/6A2LLg,,"

Domo is a data warehouse platform used to analyze Geomagical's product and business data, build charts, and provide dashboards to both the Geomagical and Ingka teams.","

Domo is a SaaS data warehouse (effectively being used as a data lake). It ingests from multiple data sources and synthesizes things into formats useful for dashboards and reports.","

Domo is the name of the product.",,,Geomagical,,,,,,,,,,,,,,2024-Jan-12,SM-116943,SM-116943,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4967,,2024-Jan-12 10:12,2024-Jan-12 10:05,SMC-695260
588688,KudoboardCA,Kudoboard in CA,KBCA,Active,Canada,Content and Inspiration,,Brian Berneker,Brian Berneker,,Guido DiCesare,https://www.kudoboard.com/,,"

IKEA's core values include Togetherness. By celebrating work and life events together, IKEA co-workers become more engaged and share in each others successes as team.

A solution that allows co-workers to provide feedback, praise, and celebratory comments for their peers during times of special events like anniversaries, birthdays, role changes, and others.","

A Kudoboard is an online tool that allows users to create and share a collaborative digital ""bulletin board"" for the purpose of expressing appreciation, congratulations, or well wishes to a specific person or group. It is typically used in a professional or personal setting to celebrate a milestone, anniversary, or special occasion.

Users can create a Kudoboard by adding a title, description, and cover image to a template and then inviting others to contribute messages, photos, or videos to the board. Contributors can use a variety of customization options, such as text formatting and media insertion, to personalize their messages.

Kudoboards can be shared via a unique URL or embedded on a website or social media platform. They are often used as a way to bring together a dispersed team or community and facilitate connection and appreciation among members.

The Product Owner is actually Taylor Pike as Valentina has left IKEA but his name was not selectable in the list.","

Kudoboard -&nbsp; online tool that allows users to create and share a collaborative digital ""bulletin board"".",SaaS,Digital Product,,,,Co-worker,Web browser,Store Co-Worker Experience,None,Differentiating,Buy,Tolerate,,,,,2023-Mar-24,SM-116609,SM-116609,Technology System,,"

Created by script using data in Nursery.

Business Owner will change to Diana Taylor but is not available in the list.",,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-24 20:25,SMC-588688
609756,Lajka,Lajka,LAJKA,Active,Engineering Services,Developer Enablement,,Per Täljemark,Martin Norin,,,https://lajka.dev.ingka.com/documentation,,"

It should be easy for users to submit feedback on a product or a service. The inspiration has come from the quick voting machines that can be found in retail stores on the way out, where it is a one click on a smiley button to get a satisfaction index.

With Lajka, we mimic the same behaviour and also allows the user to give feedback after they have registered a vote. With the data we can identify trends and find areas where we can improve.","

Lajka is a web based tool where a survey can be created and distributed to users.

It is running in Google Cloud in Cloud Run, so the cost is kept down by not having a running instance if we don't record any activities. The data is stored in Fire Store which is a noSQL database, to keep the cost down while maintaining full functionality.",,Public Cloud,Digital Product,Test Enablement,Test Platform,Test Enablement,Co-worker,Web browser,,Minor,Commodity,Make,Invest,,,,,2023-Apr-28,SM-116660,SM-116660,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2521,Setting System Master Identifier reference for Technology System,2023-Dec-20 17:15,2023-Apr-28 14:45,SMC-609756
114407,LCM365,LCM365,LCM365,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,https://confluence.build.ingka.ikea.com/x/Ir8vEg,,"

The purpose of the LCM 365 application is to consolidate data from different data sources to create LCM information and dashboards in Qliksense.

Managed by IT Asset &amp; Configuration Management team","

LCM365 is in the process of being decommissioned.

LCM 365 consists of the three parts of components

Part 1 is LCM 365 applications on prem servers which are:Linux Batch ServerDatabase Server with SQL DatabaseEOS data sourcesEOS file is stored at \\itseelm-lx0264.ikea.com\lcm365_share\&nbsp; and consist of following files:

Host.csv, si.csv, oseos.xlsx, mveos.xlsx, ikeats.xlsx, hweos.xlsx

Part 2 are components that are deployed in the Qliksense Platform owned by Qliksense governance team.

Part 3 are components that LCM 365 application loads and copies data fromReptoolADDMCMDB","

IT Asset &amp; Configuration Management",On-Premise,,Analytics & Insights,it capacity management (itcm),,,,,,Commodity,Make,Decommissioned,2021-Sep-30,"

No longer in use",,2023-Aug-28,2020-Nov-06,SM-101005,SM-101005,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114407
495417,Legal Hold Pro,Zapproved Legal Hold Pro,ZLHP,Active,USA,Identity and Access Management,,,,,,,,"

IKEA is implementing Legal Hold Pro (LHP), a cloud-based legal hold solution to replace its manual process. The system will allow the legal teams to manage legal hold activities related to litigation, investigation, data preservation and other legal matters.","

A key function of this software is the ability to create an Address Book in LHP that will consume data provided from HRIS systems internal to IKEA. This data will be POSTed to LHP via a CSV sent nightly through a API endpoint created for this task.",,,Digital Product,,local service (USA),,Co-worker,Stand alone,Identity and Access Management,Minor,Innovating,Buy,Tolerate,,,,,2022-Aug-15,SM-116311,SM-116311,Technology System,,Created by script using data in Nursery,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Aug-15 15:46,SMC-495417
114254,License Manager,License Manager,LICMAN,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,,,,,,,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Aug-28,2020-Nov-06,SM-100738,SM-100738,Technology System,,"

No longer in use",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114254
114253,Lingea Lexicon,Lingea Lexicon,LINGLEX,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise,,,IKEA Software Tools,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100739,SM-100739,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114253
114257,Linux TP,Linux TP,LINUXTP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Andreas Stehn,,,https://confluence.build.ingka.ikea.com/x/G5BvCQ,,Standardized Linux Platform,"

RedHat Linux, SOE, TS","

dcp",Central Private Hosting||Distributed Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100740,SM-100740,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114257
114252,LIP,Local Integration Point,LIP,Active,Data Integration and Middleware,Data and Integration,,Fely Vacalares Sabaldana,Fely Vacalares Sabaldana,,,https://confluence.build.ingka.ikea.com/display/AI/Local+Integration+Point+-+LIP,,,,,On-Premise,,Integration Enablement Systems||IKEA Integration Platform,oracle integration services,OIP,Co-worker,,,,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100741,SM-100741,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-01 14:19,2020-Nov-06 15:30,SMC-114252
399658,Loadrunner Enterprise,Loadrunner Enterprise,LOADRUNE,Retired,Engineering Services,Developer Enablement,,Nicholas Morgan,Martin Norin,,,https://lre.xwpinternal.ikeadt.com/,,"

LoadRunner is a software testing tool from&nbsp;Micro Focus. It is used to test applications, measuring system behavior and performance under load. LoadRunner can simulate thousands of users concurrently using application software, record and later analyze the performance of key components of the application.

&nbsp;",Enterprise level performance tool. Scheduled to be decommissioned 2024-01-31,"

LRE2021",Central Private Hosting,,Test Enablement,Test Platform,Test Enablement,Co-worker,,,Minor,Commodity,Buy,Decommissioned,2024-Jan-01,,,2024-Feb-05,2022-Jun-01,SM-116221,SM-116221,Technology System,,"

Created by script using data in Nursery",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Feb-05 07:54,2022-Jun-01 10:59,SMC-399658
607078,Local Extensions,Local Extensions,LOCALEXT,Active,Customer Meeting Point Web,Customer Meeting Point,Anette Kamf,Anette Kamf,Anette Kamf,,,https://confluence.build.ingka.ikea.com/x/9lYVE,,"

Local Extensions is one tool that can be used when adding a local solution to an existing page&nbsp;ikea.com.

If it is possible to add a local solution by building a fragment/module and add it to the site in another way, that is to be prefered since using local Extionsn might affect the Core Web Vitals.&nbsp;
&nbsp;","

Local Extensions loads the local solutions using&nbsp;ESI (Edge-Side Includes) with page targeting.

For further support reach us at slack:
#cmp-web-market-engineering-pub
&nbsp;",,,Digital Product||Platform,,,Market Engineering,,,,,,Make,Migrate,,,,,2023-Apr-13,SM-116631,SM-116631,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-13 11:57,SMC-607078
607079,Local Modules,Local Modules,LOCALMOD,Active,Customer Meeting Point Web,Customer Meeting Point,Anette Kamf,Anette Kamf,Anette Kamf,,,https://confluence.build.ingka.ikea.com/x/2SnlJQ,,"

The Local Modules technology stack provides a performance optimized way to create a fragment/module that can be seamlessly integrated into&nbsp;ikea.com&nbsp;(e.g. using content include in Publicera) while leveraging the Skapa framework for UI/UX consistency.When using Local Modules to develop a solution, you can benefit from the following advantages:Static fragments with hydration for optimal performance and core web vitals scoresFull access to Skapa componentsType safetyOOTB support for multiple market and language configurationsOOTB support for uploads to&nbsp;ikea.com","

Local Modules is built using Preact CLI, which allows for the creation of static fragments with hydration using SSG (static site generation) while producing significantly smaller bundle sizes compared to other SSG frameworks like NextJS. Preact also provides full support for Skapa components through the React compatibility layer while leveraging Preacts significantly smaller footprint and performance enhancements. The upload pipeline utilizes USC (upload service) and is pre-configured using GitHub actions. Typescript is used to improve code quality and maintainability by providing static type checking, better tooling support, to prevent common runtime errors.

For further support reach us at slack:
#cmp-web-market-engineering-pub",,,Platform||Digital Product,,,CMP/Web ME - infrastructure,,,,,,Make,Invest,,,,,2023-Apr-13,SM-116632,SM-116632,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-13 12:08,SMC-607079
509734,LPAR2RRD,LPAR2RRD,LPAR2RRD,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Patrik Johnsson,,,,,"

LPAR2RRD is a performance and capacity-monitoring analysis software for OS platform. It is able to monitoring any operating system running on certain platforms and collect input for showback / chargeback operations","

Performance and capacity management used for Power based host as well as other virtualised environments in IKEA Data Centers.",,Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2022-Sep-27,SM-116362,SM-116362,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Sep-27 16:09,SMC-509734
621479,M365D,Microsoft 365 Defender,M365D,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Matthew Itkin,Fredrik Åhlstedt,,,,,"

Microsoft 365 Defender is a unified pre- and post-breach enterprise defense suite that natively coordinates detection, prevention, investigation, and response across endpoints, identities, email, and applications to provide integrated protection against sophisticated attacks.

Here's a list of the different Microsoft 365 Defender products and solutions that Microsoft 365 Defender coordinates with:Microsoft Defender for EndpointMicrosoft Defender for Office 365Microsoft Defender for IdentityMicrosoft Defender for Cloud AppsMicrosoft Defender Vulnerability ManagementAzure Active Directory Identity ProtectionMicrosoft Data Loss PreventionApp Governance","

EDR and XDR Capability",,SaaS,Platform,,,,,,Cyber Security,Strongly,Innovating,Buy,Invest,,,,,2023-Jun-01,SM-116715,SM-116715,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2896,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jun-01 09:47,SMC-621479
613594,Mac Client Management Tool,Mac Client Management Tool,MCMT,Active,Digital Workplace,End User Enablement and Productivity,,Johan Johansson,Kristina Weberg,,,,,"

A system that enables us to enrol, manage, and report on mac clients.","

Jamf is a enterprise management solution for Apple devices, offering comprehensive toolsets for deployment, device management, mobile device management (MDM), and security. The platform enables IT teams to automate tasks and enforce compliance across a fleet of Mac based devices. Jamf ensures the business can leverage the full potential of their Apple ecosystem while maintaining optimal security and user experience.",,SaaS,Platform,,,Co-worker Clients,,,,Minor,Commodity,Buy,Invest,,,,,2023-May-17,SM-116680,SM-116680,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2661,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-May-17 14:34,SMC-613594
657787,MailChimp Integration in CA,MailChimp Integration in CA,MAILCMPCA,Active,Canada,Marketing,,,Brian Berneker,,Guido DiCesare,https://confluence.build.ingka.ikea.com/display/CANADAHUB/MailChimp+Domain+Intergration,,"

We would like to use mailchimp here in Canada for messaging campaigns. And would like to use a Custom Email Domain ending with ingka.ikea.com.

&nbsp;

Email campaigns are being used for internal employee communications. (new letters). External communications to media outlets. This tool allows us to be able to create emails campaigns track the clicks rates and manage all communications with advanced functionality that would be limited in Outlook.","

MailChimp is a web service that sends communication emails and helps you manage email campaign and lists.","

MailChimp - 3rd party web application",SaaS,Digital Product,Continuous Integration,,,Co-worker,Web browser,Store Co-Worker Experience,None,Commodity,Buy,Invest,,,,,2023-Sep-13,SM-116800,SM-116800,Technology System,,,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2024-Jan-18 11:25,2023-Sep-13 21:25,SMC-657787
141702,Managed GKE,GCP Managed Kubernetes,CLHCOMGKE,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Bonny Lindberg,Alvaro Jesus Vaquero Del Castillo,,,https://confluence.build.ingka.ikea.com/x/jZfsCQ,,"

Google public cloud managed kubernetes service for container runtime.","

GCP GKE service managed by internal cloud team.","

clh ccoe mgke gcp gke kubernetes k8s container",Public Cloud,Platform,,,,,,,Minor,Commodity,Buy,Invest,,,,,2021-Jun-10,SM-105028,SM-105028,Technology System,,"

Moved",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Jun-10 16:53,SMC-141702
114256,Mega,Mega,MEGA,Retired,Engineering Services,Developer Enablement,,,,,,,,Enterprise Architecture tool,,,,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Sep-27,2020-Nov-06,SM-100743,SM-100743,Technology System,,"

2021-11-22 Anders Pauli: According to Johan Valdemarsson the contract has been ended. But publishing the portal is still available. https://ikea-eaportal.azurewebsites.net/pages/63e07ff7554231b8.htm",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114256
686197,MEMCM,Microsoft Endpoint Manager Configuration Manager,MEMCM,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Mattias Persson,Fredrik Åhlstedt,,,,,"

Microsoft Endpoint Manager Configuration Manager (MEMCM) delivers robust endpoint protection policies tailored to safeguarding your organization's devices. It provides centralized control over security configurations, threat management, and compliance, ensuring a fortified defense against evolving cyber threats.&nbsp;","

MEMCM streamlines endpoint security operations, enabling businesses to implement and enforce effective protection measures while maintaining a secure digital environment.",,,Platform,,,,,,,,,,,,,,,2023-Dec-20,SM-116920,SM-116920,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4770,,2023-Dec-20 09:16,2023-Dec-20 09:16,SMC-686197
364298,Metal Model App,Metal Model App,MMA,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Björn Gullstrand,Gustav Lundsgård,,,https://confluence.build.ingka.ikea.com/x/aCIEG,,"

Application to help visualize non-functional controls and achievements.

More about Metal Model strategy and rating at https://confluence.build.ingka.ikea.com/display/MM/","

The metal model application is hosted on App-engine in GCP.

React frontend and a NodeJs (Fastify) backend.&nbsp;

PostgreSQL database.",,Public Cloud,Digital Product,,,Cyber Security and Privacy,,,,Minor,Innovating,Make,Invest,,,,,2022-Mar-18,SM-116148,SM-116148,Technology System,,"

Created by script using data in Nursery",,Moved from Digital Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Mar-18 11:17,SMC-364298
114255,MFA Defender,MFA Defender,MFSDEF,Active,Identity and Access Management,Identity and Access Management,Andreas Andersson,Emily Millnert,Emily Millnert,,,https://confluence.build.ingka.ikea.com/display/EKR234IA/One+Identity+Defender+decommissioning,,"On premises service for multi factor authentication. In the process of being retired, replaced by Entra ID MFA. ",,,On-Premise,Platform,,,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100744,SM-100744,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-05 09:21,2020-Nov-06 15:30,SMC-114255
114258,Microblogging,Microblogging (Microsoft Yammer),MBLOG,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100742,SM-100742,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114258
114259,Microsoft Azure,Microsoft Azure,MSAZURE,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Bonny Lindberg,Tony Yoo,,,https://confluence.build.ingka.ikea.com/x/iomlB,,"

Azure is a public cloud provider, used by Ingka as secondary cloud vendor (preferred by INTER) - main footprint is within EU.","

Azure features IaaS/PaaS/SaaS solutions for a very large set of services and features their own Marketplace","

ccoe, clh, Azure cloud",Public Cloud,Platform,,public cloud iaas TS,,,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100747,SM-100747,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114259
566110,Microsoft Hololens 2,Microsoft Hololens 2,HOLOLENS,Active,Digital Workplace,End User Enablement and Productivity,,Lukasz Sudak,Lukasz Sudak,,,https://devices.ingka.com/index.php/mixed-reality/,,"

Enables an Augumented reality platform to increase remote colloborations. Can also enable educations/instructions/onboard in an simple way.",,,Client,,,,Frontline Workplace,,,,Minor,Commodity,Buy,Invest,,,,,2022-Dec-20,SM-116483,SM-116483,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:24,2022-Dec-20 07:37,SMC-566110
114264,Microsoft Hyper-V,Microsoft Hyper-V,MSHYPER,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,,,Virtualization engine for x86 based workloads from Microsoft,Used both in distributed deployments,,On-Premise,,,,,,,,,Commodity,Buy,Decommissioned,,,,2022-Jul-14,2020-Nov-06,SM-100749,SM-100749,Technology System,,"

Set to retired 14/7 2022 - Martin P",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114264
114272,Microsoft SQL Server,Microsoft SQL Server,MSSQL,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Dinesh Adhikari,Dinesh Adhikari,,,,,"

Database technology from Microsoft used to persist, search and retrieve business data","

Relational database technology Platform from Microsoft",,Central Private Hosting||Distributed Private Hosting,Platform,,Database Platform Services,Databases Platform Services,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100759,SM-100759,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114272
622600,Microsoft Viva Insights Workplace Analytics,Microsoft Viva Insights Workplace Analytics,VIVAINS,Planned,Digital Workplace,End User Enablement and Productivity,,Maria Nyström,Maria Nyström,,,,,"

Microsoft describes Viva Insights as an “employee experience platform,” and it’s made up of four features, we will only focus on Viva Insights Workplace Analytics for this assessment:

Viva Insights Workplace Analytics: Data-driven, privacy-protected insights/reports to support health &amp; well-being and productive work (how we communicate and collaborate, how we meet, showing improvement areas), based on Office365 tools (Teams, Outlook, sharepoint, onenote, word). Also gives recommendations to improve productivity and wellbeing.

Individual insights (formerly MyAnalytics) [should be off by default]- an application designed to help employees to gain insight into how workers spend their time, with the goal of optimizing tasks and making them more efficient. Only the individual user sees the information and the text analytics part (which scans email for commitments) is triggered every time on request by the user. Individual Insights is assessed separately in OT ID 6986.

Manager insights - Provide much-needed visibility into work patterns that might lead to burnout and stress, such as regular after-hours work, meeting overload, or too little focus time. Managers can use these insights to help their teams strike a balance between productivity and wellbeing. (not activated now but could be in the future)

Leader insights - Help business leaders to address critical questions about organizational resiliency and work culture with insight into how work impacts their people and their business. These help leaders take steps to protect employee wellbeing and see opportunities where a change in process could improve business outcomes. (not activated now but could be in the future)

Viva Insights Workplace Analytics (formerly Workplace Analytics) [If user is part of the PoV the solution will be on by default]: provides actionable insights into the company's communication and collaboration trends to help you make more effective business decisions. Viva Insights Workplace Analytics analyzes how your teams work together so that you can identify the behaviors that create a good workplace, both for the individual and the business.

We want to do the proof of concept/trial/pilot to verify if the tools can help us :
•&nbsp;&nbsp; &nbsp;To measure office365 user adoption, to find improvement areas, analyzing whether we are and how are we using the tools , for possible license optimisations (eg less licenses = cost savings)
- to get insights on where we should put our focus (removal of licenses, and/or increase education/trainings, and/or communication)
•&nbsp;&nbsp; &nbsp;Get more effective, shorter and less number of meetings. We had over 14M virtual meetings FY21, with average time around 45-50 mins per meeting. Please note, physical meetings are not included!

Benefits for the business:
- if we could save 5 mins for 0.1% of all meetings, savings would be +2 FTE cost per year.&nbsp;
- if we could remove 0.01% meetings, savings would be +5 FTE cost per year, based on average size of 3, average length of 45-50 mins (please note, the meeting size is probably bigger, as we cannot measure people joining f2f in meeting rooms)
- large meetings, meeting tourists (eg not the right people in meetings), remove/avoid double/triple bookings and recurring meetings are also areas of improvements
•&nbsp;&nbsp; &nbsp;To measure communication and collaboration (including meetings) patterns globally, to find possible improvement areas - both between teams/departments but also in the way we lead
•&nbsp;&nbsp; &nbsp;In the area of health &amp; wellbeing, support Ingka going from reactive to a proactive mode&nbsp;
- Example: avoid getting people from being burnt out, seeing the trends early like working hours outside normal working hours.&nbsp;
The cost savings from this big, as the recovery time for a burnt out resource is long and costly, including adding backup person(s)

Benefits for the individual (MyAnalytics) (included also in the OT ID 6986 assessment):
- enables the individual to be come aware of the own work habits and steer to the behaviors the individual would like
- supports by keeping track of commitments and schedules time for focused activities and breaks
- support the individual to me come more effective and reduce the workload, as well as better maintaining their network.

Benefits for the individual (Viva Insights Workplace Analytics):
- helps the organization to take decision to reduce the workload by working more effectively&nbsp;
- enables the organization to act in case there are unhealth work situations and people are at risk of being burnt out
- helps to develop leader behaviors to better support the individuals needs

Because of the potential sensitivity about how data could be used, successful implementation and use of Viva Insights require careful thought and planning with regard to data protection.

Link to demonstration:
https://docs.microsoft.com/en-us/viva/insights/

Video about Viva insights
https://www.microsoft.com/da-dk/microsoft-viva/insights?rtc=1&amp;market=dk","

Part of the M365 platform.",,SaaS,,,,,,,Digital Workplace,Minor,Commodity,Buy,Invest,,,,,2023-Jun-07,SM-116726,SM-116726,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2928,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jun-07 08:22,SMC-622600
114261,MindManager,MindManager,MINDMAN,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

Mind map software from MindJet for Win and Mac",,,Client,,,IKEA Software Tools,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100745,SM-100745,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114261
565083,MLOPS,MLOPS,MLOPS,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Christian Kullendorff,Karan Honavar,,,https://iobserve-development-dot-ikea-itsd-ml.appspot.com/new-mlops/develop,,"

MLOps platform is an in-house built which is a one stop solution to Data Scientists to develop, deploy and observe their models. It helps the Data Scientists accelerate the deployment of machine learning models in production, improve the quality and reliability of these models, and reduce the time and effort required to manage and maintain them. By providing end-to-end visibility and control over the machine learning lifecycle, an MLOps platform can also enable organizations to respond quickly to changes and adapt to new business requirements.","

MLOps platform provides a comprehensive and integrated environment for developing, deploying, and&nbsp;observing&nbsp;machine learning models in production. It also support collaboration and automation across the machine learning lifecycle, and provide tools and processes for monitoring, observability, and governance of deployed models. By providing comprehensive observability of machine learning models, an MLOps platform can help&nbsp;users&nbsp;detect and prevent potential problems, and ensure that the models continue to perform optimally in production.",,Public Cloud,Platform,,,,,,,Partly,Differentiating,Buy,Invest,,,,,2022-Dec-14,SM-116475,SM-116475,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Dec-14 12:51,SMC-565083
114198,Mobility Management,Mobility Management (EMM),EMM,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,Mobile workplace platform for Personal and Shared devices,,,SaaS,,,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100683,SM-100683,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114198
375144,Mobility Master,Mobility Master,MOBMAST,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Christian Winberg,,,,,Management platform which enables cabeled and wireless functionality in IKEA aruba legacy locations,"

The Aruba Mobility Master is the master controller that can be either deployed as a virtual machine (VM) or installed on an x86-based hardware appliance. The Mobility Master provides&nbsp;simplified operations and enhanced performance.","

cnn&nbsp;",SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2022-Apr-12,SM-116186,SM-116186,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2024-Jan-08 09:04,2022-Apr-12 08:06,SMC-375144
114260,Module Adobe Live Cycle,ModuleAdobeLiveCycle_ES4_EN,MODADLC,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

Adobe Live Cycle Designer is a tool to create ie pdf as a form with fillable fields Like a web form but as PDF",,"

ModuleAdobeLiveCycle_ES4_EN",On-Premise,,,,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100746,SM-100746,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114260
573805,MongoDB Atlas,MongoDB Atlas Manage Service,MONGODB,Planned,Data Integration and Middleware,Data and Integration,,Dinesh Adhikari,Dinesh Adhikari,,,,,"

Full Service offer for MongoDB Atlas would allow product and engineering teams to roll out new features and functionalities.","

MongoDB Atlas - It is Fully managed global cloud database-as-a-service which offers data distribution and mobility across major cloud providers.",,,Platform,,,,,,,,,,,,,,,2023-Feb-02,SM-116530,SM-116530,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Feb-02 09:21,SMC-573805
114265,MS Exchange,MS Exchange,MSEXCH,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,Central Private Hosting,,,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100748,SM-100748,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114265
114263,MS Office,MS Office,MSOFF,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100750,SM-100750,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114263
114262,MS Office Mac,MS Office Mac,MSOFFMAC,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100751,SM-100751,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114262
114268,MS OneNote,MS OneNote,MSONENOT,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100756,SM-100756,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114268
114267,MS Outlook,MS Outlook,MSOUT,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100752,SM-100752,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114267
114266,MS Project inclSteelray Project viewer,MS Project inclSteelray Project viewer,MSPPV,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100753,SM-100753,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114266
114271,MS Visio,MS Visio,MSVISIO,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,Client,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100754,SM-100754,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114271
114269,MS Web,Microsoft Web,MSWEB,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Andreas Stehn,,,,,"

Middleware from Microsoft to run web applications","

IKEA packaged software from Microsoft to run web applications",,Central Private Hosting||Distributed Private Hosting,Platform,,central windows,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100755,SM-100755,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114269
621192,My rewards Middleware,My rewards Middleware,MRM,Active,Total Rewards,People,,Emil Danielsson,Catrin Tublén,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=519444492,,"

The purpose of the My rewards Middleware (MRM) is to create integrations for My rewards where the vendors cannot directly consume data from the different Data Hubs within Ingka.","

My rewards Middleware (MRM) is a cloud-native integration platform built on Google Cloud Platform (GCP) using a selection of its services including Cloud Functions, BigQuery, Pub/Sub, Cloud Storage, Compute Engine VMs, and Datastore. This solution streamlines data flows between various data hubs and vendors where vendors cannot dirctly consume APIs from data hubs. MRM is primarily built using Python, leveraging its efficiency and maintainability to support a robust infrastructure. All data flows within the system are actively monitored to ensure reliable data delivery. For infrastructure management and business continuity, we are using terraform.",,Public Cloud,Digital Product,,My rewards,HR_TR – Total Rewards and Mobility,None,Web browser,Total Rewards,Strongly,Differentiating,Make,Invest,,,,,2023-May-29,SM-116706,SM-116706,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2696,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-May-29 08:57,SMC-621192
114232,MYIDENTITY,Identity Governance and Administration,MYIDENTITY,Active,Identity and Access Management,Identity and Access Management,Andreas Andersson,Joakim Prahl,Joakim Prahl,,,https://myidentity.apps.ikea.com,https://myidentity.apps.ikea.com/,"

Identity Governance Platform supporting Ingka and Inter IKEA

http://myidentity.ikea.com/","

IGA platform from OneIdentity","

IGnA as backend tech platform",On-Premise,Platform,,Identity & access management,,,,,Strongly,Differentiating,Buy,Invest,,,,,2020-Nov-06,SM-100714,SM-100714,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-30 09:48,2020-Nov-06 15:30,SMC-114232
114270,MyIT Portal,MyIT Portal,MYITPORT,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,,,,,,,,Make,Decommissioned,,,,2023-Aug-24,2020-Nov-06,SM-100762,SM-100762,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114270
641266,MyPallet,MyPallet,MYPALLETDK,Active,Denmark,Delivery and Service Management,Christian Nilsson,Mariusz Diaczok,,,,,,"

Country requesting

Denmark

Description of Need (focus on WHAT and WHY)

We need a solution to track handling material as we will use fairly expensive material in our flows. Currently we have found no global solutions for this purpose and therefore have identified a solution used by one of our TSPs, MyPallet used by Bring, as fulfilling the need.&nbsp;https://mypallet.io/
The target group will be outbound teams in stores and CDCs as well as TSPs, both external and internal.

We have examined the current solutions used in order fulfilment, Centiro and DMP (Mover) and neither have the capability today.

Business Case

The solution is based on yearly a subscription fee, which for our market will be aprox. 7.000 EUR.
The cost per handling material is 190 EUR.
The solution would, looking solely at lost handling material, be cost neutral if we can prevent 37 lost handling material per year.
Added to this, this solution will save resources in monitoring and claim handling.

Business Stakeholder

Christian Nilsson

Digital Stakeholder

Anders Jensen

Business Value

Medium

Urgency

High

Product solving the Need

Fulfillment, Delivery and Services, (CFF)

Central or Country development

Country Development (standalone)

General comment

The solution in question is mypallet.io

&nbsp;

https://jira.digital.ingka.com/servicedesk/customer/portal/60/LNM-2784","

https://mypallet.io/",,,Digital Product,,,,Co-worker,Mobile App,Store Co-Worker Experience,Minor,,Buy,Tolerate,,,,,2023-Aug-18,SM-116788,SM-116788,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3607,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Aug-18 08:58,SMC-641266
114275,NAS File Share,NAS File Share,NASFS,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,David Johansson,,,https://confluence.build.ingka.ikea.com/x/4K4qBg,,"

DC&amp;P Network Attached Storage for central environments","

NetApp solution supported by Ontap management software.&nbsp;

Also provides the software Trident for k8s storage interface used in OpenShift Container Platform.","

dcp NAS",Central Private Hosting,Platform,,nas file share,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100757,SM-100757,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114275
349213,NCE,Network Cloud Engine,NCE,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Christian Winberg,,,,,"

Provides Network Access Functionality for China and Chinese sites","

See above. Software provided by Huawei.","

cnn",Central Private Hosting||Distributed Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2022-Feb-07,SM-105271,SM-105271,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Feb-07 16:36,SMC-349213
355418,Nebula,Nebula,NEBULA,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Bonny Lindberg,Ola Olsson,,,https://pages.git.build.ingka.ikea.com/sddc/docs/latest/,,"

Nebula &nbsp;Platform is a hosting platform for cloud native applications.&nbsp;Nebula is built&nbsp;in our Datacenters and CO-LO&nbsp;abstracting away the complex details and enabling developers to focus on what matters. Built by codifying the best practices shared by successful real-world implementations, Nebula solves the ""boring but difficult"" parts of deploying and managing cloud native services on-prem so you don't have to.","

Nebula is designed around a Kubernetes centric eco-system of software which makes up the platform.",,Central Private Hosting,Platform,,,,,,,Minor,Commodity,Make||Buy,Migrate,,,,,2022-Mar-02,SM-105311,SM-105311,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Mar-02 11:55,SMC-355418
114409,Network Intrusion Protection - Palo Alto,Network Intrusion Protection - Palo Alto,NWINTRU,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Ola Sandin,Fredrik Åhlstedt,,,,,"

Network Intrusion Detection and Intrusion prevention","

Network Intrusion Detection and Intrusion prevention",,On-Premise,Platform,,,,,,,Strongly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-101009,SM-101009,Technology System,,Martin PerssonChanged name to match excel8/4 Changed to CA,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114409
114278,Network Monitoring,Network Monitoring,NWMONI,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Venkatesh Narasingam Kuppusamy,Venkatesh Narasingam Kuppusamy,,,,,Technology for network monitoring,"

Highlight VES, WAN monitoring, ISE monitoring","

cnn",Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100761,SM-100761,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114278
114412,Nexus,Sonatype Nexus,NEXUS,Retired,Engineering Services,Developer Enablement,,,,,,,,"

Artifact repository/DML to store binaries, packages, containers etc that is a result of a build or recieved from a vendor.",,,Public Cloud,,Continuous Integration,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Nov-09,2020-Nov-06,SM-101006,SM-101006,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114412
583788,NGEN,Name Generating Service,NGEN,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Patrik Johnsson,Patrik Johnsson,,,,,"

The main tool being used in order to create unique names for central infrastructure equipment.&nbsp;","

Platform for providing name generation and life-cycle management for the names of infrastructure resources. Will provide an API.",,,Platform,,build environment,,,,,,,,,,,,,2023-Mar-09,SM-116585,SM-116585,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-09 09:46,SMC-583788
114274,NICE,NICE,NICE,Active,Digital Workplace,End User Enablement and Productivity,,Amandus Hammargren,Vera Janevska,,,,,"

NIce Robotics Connected to IT service Robotic Process Automation",,,On-Premise,,,robotic process automation,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100758,SM-100758,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-22 05:35,2020-Nov-06 15:30,SMC-114274
114273,NotePad ++,NotePad ++,NOTEPAD,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,Client,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Jun-05,2020-Nov-06,SM-100760,SM-100760,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114273
585800,NowPlatform,NowPlatform,NOWPLATFOR,Active,Service Management and Operations,End User Enablement and Productivity,,Eta Carlsson,Emelie Ernegard,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=387085069,https://nowit.ingka.com/sp?id=kb_article_view&sysparm_article=KB0020281,"

Enterprise Service management platform from ServiceNow&nbsp;","

Bought platform from ServiceNow.&nbsp;","

ESM platform",SaaS,Platform,Enterprise service management,,Enterprise Service Management,,,,Minor,Commodity,Buy,Invest,,,,,2023-Mar-16,SM-116598,SM-116598,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2024-Jan-10 10:33,2023-Mar-16 16:27,SMC-585800
114410,NTP,NTP (Non-INGKA Retailer Technical Platform),NTP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,,,Packaged solution sold to other franchisees,"

cnn",,On-Premise,,,,,,,,,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-101014,SM-101014,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114410
475956,Object Storage Platform,Cloudian Object Storage Platform,CLOBJSTO,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,David Johansson,,,,,"

Cloud storage platform (S3) for storing data for long term retention in a cost effective way,","

Object storage platform which can provide a S3 compatible interface to the storage infrastructure. Can provide archiving capabilities for long term retention of business data.",,Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2022-Jul-05,SM-116268,SM-116268,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Jul-05 08:12,SMC-475956
522534,Observera,Observera,OBSERVERA,Active,Customer Meeting Point Web,Customer Meeting Point,Anette Kamf,Anette Kamf,Anette Kamf,,,https://observera.ingka.com/,,"About​ 

Observera is a tool that collects data to serve as a basis in the ongoing work to improve the technical quality of local web solutions.

Value Proposition​

Observera helps local developers in the markets to monitor the quality of the local solutions they build. The tool tracks various metrics that might indicate problems with the solution that needs to be addressed. ​

Observera contributes to the Value Creation Goal Better Company by making it easier for developers to improve the website from a technical perspective, leading to a better site for customers. Observera also contributes to the Value Creation Goal Better Planet by making it easier for markets to remember old solutions. Decommissioning solutions leads to less data being transferred, which is good for the climate.

Customer/Co-worker missions and needs ​

Assists developers in the markets to keep track of all the local solutions they build.​

The market engineering team uses the tool to scan local solutions to find certain behaviours.  ​

Ensures IKEA.com is healthy by empowering co-workers. ​

Enables co-workers to develop and manage the data more efficiently and collaboratively, while also promoting transparency, performance, and knowledge sharing. ​

Simplifies the process of  understanding, developing and managing the code, making it easier for co-workers to work together and achieve their goals.","

A tool to overview all local extensions in hands of size, different metrics, last updated etc. to keep track of the repositories quality.&nbsp;",,,Digital Product,,,,,,,,,Make,,,,,,2022-Oct-28,SM-116409,SM-116409,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Oct-28 14:42,SMC-522534
114276,Office 2007 for Terminal servers,Office 2007 for Terminal servers,OF2007TS,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100763,SM-100763,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114276
114281,Office Templates for IKEA Trading,Office Templates for IKEA Trading,OFTEMPTR,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,Client,,,,,,,,Minor,Commodity,Make,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100771,SM-100771,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114281
114282,Office365,Microsoft Office 365,OFF365,Active,Digital Workplace,End User Enablement and Productivity,,Maria Nyström,Maria Nyström,,,,,"

Productivity and Collaboration platform",,,SaaS,,,,,,,,Partly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100764,SM-100764,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114282
115766,OIP Monitoring,Oracle Integration Platform Monitoring,OIPM,Active,Data Integration and Middleware,Data and Integration,,Dzumali Salmani,Dzumali Salmani,,,https://confluence.build.ingka.ikea.com/display/OIPM,,"

IKEA Integration Platfrom&nbsp;was built on Oracle Fusion middleware stack which includes SOA, OSB, ODI&nbsp;and OEM. OIP monitoring has covered all these areas to fetch required details from each platform.","

Using Oracle's Business Activity Monitoring (BAM)&nbsp;tools.","

BAM

IIP Monitoring",On-Premise,,IKEA Integration Platform,oracle integration platform monitoring (oipm),OIP Monitoring,Co-worker,,,,Commodity,Buy,Eliminate,,,,,2020-Dec-04,SM-101020,SM-101020,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Dec-04 08:14,SMC-115766
114295,OM,Output Managment,OUTMAN,Active,Data Integration and Middleware,Data and Integration,,Urban Martinsson,Urban Martinsson,,,https://confluence.build.ingka.ikea.com/display/DMS/,,"Outut Management (OM) is a system for document rendering, document dstribution (print, email, archive , FTP), sending email and SMS. For emails and SMS templates can be used in OM.",Cloud Run servicesin GCP (new) and on-prem (old) on both ITF and WebLogic and Oracle databases.,,On-Premise,,Customer Personal Data systems,output management,Output Management,,,,,,Make,Migrate,,,,,2020-Nov-06,SM-100777,SM-100777,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114295
621499,OMDM,Order Management Data Product,OMDM,Active,Order Management,Order Management,,Madhavi Tata,Peter Nilsson,,Yan Jia,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=134600975,,"

This product enables the availability of the data based on Data Product Architecture direction of INGKA. The datasets represented in this data product are inclusive of the products representing Order Management capability within Fulfilment.&nbsp;","

https://confluence.build.ingka.ikea.com/display/SOF/OMDM+Technology+Stack",,Public Cloud,Digital Product,Analytics & Insights,,Fulfillment Order Management Analytics,,,Order Management,Strongly,,,Invest,,,,,2023-Jun-01,SM-116717,SM-116717,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2303,Setting System Master Identifier reference for Technology System,2023-Nov-22 11:23,2023-Jun-01 14:33,SMC-621499
114415,Onetrust,Onetrust,ONETRUST,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Arjanit Kelmendi,Rima Khan,,,https://confluence.build.ingka.ikea.com/display/PRIVOPS/The+OneTrust+Handbook,https://app-eu.onetrust.com/,"

The Privacy Management Platform used by Ingka Group, Markets and Ingka Centers for various legal compliance documentation (e.g. GDPR, Privacy by Design and market data protection, privacy and information security compliance).","

Privacy Assessment Solution",,SaaS,Platform,,onetrust,,,,,Strongly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-101010,SM-101010,Technology System,,"

Martin PerssonAdded owner and Solution Area",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114415
485290,ONEVIEW,HPE OneView,ONEV,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Annika Ederfors Aronsson,,,,,"

An integrated IT infrastructure management software that automates IT operations, HPE OneView simplifies infrastructure lifecycle

&nbsp;

&nbsp;","

OneView is a converged infrastructure management platform that provides a unified interface for the administration of HPE hardware assets in a data center.

Used to manage Synergy hardware frames.

&nbsp;",,Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2022-Jul-14,SM-116282,SM-116282,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Jul-14 12:06,SMC-485290
114286,OpenVMS TP,OpenVMS TP,OPENVMS,Active,Store Fulfilment Operations,Fulfillment Locations,,Max Andersson,,,,,,"

Server platforms HPE used for MHS and CNS","

Covers several internal products, VMS for Alpha, VMS for CNS, VMS for MHS",,On-Premise,,,,,,,,,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100767,SM-100767,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114286
501342,OPlane,OPlane Threat Modeling,OTM,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Firas Dib,Gustav Lundsgård,,,https://confluence.build.ingka.ikea.com/x/nLdlEw,https://threatmodel.ingka.com/,"

Threat Modeling tool to help facilitate creation, management and data related to threat models of digital products within IKEA.","

Online web based threat modeling tool, running on Azure. Sign in using SSO.",,Public Cloud,Digital Product,,,,,,,Minor,Innovating,Buy,Invest,,,,,2022-Sep-07,SM-116333,SM-116333,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Sep-07 12:06,SMC-501342
114284,Oracle Database TP,Oracle Database TP,ORADB,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Dinesh Adhikari,Dinesh Adhikari,,,,,Database technology from Oracle,"

Linux based hosting platform for Oracle database software",,Distributed Private Hosting||Central Private Hosting,Platform,,Database Platform Services,Databases Platform Services,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100769,SM-100769,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114284
114283,Oracle Developer,Oracle Developer,ORADEV,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Nov-15,2020-Nov-06,SM-100770,SM-100770,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114283
114289,Oracle DevSuit,Oracle DevSuit,ORADEVSU,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Nov-15,2020-Nov-06,SM-100776,SM-100776,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114289
114277,Oracle Enterprise Manager Cloud Control,Oracle Enterprise Manager Cloud Control,OEM,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Mikael Rönnmark,Pontus Skog,,,https://confluence.build.ingka.ikea.com/display/DBMS/Services,,"

Management control for the IKEA Oracle environment","

Management platform for Oracle databases and middleware products",,Central Private Hosting,Platform,,DBMS - Database Management Services,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100768,SM-100768,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114277
114293,Oracle Golden Gate,Oracle Golden Gate,ORAGG,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Pontus Skog,Pontus Skog,,,https://confluence.build.ingka.ikea.com/display/DBMS/Services,,"

Database Replication and Migration tool for Oracle",,,On-Premise,,,DBMS - Database Management Services,,,,,,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100778,SM-100778,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114293
114280,Oracle Integration Platform,Oracle Integration Technical Platform,OIP,Active,Data Integration and Middleware,Data and Integration,,Tusar Ranjan Pattnaik,Tusar Ranjan Pattnaik,,,https://confluence.build.ingka.ikea.com/display/DATAINTEGR/,,"

IKEA Integration Platform (IIP) is dedicated to running integration flows between applications, other platforms and data hub. It is stateless, built using AIA principles and employs Service Oriented Architecture as a preferred integration paradigm","

Using components from the Oracle stack:Oracle SOA SuiteOracle Service Bus (OSB)Oracle Data Integrator (ODI)","

IKEA Integration Platform (IIP)

Oracle Integration Technical Platform (OITP)",On-Premise,,Integration Enablement Systems||IKEA Integration Platform,oracle integration services,OIP,Co-worker,,,,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100766,SM-100766,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-01 14:19,2020-Nov-06 15:30,SMC-114280
114292,Oracle SMP Database TP,Oracle SMP Database TP,ORASMPDB,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Mikael Rönnmark,Dinesh Adhikari,,,,,"

Oracle database standard based on AIX platform, also includes Oracle SMP Distributed TS","

AIX based Oracle Database Platform",,Central Private Hosting,,,,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100779,SM-100779,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114292
114291,Oracle Unified Directory,Oracle Unified Directory,ORAUD,Active,Identity and Access Management,Identity and Access Management,Andreas Andersson,Andreas Andersson,Andreas Andersson,,,,,Oracle database &amp; platform administration directory,,,On-Premise,Platform,Identity & Access Management,,,,,,None,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100775,SM-100775,Technology System,,,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114291
114290,Oracle Veridata,Oracle Veridata,ORAVERI,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Manohar Siddemsetti,Pontus Skog,,,https://confluence.build.ingka.ikea.com/display/DBMS,,"

Data Verification in replication scenarios",,,On-Premise,,,,,,,,,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100781,SM-100781,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-23 18:51,2020-Nov-06 15:30,SMC-114290
114287,Oracle WebForms,Forms & Reports,ORAFORMS,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Mikael Rönnmark,Urban Martinsson,,,,,"

Middleware from Oracle to run Webforms applications","

Java based runtime to run Webforms applications",,Central Private Hosting,Platform,,oracle middleware infrastructure service,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100773,SM-100773,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114287
574693,Orkestrera,Orkestrera,ORK,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Björn Gullstrand,Gustav Lundsgård,,,https://github.com/ingka-group-digital/Orkestrera,,"

Handles status of controls tied to systems.","

Add controls and status of controls to systems.",,Public Cloud,Digital Product,,,,,,,Minor,Innovating,Make,Invest,,,,,2023-Feb-07,SM-116540,SM-116540,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-10 13:27,2023-Feb-07 14:03,SMC-574693
505313,OSPO,Open Source Program Office,OSPO,Active,Engineering Services,Developer Enablement,Supriya Chitale,Supriya Chitale,Supriya Chitale,,,https://confluence.build.ingka.ikea.com/display/DevP/Open+Source+Program+Office,,"

The Purpose of this product is to provide end-users ability to download the open source libraries used in the Home Smart devices they bought or interested","

Open Source Program Office (OSPO) is web application developed for Public/customers to download the open source libraries used in devices made by IKEA. The User can view information about the devices and what open source code it uses.&nbsp;

The web architecture is based on 2 instances on Google cloud platform. Backend Instance is serving as API for devices information built in node.js and connected to mysql instance. The second Instance is Frontend built in React.js",,Public Cloud,Digital Product,,,Open Source Program Office,Customer,Web browser,,Partly,Commodity,Make,Invest,,,,,2022-Sep-13,SM-116338,SM-116338,Technology System,,"

Created by script using data in Nursery",,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Dec-22 11:13,2022-Sep-13 11:31,SMC-505313
568369,Out of Band Management,Out of Band Management,OOBM,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Tobias Johnsson,Johan Nordbeck,,,,,"

Used to deliver offline access to network equipment in case of a data center outage.","

Delivers secure in-band and out-of-band visibility and control to downstream equipment in enterprise data centers. This serial console system serves as a secure management gateway and single point of entry keeping you connected to critical IT equipment even in the event of partial or total data center outages.

Implemented using Avocent ACS",,,Platform,,,,,,,,,,,,,,,2023-Jan-03,SM-116494,SM-116494,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jan-03 13:48,SMC-568369
517111,Pactflow,Pactflow,PACTFLOW,Active,Engineering Services,Developer Enablement,,Leon Luu,Pär Svensson,,,,https://ikea.pactflow.io/,"

Pactflow is a software testing tool. It enables product teams to implement Contract Testing which will make their software development and testing more efficient.

Contract Testing is a concept to enable early testing of integrations between two applications or services. Primarily used for APIs, microservices and front-end/back-end applications.

Pactflow is part of the offering from Test Enablement team within Engineering Services in Digital Platforms &amp; Architecture.","

Pactflow is a SaaS application from SmartBear. It consists of two parts:

Pact Broker

To share and collaborate on contracts across teams. Upload and download of contracts are done from CI/CD pipelines using the Pactflow APIs.

Web UI

To view contracts and test results. Also used for administration of Pactflow users and teams. SSO is used for user authentication.",,SaaS,Digital Product,Test Enablement,Test Platform,Test Enablement,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2022-Oct-13,SM-116378,SM-116378,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Feb-05 10:27,2022-Oct-13 10:21,SMC-517111
370166,Panorama,Panorama,PNRMA,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Lage Frej,Johan Nordbeck,,,,,This management product enables implementation of secure network segments within our landscape providing internal and external protection of network perimeter.,"

Technology Product for managing and supporting the Palo Alto firewalls used within sites and also as border protection.","

cnn Palo Alto",SaaS,Platform,,,,,,,Minor,Commodity,Buy,Invest,,,,,2022-Mar-28,SM-116166,SM-116166,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2024-Jan-08 08:43,2022-Mar-28 17:49,SMC-370166
114299,Perfecto Mobile,Perfecto Mobile,PERFMOB,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Dec-21,2020-Nov-06,SM-100780,SM-100780,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-21 21:20,2020-Nov-06 15:30,SMC-114299
114300,Performance Center,Microfocus Performance Center,PERFCENT,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,On-Premise,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Mar-29,2020-Nov-06,SM-100785,SM-100785,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114300
614887,Polaris,Polaris,POLARIS,Retired,Cyber Security,Cyber Security,Pål Göran Stensson,Rajeev Kumar Jain,Gustav Lundsgård,,,,,"

Polaris is a SAST tool.

SAST inspects in-house developed&nbsp;application’s source code to pinpoint possible security weaknesses which if not resolved, could be exploited by an attacker.&nbsp;SAST&nbsp;comes into play early in the&nbsp;software development life cycle&nbsp;(SDLC), when fixing problems is both easier and less expensive.&nbsp;SAST is effective at finding many of the common weaknesses, such as&nbsp;cross-site scripting,&nbsp;SQL injection, and&nbsp;buffer overflows.","

https://ikea.polaris.synopsys.com/&nbsp;",,SaaS,Platform,,Polaris,Automated Security Testing,,,Cyber Security,Minor,Commodity,Buy,Decommissioned,,,,2024-Jan-05,2023-May-26,SM-116704,SM-116704,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2905,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:59,2023-May-26 11:30,SMC-614887
372311,Policy Compute Engine,Policy Compute Engine,PCE,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Tobias Johnsson,Johan Nordbeck,,,,,"

Product which enables&nbsp;application centric segmentations policies in our network. Allows to define application communication policies which focus around service and application flows rather than ip-addresses.","

Illumio Policy Compute Engine, is the brain of the micro-segementation. It is also combined with an agent which is deployed on each target server.

It allows to create&nbsp;an application dependency map and converts natural language policies into optimal stateful firewall rules for all workloads.

&nbsp;","

PCE",Distributed Private Hosting||Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Invest,,,,,2022-Apr-04,SM-116176,SM-116176,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Apr-04 14:00,SMC-372311
114297,PostgreSQL,PostgreSQL,POSTGRES,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Dinesh Adhikari,Dinesh Adhikari,,,,,"

Open-source relational database offering","

Relational database software as part of DBaaS offering",,Central Private Hosting,Platform,,Database Platform Services,Databases Platform Services,,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100788,SM-100788,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-30 08:01,2020-Nov-06 15:30,SMC-114297
501231,PostgreSQL Enterprise Manager,PostgreSQL Enterprise Manager,PEM,Retired,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Pontus Skog,Pontus Skog,,,https://confluence.build.ingka.ikea.com/display/DBMS/Services,,"

Software providing monitoring capabilities for the Postgres database estate on premise.","

Monitoring software for Postgres databases",,,Platform,,DBMS - Database Management Services,,,,,,,,,,,,2024-Jan-24,2022-Sep-06,SM-116331,SM-116331,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-24 15:20,2022-Sep-06 15:47,SMC-501231
152685,Power Platform,Microsoft Power Platform,PWRPL,Active,Digital Workplace,End User Enablement and Productivity,,Marius Ungureanu,Dino Omerhodzic,,,https://make.powerapps.com/home,,"

The Power Platform provides us&nbsp;with low-code app (mobile &amp; web) and low-code workflow capabilities, mainly targeting&nbsp;citizen developers who are using the platform to solve problems close to themselves. Typical apps are local products within countries or central solutions within specific functions or departments.","

The Power Platform is an Enterprise Low-Code Application Platform and in the INGKA context it consists of three products, Power Apps, Power Automate and Power Virtual Agents. All three products are backed by a built-in database called Dataverse, a multi-purpose AI component called AI Builder, and an integration component with over 400 connectors to other systems and products.",,SaaS,Platform,,Power Platform Management​,Power Platform CoE,,,,Minor,Commodity,Buy,Invest,,,,,2021-Jul-01,SM-105050,SM-105050,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-22 05:34,2021-Jul-01 14:22,SMC-152685
606982,Prestanda,Prestanda Engineering Metrics,PRESTANDA,Active,Customer Support and Returns,Customer Support,Patrik Holmqvist,Carl Sutton,Jimmy Bonney,,,https://prestanda.ingka.dev/,https://prestanda.ingka.dev/dashboard/,"

Prestanda give engineers an insight into the metrics of building their products. By connecting to JIRA, Github and GCP, it provides teams with information about their processes and costs and encourage reflection in order to mature the engineering capability.&nbsp;","

Prestanda is an application that connects to JIRA, Github, GCP, nowIT. It offers a dashboard for users to get insights into their products and team ways of working. It is serverless running on GCP to process the data and using Big Query to store it.",,Public Cloud,,,,,Co-worker,,,Minor,Differentiating,Make,Invest,,,,,2023-Apr-12,SM-116628,SM-116628,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-05 08:58,2023-Apr-12 15:34,SMC-606982
114303,Printers,Printers,PRINTERS,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100783,SM-100783,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114303
126598,PRISMA,PRISMA,PRISMA,Retired,Cyber Security,Cyber Security,,,,,,,,"

Abstract security and compliance view from multiple cloud tenants",,,SaaS,,,,,,,,,,Buy,Decommissioned,,,,2023-Jun-02,2021-Mar-25,SM-101081,SM-101081,Technology System,,"

PRISMA is superseded by SYSDIG",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Mar-25 15:38,SMC-126598
614628,Privacy Containers,Privacy Containers,PRC,Planned,Cyber Security,Cyber Security,Rima Khan,Panagiotis Vasilikos,Panagiotis Vasilikos,,,,,"

Privacy Containers will provide tooling for enchancing developers productivity when it comes to privacy assessments.","

Terraform Provider as a frontend and Google Cloud Cloud Run instances and API GW as backend.",,Public Cloud,Platform,,,Privacy Containers,Co-worker,,,Minor,Innovating,Make,Invest,,,,,2023-May-24,SM-116697,SM-116697,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2793,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-May-24 14:09,SMC-614628
668299,Privacy Enablement Dashboards,Privacy Enablement Dashboards,PRENREPORT,Active,Cyber Security,Cyber Security,,Rima Khan,Rima Khan,,,https://confluence.build.ingka.ikea.com/display/PRIVOPS/Privacy+Enablement+Dashboards,,"

Dashboard for reporting metrics for the core accountability areas for Privacy Enablement.","

Dashboard built using Microsoft PowerBI, fetching data from Ingka's privacy management platform Onetrust using jobs deployed in GCP Cloud Run and storing the data in BigQuery.",,,Digital Product,,,,,,,,,,,,,,,2023-Oct-16,SM-116830,SM-116830,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3768,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Oct-16 09:58,SMC-668299
114302,Process design and modelling,Process design and modelling,PROCDM,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,On-Premise,,,,,,,,,,Buy,Decommissioned,,,,2023-Dec-21,2020-Nov-06,SM-100790,SM-100790,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-21 21:20,2020-Nov-06 15:30,SMC-114302
114301,Project Room,Project Room (Projectplace),PROJROOM,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,SaaS,,,project room,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100791,SM-100791,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114301
523744,Prometheus DBMS,Prometheus DBMS,PROMDB,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Amit Verma,Pontus Skog,,,https://confluence.build.ingka.ikea.com/display/DBMS/PostgreSQL+-+Prometheus,,"

Tool to monitor metrics","

Tool will be used to monitor Database and Host metrics for alerting purposese&nbsp;",,,Platform,,DBMS - Database Management Services,,,,,,,,,,,,,2022-Nov-10,SM-116432,SM-116432,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-23 18:37,2022-Nov-10 08:04,SMC-523744
578572,ProtectionLS,Protection License Server,PLS,Retired,Engineering Services,Developer Enablement,,Venkata Yedida,Ferenc Horvath,,,,,"

Service Virtualization product that supports licenses for ServiceV Pro and VirtServer","

PLS is a Protection License Server for managing the licenses for system “SERVPRO“ (ServiceVPro and Virt Server).","

PLS",,Platform,Test Enablement,Test Platform,Test Enablement,,,,,,,Decommissioned,,,,2023-Dec-15,2023-Feb-28,SM-116575,SM-116575,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-15 14:28,2023-Feb-28 09:40,SMC-578572
507973,Pulse Insights,Pulse Insights - Customer Journey Surveys,PULSEINS,Planned,USA,Customer Meeting Point,Christine Cox,,,,,,,"

We are looking to partner with Pulse Insights
- Pulse Insights is a customer optimization platform that will help us Learn &amp; Act. We’ll be able to dynamically insert micro survey ""Pulses"" into the digital experience as ""content” so that it feels on-brand, in-context, and doesn’t interrupt the user’s journey.
- This project is intended to be a pilot. If successful we have opportunity to roll out more widely to other channels, journeys, markets, and audiences.

Usage:
o We will use the insights to inform product enhancements, optimize copy &amp; experience, inform product, services and pricing teams of new customer needs and plan for roadmap investments based on customer insights.
o Learning will support our current initiatives and goals including:
- Optimizing conversion and increasing AOV
- Reducing servicing costs including call volume
- Optimizing communications &amp; expectations re: supply chain and fulfillment
- Driving happy customer score – this technology complements, not replaces our existing capabilities

o ROI model:
- Lift conversion rate: conservatively 5 basis points
- Drive AOV conservatively by 5%
- Reduce returns rate and improve HCS (amount to be validated by POC)
- Improve operational efficiencies via targeted customer feedback about services pricing
- Improve product development investments with targeted insights at the product level

o Other Benefits:
- Opportunity and capability to grow our Zero Party Data (info users give to us explicitly) in an environment where 3rd party cookies are going away","

Vendor provided dynamic plugin displayed on IKEA.com DMPs, to provide contexual costumer suvery/feedback

&nbsp;

[11:45 AM] Maher Dwik

Where do we deploy?
Any page(s) that might have a survey, or
more broadly if you want to trigger based on
number of visits or Pageview depth.
Is it fast &amp; available?
Yes. Script is hosted at AWS Cloudfront and
servers are Google Cloud. Track each server
response time and watch it carefully.
Can we use Tag Mgr?
Yes, deploy via tag manager or deploy
directly on page.
Does it change?
Use the same script across pages,
environments. When working globally, you
have the option to use multiple tags or share.
Does it capture PII
It stores an anon ID in local storage which is
considered Personal Data/Information by
privacy laws.
What does it do?
Asks server if there’s a survey to render
Renders survey
Captures data and sends to server
Can we send user data?
Yes, we have methods to capture persistent ID
(identify), CRM data, or contextual data.
What do you capture?
Response, user ID, device type, URL, user
agent, number of Pageviews, number of
visits.",,,Digital Product,,local service (USA),,Customer,Web Extension,Customer Meeting Point Web,,,Buy,Invest,,,,,2022-Sep-20,SM-116349,SM-116349,Technology System,,"

Created by script using data in Nursery",,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Dec-01 11:41,2022-Sep-20 17:49,SMC-507973
114307,PuTTY,PuTTY,PUTTY,Active,Engineering Services,Developer Enablement,,Jan Magnusson,Niclas Strandéus,,,,,,,,Client,,,development workbench,,Co-worker,,,None,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100786,SM-100786,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-19 17:05,2020-Nov-06 15:30,SMC-114307
621201,PY Middleware,Payroll Middleware,PYMW,Active,Total Rewards,People,Katarzyna Sobolewska,Emilia Szczygielska,Sushil Ramsajeevan Shriwas,,,https://confluence.build.ingka.ikea.com/x/wKxAGw,,"

Middleware system to bring data from provider system to consuming Payroll related system.
Typical flow is to consume data from PP system domain and provide it into GV system.&nbsp;

Including business logic for Transforming data before entering it into consuming system.&nbsp;

Purely technical middleware system and no UX.&nbsp;&nbsp;

Countries Involved so far, Poland, Spain and US","

GCP middleware Integration solution with ETL scope.

Se System Home link for technical information.&nbsp;","

GCP Data middleware solution for Payroll capability area.",Public Cloud,Digital Product,,,HR_PY - Payroll ,None,Web browser,People Management,Strongly,Differentiating,Make,Invest,,,,,2023-May-29,SM-116707,SM-116707,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2693,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-May-29 11:21,SMC-621201
644531,QR Tool,PMP QR Generation Tool,QRT,Active,Store Co-Worker Experience,Store Operations and Experience,,Fabien Tuot,Ignacio Martín,,,https://qr.ingka.dev/,,"

A tool that generate a QR code from a url entered by the user and allows to see some analytics on the use of that code","

The core concept of the solution is to provide a complete self service “SaaS” where all users can get all the information they need without having to guess anything or contact anyone.&nbsp;

The service should be clear in what it does, predictable for the consumers and just work.&nbsp;

Ideally there would be no need for any support or operations, and if we just be able to leave it running once in a stable state.",,Public Cloud,Digital Product,,,QR Tool,,,,Strongly,,Make,,,,,,2023-Sep-04,SM-116797,SM-116797,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3481,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Sep-04 10:56,SMC-644531
695258,Radiance,Geomagical Radiance,RADIANCE,Planned,Find,Retail Range,Asheem Mamoowala,Paul Gauthier,Noah Kantrowitz,,,https://confluence.build.ingka.ikea.com/x/3g2LLg,,"

A prototype AI-assisted product browsing interface to allow customers to quickly and easily find the right IKEA products for their needs.","

An LLM chat bot that interfaces with the IKEA product catalog and search APIs.",,,Digital Product,Geomagical,,,,,,,,,,,,,,2024-Jan-12,SM-116942,SM-116942,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4665,,2024-Jan-12 10:10,2024-Jan-12 09:52,SMC-695258
672194,Range Foundation,Range Foundation for PMP range products,RAFO,Active,Real Estate and Expansion,Real Estate and Expansion,Grzegorz Bedra,,Dacian Horescu,,,,,"

Range Foundation is a digital product that provides a shared data processing and infrastructure support for Range products. Will be working with sales data, range data, geospatial data, consumer insights etc.","

Range Foundation is a GCP project that connects to internal and external sources to provide data to PMP &amp; range related product teams.",,,System of Truth||Digital Product,,,Range Foundation,,,Real Estate and Expansion,,,,,,,,,2023-Oct-20,SM-116836,SM-116836,Technology System,,,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2024-Feb-02 17:02,2023-Oct-20 09:00,SMC-672194
114416,Rapid 7 Vulnerability Scan,Rapid 7 Vulnerability Scan,VULNMAN,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Omar Benbouazza,Martin Svensson,,,,,"

Vulnerability Management Solution",,,Public Cloud||SaaS,Platform||Digital Product,,production security services,,,,,Partly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-101016,SM-101016,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114416
114306,RDT,Handheld Co-worker Device (RDT),RDT,Active,Digital Workplace,End User Enablement and Productivity,,Lukasz Sudak,Lukasz Sudak,,,https://devices.ingka.com/index.php/mobile-devices/what-is-shared/shared-device-offering/,,Enables our frontline workers to perform their customer and operational tasks on the go using a multi-purpose enterprise rugged handheld device that can be delivered as either personal and shared.,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100793,SM-100793,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:04,2020-Nov-06 15:30,SMC-114306
114213,Red Hat Directory Server,IAM - RHDS (red hat directory server),RHDS,Active,Identity and Access Management,Identity and Access Management,Andreas Andersson,Joakim Prahl,Joakim Prahl,,,https://cdsweb.apps.ikea.com,,"

Directory Service","

Directory Service",,On-Premise,Platform,Identity & Access Management,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100699,SM-100699,Technology System,,,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2024-Jan-30 09:50,2020-Nov-06 15:30,SMC-114213
114308,Refactor NET,Refactor NET,REFNET,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,Client,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Dec-21,2020-Nov-06,SM-100792,SM-100792,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-21 21:19,2020-Nov-06 15:30,SMC-114308
114310,Reflection Desktop,Reflection Desktop,REFLDT,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

Used for supporting RWEB sessions, eg MHS superusers",,,On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100795,SM-100795,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114310
114304,Reflection for Desktop X,Reflection for Desktop X,REFLDSX,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

For remote access on Unix/Linux servers in the halls",,,On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100789,SM-100789,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114304
114309,Reflection for the Web,Reflection for the Web (RWEB),REFLWEB,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100796,SM-100796,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114309
114314,Remote Access,Remote Access,REMACC,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Tobias Johnsson,Johan Nordbeck,,,,,"

Providing Client VPN access to IKEA Corporate Network","

Cisco Anyconnect and Cisco ASA (Adaptive Security Appliance)","

cnn",Central Private Hosting,Platform,,remote access,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100802,SM-100802,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114314
629817,Remote Access External VPN,Remote Access External VPN,RAVPNEXT,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Eric Belinsky,Venkatesh Narasingam Kuppusamy,,,,,"

Provides secure access to the IKEA network/resources while working remotely for Non-ICC Client Users.&nbsp;","

Provides secure access to the IKEA network/resources while working remotely for Non-ICC Client Users.&nbsp;",,,Digital Product,,,,,,,,,,,,,,,2023-Jun-26,SM-116758,SM-116758,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3115,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jun-26 10:24,SMC-629817
114311,Remote Management Portal,Remote Management Portal,RMP,Active,Digital Workplace,End User Enablement and Productivity,,Tyler Zhou,Ferenc Horvath,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=298750793,,The Remote Management Portal (RMP) responsibility is to support user initiated access to perform infrastructure administration activities on the Admin network.,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100799,SM-100799,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-04 03:57,2020-Nov-06 15:30,SMC-114311
114313,Resource Booking Tool,Resource Booking Tool,RESBT,Active,Digital Workplace,End User Enablement and Productivity,,Ivan Garro,Maria Nyström,,,,,,,"

AskCody",SaaS,,,,,Partner||Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100797,SM-100797,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-23 10:01,2020-Nov-06 15:30,SMC-114313
114165,Responsible Disclosure - BugBounty,Responsible Disclosure - BugBounty,RDBB,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Omar Benbouazza,Martin Svensson,,,,,"

Responsible Disclosure program to allow external security researches to inform us, in a responsible way, of vulnerabilities in our services - see bugs.ikea.com for more info","

SaaS Service via HackerOne",,SaaS,Digital Product,,it security,,,,,Partly,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100652,SM-100652,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114165
114312,Revit Architecture,Revit Architecture,REVARCH,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,Cad tool to replace AutoCAD Architecture for the IKEA Store Planners - not replacing AutoCAD in general,,,On-Premise,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100798,SM-100798,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114312
509731,RH Satellite,Red Hat Satellite,RHSAT,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Patrik Johnsson,,,,,"

Control Plane for managing Linux as part of deliveries to business systems","

Control and management plane for controlling and managing the IKEA Linux estate from Red Hat.","

Linux SITI",Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2022-Sep-27,SM-116360,SM-116360,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Sep-27 16:00,SMC-509731
114317,RSM,RSM (Rational Software Modeller),RSM,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,Client,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Jun-05,2020-Nov-06,SM-100805,SM-100805,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114317
475954,Rubrik Data Protection Platform,Rubrik Data Protection Platform,RBRKDPP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,David Johansson,,,,,"

Provides data protection capabilities for operational business data.&nbsp;","

Backup and data protection platform which provides backup and restore capabilites for mainly on-premise hosting services.&nbsp;

This entry also incapsulates the Rubrik Cloud Data Management and Rubrik Polaris software

&nbsp;",,Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Invest,,,,,2022-Jul-05,SM-116267,SM-116267,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Jul-05 07:43,SMC-475954
498589,Safeguard,Enterprise Privileged Access Management,EPAM,Active,Identity and Access Management,Identity and Access Management,Andreas Andersson,Daniel Fors,Andreas Andersson,,,,,"

Privileged Access Management offers additional layers of security around accounts and assets (servers etc).","

Privileged Access Management offers additional layers of security around accounts and assets (servers etc).

It offers password vaulting, session recording, keystroke banning and JIT access, amongst many things","

Old SMC name - Privileged Access Management",On-Premise,Platform,,,Identity & Access Management,,,,Strongly,Innovating,Buy,Invest,,,,,2022-Aug-24,SM-116321,SM-116321,Technology System,,Created by script using data in Nursery,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Aug-24 18:29,SMC-498589
577849,Samsung Knox,Samsung Knox,SMSUNGKNOX,Active,Digital Workplace,End User Enablement and Productivity,,Lukasz Sudak,Lukasz Sudak,,,https://central.samsungknox.com/,,"

A System that enables us to enroll Samsung devices to our different MDM Systems","

Knox Mobile Enrollment (KME) streamlines the initial setup and enrollment of corporate-owned and employee-owned devices.

KME is the recommended tool when an enterprise requires bulk device enrollment with little configuration variance amongst the devices deployed.",,SaaS,,,,Frontline Workplace,,,,Minor,Commodity,Buy,Invest,,,,,2023-Feb-23,SM-116570,SM-116570,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:26,2023-Feb-23 09:36,SMC-577849
657784,Sangoma Softphone,Sangoma Softphone,SANGSPHONE,Active,Canada,Customer Meeting Point,Anthony Sabatini,Anthony Sabatini,Brian Berneker,,,https://confluence.build.ingka.ikea.com/display/CANADAHUB/Sangoma+Softphone,,"

RCMP needed a system to enable co-workers working from home to receive calls made to the administrative&nbsp;line or the RCMP (************). This is used to reach BR&amp;C, the Duty Manager, the Absence line, Facilities, and IT co-workers.

https://jira.digital.ingka.com/browse/LNM-3267
This is the need originally created for MicroSIP but then replaced with Sangoma SoftPhone.","

Its a telephony software system that receives calls by connecting to the Freepbx telephony system.","

SANGSOFTPHONE&nbsp; -

SANG is the abrivated name of vendor SANGOMA.
SOFTPHONE is a description of the product.
&nbsp;",SaaS,Digital Product,,,,Co-worker,Stand alone,Customer Meeting Point App,None,Differentiating,Buy,Invest,,,,,2023-Sep-13,SM-116799,SM-116799,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3761,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Sep-13 20:31,SMC-657784
474803,SAP BODS,SAP Business Object Data Services,SAPBODS,Active,Business Support Foundations,Finance and Tax,,Rama Satya Srinivas Varma Ganapathiraju,Rama Satya Srinivas Varma Ganapathiraju,,,,,"

SAP BO Data Services (BODS) is an ETL tool used for data integration, data quality, data profiling and data processing. It allows you to integrate, transform trusted data-to-data warehouse system for analytical reporting.","

Hosted on Azure, Bought software from SAP",,,,SAP Finance Systems,,,,,Business Support Foundations,Strongly,Commodity,Buy,Invest,,,,,2022-Jul-01,SM-116251,SM-116251,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Jul-01 11:32,SMC-474803
518309,SAP CAR,SAP Customer Activity Repository,SAPCAR,Active,Finance and Procurement,Finance and Tax,,Purohit Jagadis B,Vaibhav Sharma,,,,,"

SAP Customer Activity Repository (CAR) is a powerful data platform that collects, cleanses, and centralizes customer and transactional data previously spread in different formats over separate applications.","

SAP CAR is a Paas product provided by SAP, hosted on Azure as IaaS service provider and managed by INGKA in collaboration with managed service provider capgemini",,,,SAP Finance Systems,,,,,,,,Buy,Invest,,,,,2022-Oct-19,SM-116389,SM-116389,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Oct-19 10:19,SMC-518309
518311,SAP CIG,SAP Ariba Cloud Integration Gateway,SAPCIG,Active,Business Support Foundations,Finance and Tax,,Prasanna Sri Guru,Rama Satya Srinivas Varma Ganapathiraju,,,,,"

SAP Ariba Cloud Integration Gateway(CIG) accelerates the integration process by making it simple to connect trading partners, SAP Ariba cloud solutions like Ariba Network, Ariba Sourcing, Ariba Buying, Ariba Contract Management etc. and customer back-end systems like SAP ERP or SAP S/4HANA.","

Saas Service provided by SAP",,,Digital Product,SAP Finance Systems,,,,,,,,,,,,,,2022-Oct-19,SM-116391,SM-116391,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Oct-19 11:11,SMC-518311
474799,SAP GRC,Governance Risk Compliance - Process control/IC,SAPGRC,Active,Business Support Foundations,Identity and Access Management,,Amit Nighojkar,Amit Sudhir Kumar Jain,,,,,"

SAP GRC (governance, risk, and compliance) is a set of solutions and products that help you manage enterprise resources in a way that minimizes risk, builds trust, and lowers compliance costs.","

Hosted in Azure , bought software by SAP",,Public Cloud,Digital Product,Identity & Access Management||SAP Finance Systems,,,Co-worker,,,Strongly,Innovating,Buy,Invest,,,,,2022-Jul-01,SM-116247,SM-116247,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Jul-01 11:00,SMC-474799
518303,SAP IAS,SAP Identity Access Service,SAPIAS,Active,Business Support Foundations,Identity and Access Management,,Amit Nighojkar,Amit Sudhir Kumar Jain,,,,,"

Leverage existing identity providers for secure authenthication and benefit from native cloud application integration
* Single sign-on for browser-based applications (cloud and on-premise)
* Different authentication options
* Different user store integration scenarios","

SAP IAS is a SaaS service provided by SAP hosted on SAP's business technology platform .&nbsp;",,Public Cloud,Platform,Identity & Access Management||SAP Finance Systems,,,,,,Minor,Commodity,Buy,Invest,,,,,2022-Oct-19,SM-116384,SM-116384,Technology System,,"

Created by script using data in Nursery ,, moved to correct Architecture Area",,,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Oct-19 09:49,SMC-518303
474801,SAP IDM,SAP Identity Management,SAPIDM,Active,Business Support Foundations,Identity and Access Management,,Amit Nighojkar,Amit Sudhir Kumar Jain,,,,,"

SAP NetWeaver Identity Management is a tool used to manage the full identity life cycle of users - from joining an organization to moving across positions inside the organization.","

Hosted on Azure , bought software from SAP",,Public Cloud,Platform,Identity & Access Management||SAP Finance Systems,,,,,Business Support Foundations,Strongly,Commodity,Buy,Invest,,,,,2022-Jul-01,SM-116249,SM-116249,Technology System,,"

Created by script using data in Nursery... changed Architecture area and system group",,,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Jul-01 11:22,SMC-474801
474804,SAP IPS,SAP Identity Provisioning,SAPIPS,Active,Business Support Foundations,Identity and Access Management,,Amit Nighojkar,Amit Sudhir Kumar Jain,,,,,"

IPS offers a simple and secure approach to identity lifecycle management in the cloud. It allows you to provision and de-provision users and their authorizations to cloud business applications by simply re-using identity data from an existing central user store.","

SaaS system hosted on SAP cloud platform",,SaaS,Digital Product||Platform,SAP Finance Systems||Identity & Access Management,,,,,Business Support Foundations,Strongly,Commodity,Buy,Invest,,,,,2022-Jul-01,SM-116252,SM-116252,Technology System,,"

Created by script using data in Nursery... moved to correct Architecture and System group",,,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Jul-01 11:36,SMC-474804
474802,SAP IQ,SAP IQ,SAPIQ,Active,Business Support Foundations,Finance and Tax,,Amit Nighojkar,Amit Sudhir Kumar Jain,,,,,"

SAP IQ is a column-based, petabyte scale, relational database software system used for business intelligence, data warehousing, and data marts.","

Hosted on Azure, Bought software from SAP.",,Public Cloud,,SAP Finance Systems,,,,,Business Support Foundations,Strongly,Commodity,Buy,Invest,,,,,2022-Jul-01,SM-116250,SM-116250,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Jul-01 11:27,SMC-474802
518304,SAP IS,SAP Integration Suite,SAPIS,Active,Business Support Foundations,Finance and Tax,,,Rama Satya Srinivas Varma Ganapathiraju,,,,,"

SAP Integration Suite is an integration platform as a service (iPaaS) that allows you to smoothly integrate on-premise and cloud-based applications and processes with tools and prebuilt content managed by SAP.","

SAP IS is a SaaS service provided by SAP hosted on SAP Business Technology platform. SAP IS is comprised of multiple technical components like API management, Cloud integration Platform , message service etc.,",,,,SAP Finance Systems,,,,,,,,,,,,,,2022-Oct-19,SM-116385,SM-116385,Technology System,,Created by script using data in Nursery,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Oct-19 09:52,SMC-518304
518300,SAP PO,SAP Process Orchestration,SAPPO,Active,Business Support Foundations,Finance and Tax,,,Rama Satya Srinivas Varma Ganapathiraju,,,,,"

SAP PO (Process Orchestration) is a tool for automation and optimization of business processes. It combines features of SAP Business Process Management (BPM), SAP Process Integration (PI), and SAP Business Rules Management (BRM). In other words, SAP Process Orchestration is a more advanced version of SAP PI and has all the tools required to integrate applications.","

SAP PO is a PaaS service provided by SAP, this product acts as integration/middle-wear component in the sap eco-system. This product is hosted on azure cloud as Iaas provider and managed by Ingka in collaboration with Managed service provider Capgemini",,,,SAP Finance Systems,,,,,,,,,,,,,,2022-Oct-19,SM-116383,SM-116383,Technology System,,Created by script using data in Nursery,,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Oct-19 09:42,SMC-518300
474800,SAP SLT,SAP Landscape Transformation,SAPSLT,Active,Business Support Foundations,Finance and Tax,,Amit Nighojkar,Amit Sudhir Kumar Jain,,,,,"

SLT is the an ETL ( Extract , Transform , Load ) tool which allows us to load and replicate data in real-time or schedule data from SAP source system or Non SAP System into SAP HANA Database. SAP SLT server uses a trigger-based replication approach to pass data from source system to target system.","

This system is hosted on Azure, bought software from SAP.&nbsp;",,Public Cloud,,SAP Finance Systems,,,,,Business Support Foundations,Strongly,Commodity,Buy,Invest,,,,,2022-Jul-01,SM-116248,SM-116248,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Jul-01 11:15,SMC-474800
474805,SAP SOLMAN,SAP Solution Manager,SAPSOLMAN,Active,Business Support Foundations,Finance and Tax,,Divya Ravikumar,Amit Sudhir Kumar Jain,,,,,"

The SAP Solution Manager is a centralized robust application management and adminitration solution used to implement, support, operate and monitor your SAP enterprise solutions, SAP Solution Manager is a platform providing integrated content, tools, methodologies and access to SAP systems.","

Hosted on Azure, bought software from SAP",,Public Cloud,,SAP Finance Systems,,,,,Business Support Foundations,Strongly,Commodity,Buy,Invest,,,,,2022-Jul-01,SM-116253,SM-116253,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Jul-01 11:40,SMC-474805
386934,SAPDI,SAP Data Intelligence,SAPDI,Active,Business Support Foundations,Finance and Tax,,,Rama Satya Srinivas Varma Ganapathiraju,,,,,"

SAP Data Intelligence Cloud is a comprehensive data management solution that connects, discovers, enriches, and orchestrates disjointed data assets into actionable business insights at enterprise scale.","

SAP Data Intelligence is a SaaS Service hosted on SAP Business Technology Platform.&nbsp;",,,,SAP Finance Systems,,,,,,,,Buy,Invest,,,,,2022-Apr-29,SM-116198,SM-116198,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Dec-08 08:14,2022-Apr-29 10:03,SMC-386934
114315,SAPP,Service Application Platform,SAPP,Active,Data Integration and Middleware,Data and Integration,,Fely Vacalares Sabaldana,Fely Vacalares Sabaldana,,,https://confluence.build.ingka.ikea.com/display/AI/Service+Application+Platform+-+++SAPP,,Used for hosting in stores for application components which require business logic or transformations which violate the integration concept,,,On-Premise,,IKEA Integration Platform||Integration Enablement Systems,oracle integration services,OIP,,,,,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100801,SM-100801,Technology System,,Martin PerssonChanged to IT Foundation (sw dev),,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-01 14:19,2020-Nov-06 15:30,SMC-114315
522337,Sauce Labs,Sauce Labs,SAUCELABS,Active,Engineering Services,Developer Enablement,,Peter Babamov,Pär Svensson,,,https://saucelabs.com,https://accounts.saucelabs.com/am/XUI/#sso-login/,"Sauce Labs offers cloud-based testing for web and mobile applications, supporting numerous browser and OS combinations. It includes up-to-date Android emulators and iOS simulators for automated testing of native apps and mobile web. Additionally, Sauce Labs features a Real Device Cloud, both public and private, for testing mobile apps under real-world conditions, ensuring fast and quality testing.","

Sauce Labs allows users to run tests in the cloud on more than 700 different browser platforms, operating systems and device combinations, providing a comprehensive test infrastructure for automated and manual testing of desktop and mobile applications using Selenium, Appium and JavaScript unit testing frameworks.

There is no VM setup or maintenance required; live breakpoints are accessible while the tests are running, which enables you to investigate a problem manually.

Sauce Labs also provides a secure testing protocol, Sauce Connect, for testing applications behind customer firewalls.",,SaaS,Digital Product,Test Enablement,Test Platform,Test Enablement,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2022-Oct-27,SM-116406,SM-116406,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Feb-05 10:22,2022-Oct-27 11:28,SMC-522337
621477,SecureScope,SecureScope Vulnerability Management,SSCOPE,Planned,Cyber Security,Cyber Security,Pål Göran Stensson,Claes Gustafsson,Martin Svensson,,,,,"

A system for managing a consolidated/normalized view of vulnerability information across multiple source systems.","

Vulnerability Management System",,Public Cloud,Digital Product,,,,,,,Strongly,Innovating,Make,Invest,,,,,2023-Jun-01,SM-116713,SM-116713,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2968,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jun-01 09:40,SMC-621477
638618,SecurityScorecard,SecurityScorecard,SECSCRE,Active,Cyber Security,Cyber Security,Frederik Janos Braun,Benny Landin,Benny Landin,,,https://confluence.build.ingka.ikea.com/x/XxAyEw,,"

SecurityScorecard is a&nbsp;Cyber Risk rating platform&nbsp;that rates cybersecurity postures of corporate entities through completing scored analysis of cyber threat intelligence signals for the purposes of&nbsp;third party management&nbsp;and&nbsp;IT &amp; cyber risk management.","

SecurityScorecard is a SaaS soltuion that:uses OSINT to score suppliersoffers integration&nbsp;with API capability so that the information can be integrated into other dashboardswe have license for 100 supplier scorecards.",,SaaS,Platform,,,Cyber Assurance,,,,None,Commodity,Buy,Tolerate,,,,,2023-Jul-31,SM-116779,SM-116779,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3400,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jul-31 10:05,SMC-638618
114334,Self Service Cabinet Management System,Self Service Cabinet Management System,SSCAB,Active,Digital Workplace,End User Enablement and Productivity,,Lukasz Sudak,Lukasz Sudak,,,https://idevices.azurewebsites.net/index.php/ssc/,,"

Software for managing device cabinets with end user check in/out functionality","

Trakaweb",,Central Private Hosting,,,self service cabinet,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100823,SM-100823,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114334
621478,Sentinel,Microsoft Sentinel,SENTINEL,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Matthew Itkin,Martin Svensson,,,,,"

Microsoft Sentinel is a scalable, cloud-native solution that provides:Security information and event management (SIEM)Security orchestration, automation, and response (SOAR)","

SIEM and SOAR Capability",,SaaS,Platform,,,,,,,Strongly,Innovating,Buy,Invest,,,,,2023-Jun-01,SM-116714,SM-116714,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2946,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jun-01 09:42,SMC-621478
509538,Service Mesh,Service Mesh,SVCMESH,Planned,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Ola Olsson,Ola Olsson,,,,,"

Software which allows for eased connectivity between services in a micro-service architecture.","

Technology implemented to solve secure connectivity for micro-services as well as additional features in a bounded context location. Service Discovery, rate limiting and&nbsp; canary releases are some key features which are part of a service mesh&nbsp;",,,Platform,,,,,,,,,,,,,,,2022-Sep-26,SM-116355,SM-116355,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Sep-26 13:57,SMC-509538
114419,Service v Pro,Service vPro,SERVPRO,Active,Engineering Services,Developer Enablement,,Venkata Yedida,Pär Svensson,,,https://confluence.build.ingka.ikea.com/display/DevP/Service+V+Pro,,Service virtualization server hosted on the internal network. With Service V Pro you can virtualize complex responses for end-points that you don't control yourself and then use detailed logs to analyze failures from the interaction.,Service virtualization server hosted on the internal network. With Service V Pro you can virtualize complex responses for end-points that you don't control yourself and then use detailed logs to analyze failures from the interaction.,,Central Private Hosting,,Test Enablement,Test Platform,,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-101013,SM-101013,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2024-Feb-05 10:24,2020-Nov-06 15:30,SMC-114419
606846,Servicetrace,Servicetrace,SVTR,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,Christian Kullendorff,Christian Kullendorff,Johan Jacobsson,,,https://anypoint.mulesoft.com/login/domain/ingka,,"

For monitoring availiability of INGKA applications and platforms","

Synthetic monitoring tool for Oracle DB and Citrix applications.",,,,,,,,,,,,,,,,,,2023-Apr-11,SM-116625,SM-116625,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-24 08:46,2023-Apr-11 15:06,SMC-606846
605426,Shoppermotion,Shoppermotion SE,SHOPMOSE,Active,Sweden,Customer Information Management,Andrea Liuzzi,Linus Stenberg,,,,,,"

Shoppermotion is a digital solution that enables the creation of Digital customer flows.","

3rd party product.","

Shopmose",,,,,,,,Store Co-Worker Experience,,,,Tolerate,,,,,2023-Mar-31,SM-116614,SM-116614,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Mar-31 15:06,SMC-605426
559634,ShopperMotion in US,ShopperMotion in US,SHOPMO,Active,USA,Customer Meeting Point,Ida Danielsson,Ida Danielsson,,,,https://shoppermotion.com/discover-why/#,,"

Shoppermotion collects customer paths automatically in real-time without any interaction. It processes millions of visits and serves custom analytics for media, merchandising, category and marketing teams","

Software/app installed on available laptop,&nbsp;smartphone, etc.",,,Digital Product,,local service (USA),,Co-worker,Web browser,Store Co-Worker Experience,,,Buy,Tolerate,,,,,2022-Dec-07,SM-116464,SM-116464,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Dec-07 16:18,SMC-559634
585252,SIDORÄTT,SIDORÄTT,SIDORATT,Active,Digital Workplace,End User Enablement and Productivity,,Lukasz Sudak,Lukasz Sudak,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=582035404,,"

This system&nbsp;enables Local-IT on the sites to manage the weblinks on chromekiosk in an easy and fast way","

Sidorätt is developed to create an weblink into chrome admin page for the specific sites chrome kiosks where they can add weblinks to be run in kiosk mode on the chrome OS kiosks.",,Public Cloud,,,,Frontline Workplace,,,,Minor,Commodity,Make,Invest,,,,,2023-Mar-13,SM-116587,SM-116587,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:28,2023-Mar-13 11:57,SMC-585252
114418,SIEM,SIEM,SIEM,Retired,Cyber Security,Cyber Security,,,,,,,,"

Security Incident and Event Monitoring",,,,,,production security services,,,,,,Commodity,Buy,Decommissioned,,,,2023-Jun-08,2020-Nov-06,SM-101019,SM-101019,Technology System,,"

Martin Persson8/4 Changed to CA19/5 Changed to Cyber Sec",,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114418
181637,SignalFX,SignalFX,SFX,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,Christian Kullendorff,Christian Kullendorff,Johan Jacobsson,,,https://confluence.build.ingka.ikea.com/display/ITOI/Operational+Intelligence,,"

SignalFx&nbsp;provides real-time analytics and operational intelligence for infrastructure, microservices, containers, serverless, and applications.","

SaaS solution which supports real-time cloud native metric collection and tracing that also supports open standards such as OpenTelemetry and OpenTracing.","

opi",SaaS,Platform,,real time monitoring,,,,,Minor,Commodity,Buy,Tolerate,,,,,2021-Oct-06,SM-105128,SM-105128,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-23 15:32,2021-Oct-06 08:07,SMC-181637
114320,SiteScope,SiteScope,SITESCO,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Dec-21,2020-Nov-06,SM-100803,SM-100803,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-21 21:18,2020-Nov-06 15:30,SMC-114320
583511,Slack,Slack,SLACK,Active,Engineering Services,Developer Enablement,Christofer Anderberg,Christofer Anderberg,Kenneth Flatby,,,https://confluence.build.ingka.ikea.com/x/BJphI,,"

Slack is a collaboration hub where you and your team can work together to get things done.&nbsp;

From project kickoffs to budget discussions, and to everything in between — Slack has you covered","

Slack is a cloud-based collaboration software suite. &nbsp;Features include direct-messaging capabilities, notifications and alerts, document sharing, group chat and search. Slack offers integration with many third-party services, including Google Drive an OneDrive, and it is especially popular with software developers and technology-driven companies because it supports source code snippets and retains formatting for a variety of programming languages. Slack also offers integration options for developer-oriented tools such as Github.",,SaaS,Platform,,,Engineering Collaboration,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2023-Mar-08,SM-116583,SM-116583,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-22 11:32,2023-Mar-08 10:36,SMC-583511
114324,Smartprint platform,Smartprint platform,SMARTPRI,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,printing infrastructure service,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100806,SM-100806,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114324
114323,SMTP routing,SMTP routing,SMTPROUT,Active,Cyber Security,Cyber Security,Fredrik Åhlstedt,Anneli Claesson,Fredrik Åhlstedt,,,https://confluence.build.ingka.ikea.com/display/CF/SMTP,,"

IKEA's SMTP Routing service supports two main mail systems (environments) at IKEA today.
One is Exchange/Outlook used for personal co-workers mail traffic, and the other is for applications, SMTP-Routing, e.g. used for mail traffic from different applications/services on global level.","

The product as are used is Cisco ESA which is used as mail gateway for inbound and outbound mail traffic&nbsp;from different applications.&nbsp;
The mail gateway is acting as a protection layer for email and searches for&nbsp;spam/virus and content control etc.

The solution today is based on premises and located in our data centers in Sweden and China where all emails are routed.",,Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100807,SM-100807,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-05 14:27,2020-Nov-06 15:30,SMC-114323
114336,Software Catalogue,Software Catalogue,SWCAT,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,software catalogue application,,,On-Premise,,,central windows,,,,,Minor,Commodity,Make,Eliminate,,,,,2020-Nov-06,SM-100822,SM-100822,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114336
114342,Solidworks Daemon,Solidworks Daemon,SWORKDA,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100827,SM-100827,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114342
114341,Solidworks DocumentManager,Solidworks DocumentManager,SWORKDM,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

It is needed for user's who does not have SolidWorksProfessional and also it allows the non SolidWorks users to see the SolidWorks drawing information in SolidWorkseDrawings",,,On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100828,SM-100828,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114341
114340,Solidworks eDrawings,Solidworks eDrawings,SWORKDRA,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

A multi-format viewer to view, print and review SolidWorks parts, assemblies and drawings and AutoCAD drawings",,,On-Premise,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100824,SM-100824,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114340
114339,Solidworks Flow Simulation,Solidworks Flow Simulation,SWORKFLO,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

Add-in for Solidworks professional",,,SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100830,SM-100830,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114339
114345,Solidworks Plastics,Solidworks Plastics,SWORKPLA,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,"

easy-to-use simulation for analyzing plastic parts and injection molds It simulates how melted plastic flows during the injection molding process to predict manufacturing-related defects",,,SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100831,SM-100831,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114345
114344,Solidworks Premium,Solidworks Premium,SWORKPRE,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100826,SM-100826,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114344
114343,Solidworks Professional,Solidworks Professional,SWORKPRO,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,SaaS,,,IKEA Software Tools,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100833,SM-100833,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114343
114349,Solidworks Simulation Premium,Solidworks Simulation Premium,SWORKSIM,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100834,SM-100834,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114349
114348,Solidworks Visualize,Solidworks Visualize,SWORKVIS,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100829,SM-100829,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114348
114322,SonarQube,SonarQube,SONCUBE,Active,Cyber Security,Developer Enablement,Pål Göran Stensson,Rajeev Kumar Jain,Gustav Lundsgård,,,,https://sonarqube.ct.blue.cdtapps.com/,"Sonarqube is a SAST tool.
SAST inspects in-house developed application’s source code to pinpoint possible security weaknesses which if not resolved, could be exploited by an attacker. SAST comes into play early in the software development life cycle (SDLC), when fixing problems is both easier and less expensive. SAST is effective at finding many of the common weaknesses, such as cross-site scripting, SQL injection, and buffer overflows.",,,Public Cloud,Platform,,,,,,,Partly,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100814,SM-100814,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114322
114335,SourceAnywhere,SourceAnywhere,SRCANY,Retired,Engineering Services,Developer Enablement,,,,,,,,Visual SourceSafe Remote Access Add-On Tool,,,,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Nov-15,2020-Nov-06,SM-100816,SM-100816,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114335
114331,SPLUNK,SPLUNK,SPLUNK,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,Christian Kullendorff,Niclas Nilsson,Niclas Nilsson,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=248211886,https://confluence.build.ingka.ikea.com/display/ITOI/FAQ,"

Used for many capabilities in terms of instrumentation, log collection and also SIEM","

Splunk log collection, analytics, security monitoring and observability platform. Used to collect logs from both on-prem as well as Cloud based solutions.","

opi",Central Private Hosting||Public Cloud,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100813,SM-100813,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-16 21:48,2020-Nov-06 15:30,SMC-114331
114329,SQL Developer,SQL Developer,SQLDEV,Active,Data Integration and Middleware,Developer Enablement,Pontus Skog,Amit Verma,Pontus Skog,,,https://confluence.build.ingka.ikea.com/display/DBMS/Database+Management+Services+Home,,Oracle SQL Developer is an Integrated development environment (IDE) for working with SQL in Oracle databases. Oracle Corporation provides this product free; it uses the Java Development Kit.,SQL Developer is a tool used for connect an Oracle Database and to run SQL queries against it.,,Client,,,development workbench,,,,,,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100815,SM-100815,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-04 10:35,2020-Nov-06 15:30,SMC-114329
114330,SQL Developer Data Modeler,SQL Developer Data Modeler,SQLDDM,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,Client,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Nov-15,2020-Nov-06,SM-100820,SM-100820,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114330
528502,SSAD,Single Source of Asset Data,SSAD,Active,Real Estate and Expansion,Real Estate and Expansion,,Maria Henriksson,Dean Bateman,,,https://confluence.build.ingka.ikea.com/display/HRRE/High+Level+Architecture,,"

SSAD as a data product consolidates all the asset related information different core systems and provides data to the respective systems of operations and also for analytis","

Built on GCP",,,Digital Product,,,CA Real Estate & Expansion,,,,,,,,,,,,2022-Nov-28,SM-116447,SM-116447,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Nov-28 11:58,SMC-528502
668298,SSPR,Self Service Password Reset,SSPR,Active,Identity and Access Management,Identity and Access Management,Andreas Andersson,Emily Millnert,Emily Millnert,,,,,"

Self Service Password Management for Ingka co-workers to change password, reset forgotten password or unlock account.&nbsp;

Product is part of Microsoft's Entra ID (formely Azure AD) suite.&nbsp;","

Self Service Password Management for Ingka co-workers to change password, reset forgotten password or unlock account.&nbsp;",,,Digital Product,,,,,,,,,,,,,,,2023-Oct-16,SM-116829,SM-116829,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4031,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Oct-16 09:57,SMC-668298
114318,Standard Local Infrastructure,Standard Local Infrastructure,SLI,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Hilmar Beck,Hiong Seng Goh,,,,,"Packaged hyperconverged solution for distributed sites, using HPE Simplivity","

X86 based HCI solution based on HPE Simplivity

This system also includes virtualization software from VMware, like vCenter and ESXi",,Distributed Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100811,SM-100811,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-16 11:34,2020-Nov-06 15:30,SMC-114318
114332,Storage M andR,Storage M andR,STORMR,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,David Johansson,,,https://confluence.build.ingka.ikea.com/x/4K4qBg,,"

DC&amp;P Storage and Data Protection engineering team","

Management software for EMC storage","

dcp",Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2024-Jan-25,2020-Nov-06,SM-100819,SM-100819,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-25 09:06,2020-Nov-06 15:30,SMC-114332
114328,Storage Platforms - Archive,Storage Platforms - Archive,SPLATARC,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,David Johansson,,,https://confluence.build.ingka.ikea.com/x/4K4qBg,,"

DC&amp;P Technology to provide long term archiving of data",IBM On-demand,"

dcp",Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100809,SM-100809,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114328
114327,Storage Platforms - Backup,Storage Platforms - Backup,SPLATBAC,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,https://confluence.build.ingka.ikea.com/x/4K4qBg,,"

DC&amp;P Technology to provide backup and restore of business data","

IBM Spectrum Protect (Formerly knows as IBM Tivoli Storage Manager) - Mainly distributed, some centrally still.

EMC Data Domain (HW Distributed)

Veritas Backup Exec (Sw - Distributed part - Retail)

&nbsp;","

dcp",On-Premise,,,,,,,,,,Buy,Decommissioned,,,,2023-Feb-27,2020-Nov-06,SM-100810,SM-100810,Technology System,,"

Retired and replaced with other entries for Retail Backup and Storage tech.",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114327
114333,StoreServ Management Console,StoreServ Management Console,SSMC,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,David Johansson,,,https://confluence.build.ingka.ikea.com/x/4K4qBg,,"

Software used to manage and maintain the central storage environment&nbsp;","

Management software for HPE block storage","

dcp",Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100818,SM-100818,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114333
576874,SYSDIG,Sysdig Secure,SYSDIG,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Daniel Ehn,Fredrik Åhlstedt,,,,,"

Provides reporting and mitigation capabilities to enhance run time container security posture. Also support PCI-DSS compliance needs &amp; Mitigate risk identified from Vulnerability Mgmt tools etc..&nbsp;","

Cloud and container security tool.",,SaaS,Digital Product,,,Cloud Protection,,,,Partly,Commodity,Buy,Invest,,,,,2023-Feb-17,SM-116549,SM-116549,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Feb-17 11:53,SMC-576874
609251,SysTrack,SysTrack,SYSTRACK,Planned,Digital Workplace,End User Enablement and Productivity,Henrik Johansson,Maria Nyström,Maria Nyström,,,,,"

DEX tools are a support tool that will help Ingka to improve for the employees, find the root cause faster, shorter handling of IT-related matters, make things more efficient for the organization through e.g. keep track of the applications in use or that hardware in use can extend their lifecycle to cost optimize.&nbsp;","

Lakeside Software DEX (Digital Employee Experience) is a platform that enables organizations to monitor, measure, and improve the digital experience of their employees. It provides visibility into the performance of digital tools and infrastructure, as well as insights into how employees are using these tools and how they are impacting productivity and engagement.

&nbsp;

The Lakeside Software DEX platform offers a range of features and capabilities, including:

&nbsp;

Digital Experience Monitoring: This feature enables organizations to monitor the performance of their digital tools and infrastructure in real-time. It provides visibility into how applications and systems are performing, and can alert IT teams to issues that may be impacting employee productivity.

Digital Experience Analytics: This feature provides insights into how employees are using digital tools and how these tools are impacting productivity and engagement. It can help organizations identify areas for improvement and optimize digital tools to better meet the needs of employees.

Digital Experience Scoring: This feature enables organizations to measure the overall digital experience of their employees, based on factors such as usability, reliability, and performance. This can help organizations benchmark their digital experience against industry standards and identify areas for improvement.

Digital Experience Optimization: This feature enables organizations to take action to improve the digital experience of their employees. This may involve optimizing digital tools and infrastructure, providing training and support to employees, or implementing new technologies to better meet the needs of employees.

&nbsp;

Overall, Lakeside Software DEX is a comprehensive platform for monitoring, measuring, and optimizing the digital experience of employees. It can help organizations to improve productivity, engagement, and overall employee satisfaction.

URL to Lakeside: https://www.lakesidesoftware.com/","

DEX
Lakeside Software
SysTrack Cloud Edition",SaaS,,,,Collaboration & Communication,,,,Minor,Commodity,Buy,Invest,,,,,2023-Apr-26,SM-116655,SM-116655,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2540,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Apr-26 16:03,SMC-609251
498303,Säkra,Vulnerability Management Platform,SAKRA,Active,Cyber Security,Cyber Security,Pål Göran Stensson,Claes Gustafsson,Omar Benbouazza,,,,,System for storing security related events,Self-managed Apache Kafka cluster running on compute engine instances in GCP.,,Public Cloud,Platform,,,,,,,Partly,Differentiating,Make,Invest,,,,,2022-Aug-22,SM-116320,SM-116320,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Aug-22 09:30,SMC-498303
114099,Tax Integration Point,Tax Integration Point,TIP,Retired,Finance and Procurement,Finance and Tax,,Pernilla Edström,Anders Tenggren,,,,,"

It's part of TIM Assets Search - Jira Digital (ingka.com), so this module is removed from SMC

Serves as an integration point between IRW and market specific tax engines. TIP interacts with different tax calculation engines, depending on the market. For US Vertex will be used and for CA a component called TIM. But from IRW’s perspective this will be fully seamless.

Integrations created as part of project “Tax4eCOM”, which is part of the Multichannel Transformation Programme.",,,,,,,Product Tax Intelligence Module(s) platform,,,,,,,,,,,2023-Oct-26,2020-Nov-06,SM-100048,SM-100048,Technology System,,"

November 2020 Imported from Insight Integration Repository (IIR)",,Moved from Business Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:15,SMC-114099
485036,Td-crawler,Td-crawler,TDC,Active,Engineering Services,Developer Enablement,,Santosh Kumar Dwivedi,Martin Norin,,,https://confluence.build.ingka.ikea.com/x/oDX5GQ,,"

TD-Crawler is web and API based application . It contains test data, orders, fulfillment and customers parts.&nbsp;","

TD Crawler will fetch the test data from different systems and valid test data&nbsp;&nbsp;(working test data among all the systems)&nbsp;and stock or inventory details .

We can create orders from different systems like ISell and Oneweb with API or on the Web. It will be very useful to create bulk orders with different combinations&nbsp;and We can move order statuses as well with TD Crawler . We don't need to contact different applications teams to check article stock ,&nbsp;create orders and move the statuses in downstream systems .",,Central Private Hosting,,Test Enablement,Test Platform,Test Enablement,Co-worker,,,Minor,Commodity,Make,Invest,,,,,2022-Jul-12,SM-116279,SM-116279,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Dec-21 17:30,2022-Jul-12 11:29,SMC-485036
570015,TE-Generera,Generera,TDG,Active,Engineering Services,Developer Enablement,,Santosh Kumar Dwivedi,Martin Norin,,Santosh Kumar Dwivedi,https://confluence.build.ingka.ikea.com/x/lBuwE,,"

Generera is a product to generate test data. Generera aims at mainly using generated/synthetic test data, hence the name, which means ""generate"" in Swedish.

&nbsp;","Generera is a tool that generates synthetic test data by taking a template of the data structure of your choice. It can be used locally or in a deploy pipeline and can also copy data from one environment to another to produce a set of hybrid test data. Generera can be used as a cloud service, a binary release, or a Docker container, but it is important to choose one method and stick with it to avoid mixing methods.",,Public Cloud||Central Private Hosting,Digital Product,Test Enablement,Test Platform,Test Enablement,Co-worker,,,Minor,Commodity,Make,Invest,,,,,2023-Jan-13,SM-116507,SM-116507,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Feb-05 08:23,2023-Jan-13 10:26,SMC-570015
114916,Telephony Sweden,Telephony Sweden,TELSE,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,Central Private Hosting,,,telephony sweden (local sweden),,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100561,SM-100561,Technology System,,"

Johan Valdemarsson - Added ""NA - RETIRED"" as it is retired an not handover needed",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:33,SMC-114916
573994,Test Automation Framework,Test Automation Framework,TAF,Active,Data Integration and Middleware,Data and Integration,,Fely Vacalares Sabaldana,Fely Vacalares Sabaldana,,,https://confluence.build.ingka.ikea.com/x/i45TDg,,"

Test Automation Framework (TAF) is a testing framework that is used for automated functional and non-functional testing of developed integrations within the data integration and middleware area.&nbsp;","

TAF is used by many domain customers for their testing activities, TAF is also integrated with FlexDeploy to support testing in deployment pipelines for INTER and INGKA domains.","

TAF, Test Automation Framework",,Platform,Integration Enablement Systems,oracle integration services,,,,,,,,Eliminate,2027-Dec-31,,,,2023-Feb-03,SM-116532,SM-116532,Technology System,,"

Created by script using data in Nursery",,,Setting System Master Identifier reference for Technology System,2023-Dec-27 09:38,2023-Feb-03 15:06,SMC-573994
114352,TextTool,TextTool,TEXTTOOL,Active,Data Integration and Middleware,Data and Integration,,Jesper Petersson,Urban Martinsson,,,https://confluence.build.ingka.ikea.com/display/TT,,"

Texttool is used for ISELL to create and distribute translations","

The Application Translation Service enables implementation of translations for static application text within software applications. Tools and process provides both developer and translator support. The service also provides distribution of translations to selected clients and servers.
TextTool is a legacy application and should not be used for new products",,Client,,,application translation,,,,,,Commodity,Make,Tolerate,,,,,2020-Nov-06,SM-100832,SM-100832,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114352
114350,Toad,Toad  Xpert,TOAD,Active,Data Integration and Middleware,Developer Enablement,Pontus Skog,Amit Verma,Pontus Skog,,,https://confluence.build.ingka.ikea.com/display/DBMS/Database+Development+Tools,https://jira.digital.ingka.com/servicedesk/customer/portal/305,"Toad™ for Oracle Xpert Edition provides you with all of Toad’s development, editing, debugging, and project management features, plus SQL Optimizer for Oracle. SQL Optimizer validates your database code to ensure the best-possible performance. It also enables you to proactively identify potential issues and optimize SQL automatically.

This tool is under license. to use it, motivation must be provided.","Toad for Oracle generally requires user-based licensing, with no concept of license sharing (for example, when one user stops using the application, another cannot start using it under the same entitlement). Consumption is need-based.",,Client,,,development workbench,,,,,,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100840,SM-100840,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-04 10:41,2020-Nov-06 15:30,SMC-114350
114355,Total Commander,Total Commander,TOTCOM,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,Client,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Jun-05,2020-Nov-06,SM-100841,SM-100841,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114355
635379,TRANSACTION VIEWER,TRANSACTION VIEWER FOR CCTV MANAGEMENT,POSCCTV,Active,Poland,Governance Risk and Compliance,Miłosz Kuczyński,Aleksandra Mickuś-Pijanowska,,,,,,"

App that link POS log to CCTV management system that creates possibilty to search corresponding transaction with CCTV footage","

Live POS log feed into the CCTV Management system",,,,,,,,,Core Digital Infrastructure,,,,Invest,,,,,2023-Jul-10,SM-116770,SM-116770,Technology System,,,,Created by smc-magic using ticket SYSCATSD-3388,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jul-10 13:25,SMC-635379
572838,Translation Orchestrator,Translation Orchestrator,TLO,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,Johan Sporre,Johan Sporre,Johan Sporre,,,https://confluence.build.ingka.ikea.com/x/oJT-GQ,,"

Built Translation Orchestration solution to enable an API for Group Digital.","

Build in Cloud with open source.",,,Digital Product,,,,,,,,,,,,,,,2023-Jan-30,SM-116525,SM-116525,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2023-Jan-30 13:55,SMC-572838
349188,True Availability Dashboard,True Availability Dashboard,TAD,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,Åsa Persson Bayne,Pramod Kompella,Pramod Kompella,,,https://jira.digital.ingka.com/secure/ObjectSchema.jspa?id=17&typeId=1380&objectId=349188,,"

End to end monitoring and automated smoke tests across INGKA solutions for business critical flows that pinpoints performance, availability and users experience as well as financial impact of incidents

&nbsp;","

True Availability provides a dashboard which aggregates data from Catchpoint ,Splunk to analyse production incidents and exceptions and to present a summarised view of issues, root causes and associated financial impact. To validate the robustness of production, it will also run periodic end to end automated tests across Ingka systems in production","

TA Dashboard",Central Private Hosting||SaaS,Platform,,service monitoring,True Availability Dashboard,,,,Minor,Commodity,Buy,Tolerate,,,,,2022-Feb-07,SM-105268,SM-105268,Technology System,,Created by script using data in Nursery,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-16 10:32,2022-Feb-07 09:58,SMC-349188
114354,tyGraph for Yammer,tyGraph for Yammer,TYGYAM,Active,Digital Workplace,End User Enablement and Productivity,,Michaela Erlandsson,Maria Nyström,,,,,"Social analytics tool for the IKEA, Office 365 based, Yammer solution",,,SaaS,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100842,SM-100842,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114354
114353,UFT,Microfocus Unified Functional Manager,UFT,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,On-Premise,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Mar-29,2020-Nov-06,SM-100838,SM-100838,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114353
114359,UIPath,UIPath,UIPATH,Active,Digital Workplace,End User Enablement and Productivity,,Amandus Hammargren,Vera Janevska,,,,,"

RPA",,,SaaS,,,robotic process automation,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100843,SM-100843,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-14 10:58,2020-Nov-06 15:30,SMC-114359
114358,UltraEdit,UltraEdit,ULTRAED,Active,Store Fulfilment Operations,Developer Enablement,,Kristian Bengtsson,,,,,,,,,Client,,,development workbench,,,,,,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100844,SM-100844,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-24 14:01,2020-Nov-06 15:30,SMC-114358
695006,US-PSH,USDH Provided Services Hub,USPSH,Planned,USA,Fulfillment Forecasting and Planning,,Thomas Pham,Alan Macfarlane,Kris Olson,Brendan Mcgeehan,,,"

The USDH Provided Service Hub is a suite of tools and low-code platforms that unlock system flexibility and support ongoing configuration changes without requiring custom engineering. This solution includes rented software that solves the problem now and provides flexibility for future needs (e.g. Expansion of services to Bathrooms)","

The USDH Provided Service Hub is a suite of tools and low-code platforms backed by an industry-leading iPaaS (integration Platform as a Service).

This platform provides a flexible canvas to create, manage, and expand US-provided services which can easily be managed by the business lines.","

USDH Provided Service Hub ""US-PSH"" includes a suite of low-code platforms including:BoomiMatillionSnowflakeSalesforce",,,,,USDH_Services,,,Fulfilment Forecasting and Planning,,,,,,,,,2024-Jan-10,SM-116938,SM-116938,Technology System,,,,Removed inactive Jira Profile,,2024-Feb-02 17:01,2024-Jan-10 14:47,SMC-695006
114363,Vault,Hashicorp Vault,VAULT,Active,Engineering Services,Developer Enablement,,Jan Magnusson,Niclas Strandéus,,,,,"

Credentials manager. It stores credentials such as usernames, password and certificates in a secure and eaisly accessible manner.",,,Central Private Hosting,,Continuous Integration,,,Co-worker,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100846,SM-100846,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Dec-21 17:14,2020-Nov-06 15:30,SMC-114363
114362,VDI,Virtual Desktop Infrastructure,VDI,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,virtual workplace,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100847,SM-100847,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114362
681099,Verint Workforce Engagement,Verint Workforce Engagement,VERWRKEN,Active,Canada,Customer Meeting Point,Georges Deir,Charles Simard,,,,https://confluence.build.ingka.ikea.com/display/CANADAHUB/Verint+Workforce+Engagement,,"

Cloud-based online tool used for scheduling and attendance of co-workers.","

https://wfo.ikea.verintcloudservices.com/wfo/control/signin","

VER : Veritn
WRK : Workforce
EN : Engagement",SaaS,,,,,Co-worker,Web browser,Co-worker Engagements and Insights,Strongly,Commodity,Buy,Invest,,,,,2023-Nov-24,SM-116879,SM-116879,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4588,,2023-Dec-06 14:55,2023-Nov-24 16:51,SMC-681099
152716,VIAD,Virtual Apps & Desktops,CWEVIAD,Active,Digital Workplace,End User Enablement and Productivity,,John Kalkbrenner,Ferenc Horvath,,,https://confluence.build.ingka.ikea.com/x/SvW1E,,"

Centralized Terminal server based delivery model for delivering published applications and desktops allowing users to access applications and data on a remote computer over the network. The VIAD technical platform is based on ICC infrastructure and Citrix Virtual Apps &amp; Desktops technology","

Citrix Virtual Apps &amp; Desktops technology

Note! Will replace IWTS, roadmap plan is to implement solution for Europe till end of 2021, and APAC + Noth America and Canada till end of 2022.","

cwe citrix",On-Premise,,,,,,,,Minor,Commodity,Buy,Invest,,,,,2021-Jul-02,SM-105052,SM-105052,Technology System,,,,Moved from Business Platform,Setting System Master Identifier reference for Technology System,2024-Jan-15 03:40,2021-Jul-02 17:22,SMC-152716
506731,VIBECOLLAB,Vibe,VIBECOLLAB,Active,Serbia,Digital Platform and Core Infrastructure,,Vuk Maras,,,,https://vibe.us/,,"

Connected with a Service office rebuild we need a solution for Digital Whiteboard. This tool will respond to our need to digitalizing our processes and adjusting to our new ways of working. Meeting in physical space is not only a physical experience anymore – that is what we want to establish with our new office. On top of that, our new office should reflect an image of new, stable and modern IKEA. innovating tools that we are using every day will contribute to that vastly","

55"" Interactive Whiteboard | 4K UHD Touch Screen Display | Vibe&nbsp;

This model has been purchased and will be connected to IKEA Network wirelessly since the device is portable.IKEA service account will be connected with VIBE for a better collaboration inside the systems (Team,Office,etc.)",,,Platform,,,,,,Store Co-Worker Experience,,,,Tolerate,,,,,2022-Sep-15,SM-116343,SM-116343,Technology System,,"

Created by script using data in Nursery",,Removed inactive Jira Profile,Setting System Master Identifier reference for Technology System,2023-Nov-17 18:01,2022-Sep-15 13:25,SMC-506731
114360,Visual Source Safe,Visual Source Safe,VSS,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,On-Premise,,,,,,,,,Commodity,Buy,Decommissioned,,,,2023-Nov-15,2020-Nov-06,SM-100849,SM-100849,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114360
114366,Visual Studio,Visual Studio,VSTUDIO,Active,Engineering Services,Developer Enablement,,Jan Magnusson,Niclas Strandéus,,,,,,,,Client,,,development workbench,,Co-worker,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100850,SM-100850,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-21 17:11,2020-Nov-06 15:30,SMC-114366
566108,VM-Ware Workspace One,VM-Ware Workspace One,WRKSPACE1,Active,Digital Workplace,End User Enablement and Productivity,,Lukasz Sudak,Lukasz Sudak,,,https://ikeacn.awmdm.com/,,"

this system enables mobile devices for our frontline workers to have a shared device at hand when they need it. and easily access needed INGKA apps and resources wherever the worker are at. The service offering includes secure management of the devices and security features to have a secure device.","

Managing shared devices and RDTs from Workspaceone",,SaaS,,,,Frontline Workplace,,,,Minor,Commodity,Buy,Invest,,,,,2022-Dec-20,SM-116481,SM-116481,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-05 10:28,2022-Dec-20 07:33,SMC-566108
114375,VMware vSphere,VMware vSphere Virtualization Platform,WMWSPH,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Annika Ederfors Aronsson,,,https://confluence.build.ingka.ikea.com/x/weY-Eg,,"

Virtualization engine for x86 based workloads.","

Also contains vCenter and related technologies likea automation suites as vRealize Automatin and Operation (vRa /vRO)","

clh ccoe",Central Private Hosting,Digital Product,,,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100861,SM-100861,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-16 11:38,2020-Nov-06 15:30,SMC-114375
114364,WAN Connectivity,WAN Connectivity,WANCON,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,Francis Yeung,,,,,Provides routing within the IKEA WAN. The routing function routes the outgoing traffic from a site depending on destination and type of traffic between offloading as a tunnel over Internet or WAN (MPLS).,"

Versa SD-WAN Verizon MPLS, Verizon Internet Local ISP Provider. Requires a SD-WAN router at each site.","

cnn",Distributed Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100852,SM-100852,Technology System,,"

POs should not be Inter users of Ingka owned systems.",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-04 09:55,2020-Nov-06 15:30,SMC-114364
114370,WAN optimization,WAN optimization,WANOPT,Retired,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,,,,,https://confluence.build.ingka.ikea.com/x/Ynk6CQ,,"

Provides prioritising and compressing of traffic leaving a site before the traffic enters the WAN. Cache the information whenever it is possible based on policies.

Was heavily used in Distributed Sites on early internet period, while it was very slow and solution improved user experience a lot. Today almost completely removed.",Riverbed,,,,,network wan,,,,,,Commodity,Buy,Decommissioned,,,,2023-Feb-27,2020-Nov-06,SM-100853,SM-100853,Technology System,,"

Technology now longer in use",,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114370
512549,Warehouse Backup ,Warehouse Backup ,WHBKUP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,David Johansson,David Johansson,,,https://confluence.build.ingka.ikea.com/x/QnOYK,,IKEA Warehouse technical platform backup. Redunant backup inbetween room A and room B.,Backup management solution for Warehouse Technical Platform,,,,,,,,,,,,,,,,,,2022-Oct-04,SM-116370,SM-116370,Technology System,,"

Created by script using data in Nursery",,,,2024-Jan-26 09:17,2022-Oct-04 14:48,SMC-512549
512551,Warehouse Backup Storage,Warehouse Backup Storage,WHBKPSTO,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,David Johansson,David Johansson,,,https://confluence.build.ingka.ikea.com/x/QnOYK,,Storage for backups in warehouses. ,"Backup storage solution for Warehouse Technical Platform, this storage is based on Dell/EMC Data Domain.",,,Platform,,,,,,,,,,,,,,,2022-Oct-04,SM-116371,SM-116371,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2024-Jan-26 09:20,2022-Oct-04 14:57,SMC-512551
114380,Warehouse Technical Platform,Warehouse Technical Platform,WTP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Hilmar Beck,Dag Ehnbom,,,https://confluence.build.ingka.ikea.com/x/VGoAC,,Packaged solution for distribution centres and CDC:s,"

LCM space https://confluence.build.ingka.ikea.com/x/Evz1Cw

This system also includes virtualization software from VMware, like vCenter and ESXi",,Distributed Private Hosting||Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100868,SM-100868,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2024-Jan-16 11:33,2020-Nov-06 15:30,SMC-114380
114368,Web Deployment Tool,Web Deployment Tool,WEBDEPL,Retired,Engineering Services,Developer Enablement,,,,,,,,Web Deployment Tool simplifies the migration management and deployment of IIS Web servers Web applications and Web sites,,,On-Premise,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Nov-15,2020-Nov-06,SM-100855,SM-100855,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114368
114420,Web Meeting,Web Meeting (Teams for business),WEBMEET,Active,Digital Workplace,End User Enablement and Productivity,,,Maria Nyström,,,,,,,,SaaS,,,,,,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-101018,SM-101018,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114420
696258,Webex,Cisco Webex - Corporate Telephony ,WEBEX,Active,France,End User Enablement and Productivity,Yann DA SILVA,Yann DA SILVA,,,,,,"

WEBEX is an app provided by vendor called BOUYGUES TELECOM.

It has replaced the historical solution called RINGA.

Webex is developped and published by CISCO.

The solution provides a corporate telephony solution including :

&nbsp;Corporate PhonebookCall groups functionnalities (connect and disconnect form a call group)Written Chat&nbsp;Video call (not used)","

The APP is deployed via AIRWATCH and INTUNE to coworkers smartphones.

It is linked to the Google Play Store, no APK manual deployment as previously.&nbsp;

Updates are done automatically via the Google Play Store",,,Digital Product,,local service (France),,,,Digital Workplace,,,,,,,,,2024-Jan-15,SM-116944,SM-116944,Technology System,,,,Created by smc-magic using ticket SYSCATSD-6086,,2024-Jan-15 13:22,2024-Jan-15 13:22,SMC-696258
114376,Weblogic J2EE,Weblogic J2EE,WLS,Active,Data Integration and Middleware,Digital Platform and Core Infrastructure,,Urban Martinsson,Urban Martinsson,,,https://confluence.build.ingka.ikea.com/display/AH,,Middleware from Oracle to run Java Applications,,,On-Premise,,,,,,,,,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100865,SM-100865,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114376
114365,Windows 10 Kiosk,Windows 10 Kiosk,W10KIOSK,Active,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,,,,,,Partly,Commodity,Buy,Migrate,,,,,2020-Nov-06,SM-100851,SM-100851,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114365
613593,Windows Client Management Tool,Windows Client Management Tool,WCMT,Active,Digital Workplace,End User Enablement and Productivity,,Johan Johansson,Ferenc Horvath,,,,,"

A system that enables us to enrol, manage, and report on windows clients and apps.","

Microsoft Intune is a cloud-based service that focuses on mobile device management (MDM) and mobile application management (MAM). It allows businesses to manage Windows clients remotely, ensuring they adhere to security protocols, while providing automated updates and patches. Intune integrates seamlessly with Microsoft 365, enhancing productivity by managing apps and data on Windows devices. It also supports conditional access policies and configures device settings, facilitating efficient, secure, and compliant operations. Its intuitive, user-friendly interface makes it an essential tool for modern IT administration.",,SaaS,Platform,,,Co-worker Clients,,,,Minor,Commodity,Buy,Invest,,,,,2023-May-17,SM-116679,SM-116679,Technology System,,,,Created by smc-magic using ticket SYSCATSD-2662,Setting System Master Identifier reference for Technology System,2024-Jan-29 07:37,2023-May-17 14:33,SMC-613593
114377,Windows TP,Windows TP,WINTP,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Andreas Stehn,,,https://confluence.build.ingka.ikea.com/x/wxM0E,,"

Microsoft Windows based Server platforms","

Standardized OS platform based on Windows Server","

dcp",Central Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100860,SM-100860,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114377
114371,WinSCP,WinSCP,WINSCP,Active,Engineering Services,Developer Enablement,,Jan Magnusson,Niclas Strandéus,,,,,,,,On-Premise,,,development workbench,,Co-worker,,,Minor,Commodity,Buy,Eliminate,,,,,2020-Nov-06,SM-100859,SM-100859,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Dec-21 17:12,2020-Nov-06 15:30,SMC-114371
114367,Wordfinder dictionaries,Wordfinder dictionaries,WFDICT,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100856,SM-100856,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114367
114373,Wordfinder Engine,Wordfinder Engine,WFENGINE,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise,,,,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100857,SM-100857,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114373
114372,Wordfinder Grammar,Wordfinder Grammar,WFGRAM,Active,Digital Workplace,End User Enablement and Productivity,,Kjell-Åke Hofer,Maria Nyström,,,,,,,,On-Premise,,,,,,,,None,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100858,SM-100858,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114372
565084,Work Environment Manager Forms,Work Environment Manager Forms,WEMFORMS,Active,Sweden,Inventory Management,Veronica Laudius,Johan Lindgren,,,,,,"

It's a digital solution that simplifies the administration around work environment forms. There is a SharePoint list that is&nbsp;used as a&nbsp;""Dashboard"" for the P&amp;C coworkers.&nbsp;Allowing the user&nbsp;to&nbsp;find out the status of work environment&nbsp;forms for the managers. Signing the form is all managers within Retail Sweden, working with the administration is all Local P&amp;C departments in the Units.","

A report from Global View will be imported to a DB that is connected to a Power Automate flow. This will make scheduled updates to check if there are new managers, and what the current status is for the already added managers. It will then filter the SharePoint list in red/green rows with all information needed to follow up easily.","

N/A",,Digital Product,,,,,Web browser,People Management,,,,Invest,,,,,2022-Dec-14,SM-116476,SM-116476,Technology System,,Created by script using data in Nursery,,,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2022-Dec-14 13:40,SMC-565084
399379,Workflows,Workflows,WORKFLOWS,Active,Engineering Services,Developer Enablement,,Jan Magnusson,Niclas Strandéus,,,,https://portal.dev.ingka.com/docs/default/component/workflows,"

Workflows provides a platform for teams to automate tools and processes for providing services, and to create reusable software templates to allow other teams to leverage their work.","

Workflows&nbsp;provides a framework&nbsp;for technology services teams to provide access to the tools that they own in an automated way.&nbsp;It also provides a framework for creating services out of templates.

&nbsp;

Repository: https://github.com/ingka-group-digital/es-workflows

Documentation: https://portal.dev.ingka.com/docs/default/component/workflows",,Public Cloud,,,,,Co-worker,,,Minor,Commodity,Make,Tolerate,,,,,2022-May-30,SM-116218,SM-116218,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Dec-22 11:16,2022-May-30 10:05,SMC-399379
375826,Workpath,Workpath,WORKPATH,Active,Engineering Services,Developer Enablement,,Kenneth Flatby,Kenneth Flatby,,,https://ingka-ikea.workpath.com/,https://jira.digital.ingka.com/servicedesk/customer/portal/1/create/4170,"

OKR Management tool for Group Digital.

The workflow tool also provides the flexibility needed to meet customer requirements across all maturity stages and stakeholder groups. Guiding enterprises along each step of their journey and supporting them with an entire enablement ecosystem.

Business ownership will be Digital Transformation Office (DTO) responsible for onboarding users in wow and processes.","

Workpath is a SaaS tool for strategy execution with OKR.&nbsp;

The enterprise platform meets the required security standards and allows numerous integrations. .
Technical ownership will be Engineering Collaboration team. This includes license management, user system onboarding and integrations.",,SaaS,,,,Engineering Collaboration,Co-worker,Web browser,,Minor,Commodity,Make,Invest,,,,,2022-Apr-13,SM-116188,SM-116188,Technology System,,Created by script using data in Nursery,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Dec-22 11:17,2022-Apr-13 09:54,SMC-375826
114374,Workspace Sharepoint,Workspace (Sharepoint),WORKSHAR,Retired,Digital Workplace,End User Enablement and Productivity,,,,,,,,,,,On-Premise,,,,,,,,Minor,Commodity,Buy,Decommissioned,,,,2023-Sep-08,2020-Nov-06,SM-100862,SM-100862,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114374
114379,x86 Hardware TP,x86 Hardware TP,X86HW,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Martin Vörös,Annika Ederfors Aronsson,,,https://confluence.build.ingka.ikea.com/x/UjevB,,"

Compute platform based on x86 hardware &nbsp;server platforms",HPE x86 based hardware,"

dcp x86",Central Private Hosting||Distributed Private Hosting,Platform,,,,,,,Minor,Commodity,Buy,Tolerate,,,,,2020-Nov-06,SM-100863,SM-100863,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114379
114378,XML Marker,XML Marker,XMLMARK,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,On-Premise,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Jun-05,2020-Nov-06,SM-100864,SM-100864,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114378
114384,Xstandard Zip,Xstandard Zip,XZIP,Retired,Engineering Services,Developer Enablement,,,,,,,,,,,On-Premise,,,development workbench,,,,,,Commodity,Buy,Decommissioned,,,,2023-Jun-05,2020-Nov-06,SM-100871,SM-100871,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2020-Nov-06 15:30,SMC-114384
141667,YANDEX,Yandex public cloud,YANDEX,Active,Core Digital Infrastructure,Digital Platform and Core Infrastructure,,Bonny Lindberg,Tobias Berg,,,https://confluence.build.ingka.ikea.com/x/iomlB,,"

Yandex is a native Russian cloud provider - used to sustain / fullfill Russian region cyber regulations and laws","

Yandex features IaaS/PaaS/SaaS solutions for a very large set of services and features their own Marketplace","

clh ccoe",SaaS||Public Cloud,Platform,,,,,,,Minor,Commodity,Buy,Eliminate,,,,,2021-Jun-08,SM-105025,SM-105025,Technology System,,,,Moved from Technology Platform,Setting System Master Identifier reference for Technology System,2023-Nov-17 16:48,2021-Jun-08 16:44,SMC-141667
114955,Zephyr,Zephyr,ZEPHYR,Active,Engineering Services,Developer Enablement,,Marcus Bruzelius,Roger Frödin,,,https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=171616579,https://build-ingka-ikea.yourzephyr.com/,"Zephyr Enterprise is a test management tool with which you can create control over  your tests. 

- create/import requirements, testcases
- create releases and test cycles
- assign testcases to testers
- integrates with automated tests
- reporting / tracking

Zephyr handles traceability between requirements-testcases and bug reports and integrates with Jira.",Zephyr is a SaaS solution licensed from Smartbear. On-boarding via Allen development portal.,,SaaS,,Test Enablement,Test Platform,Test Enablement,Co-worker,,,Minor,Commodity,Buy,Invest,,,,,2020-Nov-06,SM-100604,SM-100604,Technology System,,,,Moved from Technology Product,Setting System Master Identifier reference for Technology System,2023-Dec-22 11:33,2020-Nov-06 15:33,SMC-114955
695002,Zip Lookup Tool,Zip Lookup Tool,ZIPLOOKUP,Active,USA,Customer Support,Andrea Wise,Lauren Chinault,,,,,,"

The Zip Lookup tool is used to identify the TSP (transportation service provider) and local IKEA store in closest proximity to a customer's zip code. The tool consists of an open field to enter a zip code and returns results for the closest IKEA stores and TSP servicing the area.","

This tool was developed by the USDH. The application exists on the Coworker Dashboard and references data maintained in the Info Integrity Tool (Excel), which is kept up to date from the Service Fulfillment Operations Specialists. Anyone with the URL can access:&nbsp;https://cwtools.csc.ingka.com/zip-lookup",,,,,,,,,Customer Support and Returns,,,,,,,,,2024-Jan-10,SM-116937,SM-116937,Technology System,,,,Created by smc-magic using ticket SYSCATSD-4814,,2024-Jan-10 14:44,2024-Jan-10 14:44,SMC-695002
