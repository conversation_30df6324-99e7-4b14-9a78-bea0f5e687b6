Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project description,Project url,Priority,Resolution,Assignee,Reporter,Creator,Created,Updated,Last Viewed,Resolved,Fix Version/s,Component/s,Component/s,Due Date,Votes,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Labels,Description,Environment,Watchers,Watchers,Watchers,Watchers,Watchers,Watchers,Watchers,Original Estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Inward issue link (Blocks),Outward issue link (Blocks),Inward issue link (Relates),Outward issue link (Relates),Attachment,1st Reminder to Customer,1st Reminder to Customer simplified,2nd Reminder to Customer,2nd Reminder to Customer simplified,Custom field (Acceptance criteria),Acknowledgement SLAs (CET),Acknowledgement SLAs (CET) simplified,Custom field (Additional Information),Custom field (Affected System),Custom field (Aha Key),Custom field (Application),Custom field (Area),Auto close resolved tickets,Auto close resolved tickets simplified,Autoclose Tickets,Autoclose Tickets simplified,Avg time at requester,Avg time at requester simplified,Custom field (Build Number),Custom field (Business Benefits),Custom field (Business Impact),Custom field (Business Owner),Custom field (Business Value),Custom field (Cause Service),Custom field (Cause code),Centres Dynamics SLA,Centres Dynamics SLA simplified,Custom field (Change completion date),Custom field (Change reason),Custom field (Change risk),Custom field (Change type),Custom field (City),Closed for 6 mo,Closed for 6 mo simplified,CoWorkerSLA,CoWorkerSLA simplified,Custom field (Code String),Custom field (Complexity),Custom field (Computer ID),Custom field (Configuration Item),Custom field (Consequences if not accepted),Custom field (Contingency plan),Custom field (Cost),Custom field (Cost Center),Custom field (Country),Create to In Progress,Create to In Progress simplified,Create to Resolved,Create to Resolved simplified,Custom field (Customer),Customer Request Type,Customer time-out,Customer time-out simplified,Custom field (Cyber Inventory),"DEM &amp; APM Customer response timeout","DEM &amp; APM Customer response timeout simplified",DSM - 1st Reminder to Customer,DSM - 1st Reminder to Customer simplified,DSM - 2nd Reminder to Customer,DSM - 2nd Reminder to Customer simplified,DSM - Autoclose Tickets,DSM - Autoclose Tickets simplified,DSM - Time to First Response,DSM - Time to First Response simplified,DSM - Time to Resolution,DSM - Time to Resolution simplified,DSM-T1R,DSM-T1R simplified,DSM-TTR,DSM-TTR simplified,Custom field (Dependencies),Custom field (Deployment files),Custom field (Detected by),Custom field (Digital Domain),Custom field (Digital Unit),Custom field (Duration),Custom field (Effort),Custom field (Email address),Custom field (End date),Custom field (Epic Link),Custom field (External reference),Custom field (FFPA count),Custom field (Feature type),Custom field (First Reported by),Custom field (Full Name),GCP access,GCP access simplified,GLS Translation,GLS Translation simplified,Custom field (Generic Work Packages),Custom field (Group Decision),Custom field (Impact),Custom field (Inherent risk),Custom field (Installation instructions),Internal Escalation,Internal Escalation simplified,Custom field (Investigation reason),LAEM-WaitforCustomer,LAEM-WaitforCustomer simplified,LAEMO-WFC,LAEMO-WFC simplified,LAEMO-WFC-New,LAEMO-WFC-New simplified,Custom field (Lead time),Custom field (Likelihood),Custom field (Linked major incidents),Custom field (Local/Central),Localisation,Localisation simplified,Custom field (Main Business Value Narrative),Custom field (Main Business Value Narrative),Custom field (Maintenance Work %),Custom field (Modernize Foundation %),Custom field (New Value Work %),Non responsive customer,Non responsive customer simplified,Custom field (Operational categorization),Custom field (Organizational Unit),Custom field (Original issue key),Custom field (Original story points),Custom field (Originating Team),Custom field (Overview),Custom field (Owner),Custom field (PBI Number),Custom field (PKE Number),PLFSD Jira,PLFSD Jira simplified,Custom field (Parent Link),Custom field (Pending reason),Custom field (Phase Detected),Custom field (Plan Maturity),Custom field (Planned Sign-off Date),Custom field (Planned Submission Date),Custom field (Planned Tertial),Custom field (Plugin),Custom field (Probability),Custom field (Problem),Custom field (Product Name),Custom field (Product categorization),Custom field (Project Lead),Custom field (Project Name),Custom field (Proposed solution),Custom field (Push/Pull),Custom field (Question 1 - Free Text (single line)),Custom field (Question 10 - Free Text (single line)),Custom field (Question 100 - Select List (single choice)),Custom field (Question 101 - Free Text (single line)),Custom field (Question 102 - Free Text (single line)),Custom field (Question 103 - Free Text (single line)),Custom field (Question 104 - Free Text (single line)),Custom field (Question 109 - Date Time Picker),Custom field (Question 11 - Free Text (single line)),Custom field (Question 110 - User Picker (single user)),Custom field (Question 117 - User Picker (single user)),Custom field (Question 12 - Free Text (single line)),Custom field (Question 13 - Free Text (single line)),Custom field (Question 14 - Free Text (single line)),Custom field (Question 15 - Free Text (single line)),Custom field (Question 16 - Free Text (single line)),Custom field (Question 17 - Free Text (single line)),Custom field (Question 2 - Free Text (single line)),Custom field (Question 21 - Select List (single choice)),Custom field (Question 3 - Free Text (single line)),Custom field (Question 4 - Free Text (single line)),Custom field (Question 42 - Select List (single choice)),Custom field (Question 5 - Free Text (single line)),Custom field (Question 50 - Free Text (multi line)),Custom field (Question 51 - Free Text (multi line)),Custom field (Question 52 - Free Text (multi line)),Custom field (Question 53 - Free Text (multi line)),Custom field (Question 56 - Free Text (single line)),Custom field (Question 57 - Free Text (single line)),Custom field (Question 58 - Free Text (single line)),Custom field (Question 59 - Free Text (single line)),Custom field (Question 6 - Free Text (single line)),Custom field (Question 60 - Free Text (single line)),Custom field (Question 61 - Free Text (single line)),Custom field (Question 62 - Free Text (single line)),Custom field (Question 63 - Free Text (single line)),Custom field (Question 64 - Free Text (single line)),Custom field (Question 65 - Date Time Picker),Custom field (Question 68 - Date Picker),Custom field (Question 69 - User Picker (single user)),Custom field (Question 7 - Free Text (single line)),Custom field (Question 70 - User Picker (single user)),Custom field (Question 77 - User Picker (single user)),Custom field (Question 78 - User Picker (single user)),Custom field (Question 8 - Free Text (single line)),Custom field (Question 81 - User Picker (single user)),Custom field (Question 82 - Free Text (multi line)),Custom field (Question 83 - Free Text (multi line)),Custom field (Question 84 - Free Text (multi line)),Custom field (Question 85 - Date Picker),Custom field (Question 86 - Free Text (multi line)),Custom field (Question 87 - Free Text (multi line)),Custom field (Question 88 - Free Text (multi line)),Custom field (Question 89 - Free Text (multi line)),Custom field (Question 9 - Free Text (single line)),Custom field (Question 91 - Number Field),Custom field (Question 92 - Number Field),Custom field (Question 93 - Number Field),Custom field (Question 94 - Number Field),Custom field (Question 95 - Number Field),Custom field (Question 96 - Free Text (single line)),Custom field (Question 97 - Free Text (single line)),Custom field (Question 98 - Free Text (single line)),Custom field (Question 99 - Select List (single choice)),Custom field (Rank),Custom field (Ready for Verification),Custom field (Realization),Custom field (Related Product),Reminder Email,Reminder Email simplified,Reminder for FAQ Page Update,Reminder for FAQ Page Update simplified,Custom field (Reproducible),Custom field (Request participants),Custom field (Requesting Project),Custom field (Residual Impact),Custom field (Residual Likelihood),Custom field (Residual risk),Resolution Time,Resolution Time simplified,Custom field (Resolution code),Custom field (Resolution notes),Resolved for 3 Days,Resolved for 3 Days simplified,Resolved for 7 days,Resolved for 7 days simplified,Responded to customer,Responded to customer simplified,Custom field (Response),Custom field (Risk Category),Custom field (Risk Impact),Custom field (Root cause),SL3 Request Low,SL3 Request Low simplified,SL3 Request Medium,SL3 Request Medium simplified,SL3 SE,SL3 SE simplified,SLA for New Development,SLA for New Development simplified,SLA for Service Request,SLA for Service Request simplified,SLA for Waiting for Customer,SLA for Waiting for Customer simplified,SLA test,SLA test simplified,SST SLA,SST SLA simplified,Satisfaction score (out of 5),Custom field (Select Launch Epics),Custom field (Service),Custom field (Service Offering),Custom field (Severity),Custom field (Site),Custom field (Source),Custom field (Start date),Custom field (Steps to Reproduce),Custom field (System/s),Custom field (System/s),Custom field (System/s),Custom field (T-shirt size),Custom field (Target Group),Custom field (Target end),Custom field (Target start),Custom field (Team),Time To Close after Finished Assessment,Time To Close after Finished Assessment simplified,Time in Pending,Time in Pending simplified,Time pending approval,Time pending approval simplified,Time to Approce 2w,Time to Approce 2w simplified,Time to Approve,Time to Approve simplified,Time to Approve 2w,Time to Approve 2w simplified,Time to Approve Request,Time to Approve Request simplified,Time to Archive,Time to Archive simplified,Time to Assignee,Time to Assignee simplified,Time to Delete,Time to Delete simplified,Time to approve normal change,Time to approve normal change simplified,Time to close SLA breached ticket,Time to close SLA breached ticket simplified,Time to close after resolution,Time to close after resolution simplified,Time to close after waiting for customer,Time to close after waiting for customer simplified,Time to first action,Time to first action simplified,Time to first assignee,Time to first assignee simplified,Time to first response,Time to first response simplified,Time to first response - Nordic Morning,Time to first response - Nordic Morning simplified,Time to in Review,Time to in Review simplified,Time to initial Response,Time to initial Response simplified,Time to nag email,Time to nag email simplified,Time to remind after waiting for customer,Time to remind after waiting for customer simplified,Time to remind after waiting for customer 2days,Time to remind after waiting for customer 2days simplified,Time to remove access (3 months),Time to remove access (3 months) simplified,Time to resolution,Time to resolution simplified,Time to resolve a request,Time to resolve a request simplified,Time to send Reminder,Time to send Reminder simplified,Custom field (Transaction ID),Custom field (Treatment),Custom field (Treatment plan),Custom field (Unplanned Work %),Custom field (Urgency),Custom field (Users in Group),Custom field (Vendor),WFC-SLA-Close-Req,WFC-SLA-Close-Req simplified,WFC-SLA-Notify-Cus,WFC-SLA-Notify-Cus simplified,WFS-SLA-Notify-Assignee,WFS-SLA-Notify-Assignee simplified,Custom field (WP Details),Waiting for Customer for 10 days,Waiting for Customer for 10 days simplified,Waiting for Support,Waiting for Support simplified,Custom field (Week),Custom field (Workaround),Custom field (Workpackage ID),Yearly review by Admin,Yearly review by Admin simplified,application life,application life simplified,Custom field (iDesk ID),Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment
Extra Need Reservation (ENR) for CDC,CHI-1962,2670249,Launch,Backlog,CHI,China,software,chher8,,,Low,,SOLIU10,ansua10,jirabot,2024-Jan-19 14:50,2024-Feb-02 10:35,2024-Feb-28 03:27,,,CFF,,,0,CFF-Availability&Support,New,Pilot,,,,,,,"*[Extra Need Reservation (ENR) for CDC - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/Extra+Need+Reservation+%28ENR%29+for+CDC+-+Launch+One+Pager]*",,jirabot,,,,,,,0,0,,0%,0,0,,,,,LAUNCH-4749,,,,,,,,,,,,,,,,,,,,,,"Expected Business Value (country level) 

The business value stated below are rough estimations on Global level which will be validated during pilot period and as more countries start using the reservation capabilities.


Value drivers

Revenue increase:
Avoid losing 80M Euros from the largest B2B orders, Reserving Extra Need could contribute to saving up to 80M€ of sales loss per year, if implemented in all Ingka markets (estimation based on assumptions to be validated)

Cost reduction: -

Productivity increase:
Allow saving 105K working hours of co-workers (CFF & stores), Reserving Extra Need could contribute to saving up to 105,000h of manual workload (estimation based on assumptions TBC)


Value enablers

Customer experience:
Enhance business customer satisfaction and trust, Reserving Extra Need could contribute to improving B2B customer happy score by +10%  (estimation based on assumptions TBC)

Co-worker experience:
Improved transparency
Increase reliability of information for B2B co-worker
Improved data quality and communication to B2B customer
Data & analytics in order to have a better overview to improve the process continuously​

Foundational improvements:

Reduce the risk of brand damage


Value Assumptions
Sales loss avoided globally per year is based on following assumptions: 

As an example, 100 Extra Needs are created in Japan per month.
On average, each Extra Need represents a value of 4 000 EUR.
When arriving to the Fulfilment unit, let's assume that 50% of the articles are sold to other customers.

Impacts:
The co-worker will not be able to fulfil the order according to the agreement with the business customer, but will spend time to create and monitor another Extra Need
The sale can be 100% lost: 10 KEUR; or 50% lost if the customer agrees to buy the available articles: 2 KEUR.
IKEA image will be tarnished
The business customer will be highly dissatisfied and may not order through IKEA again. The customer may even complain on social media and lower the Happy Customer Score or the Net Promotor Score
IKEA may have to pay high fines: + 2 KEUR. IKEA would have to pay extra storage costs for the remaining articles of the Extra Need unsold: 1 KEUR
According to the most pessimistic scenario, it could mean a direct loss of 2.4 to 4.8 MEUR per year in Japan. Not to mention the impacts on the image and loyalty.
By analysing different countries and extrapolating: it could mean a direct loss of 80 million euros per year Globally. Not to mention the impact on the image and loyalty.

Other Cost Details: No

Additional Information
Contribution to 10 jobs:

Job 9 - Create a more simple and low-cost IKEA
M2: Make IKEA simpler and better in the eyes of the many co-workers and customers
This will be achieved by implementing the STRUKTUR App generating system support with improved transparency and efficiency.
Job 4 - Create affordable services to make IKEA convenient
M2: Create service experiences that live up to IKEA customers needs
This is achieved by enabling reservation of goods for B2B customers when arriving at the final Fulfilment unit",,,1.0,,,,,,,,,,,,,,,Complex,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Fulfilment (SMC-113949),Order Management (SMC-113979),,,,,,663366497,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,Value Driver - Productivity increase,Value Driver - Revenue Increase,,,,,,,,,,,Extra Need Reservation (ENR) is a solution that will enable B2B co-workers to monitor and reserve/secure large B2B customer orders,dipou1,,,,,LAUNCH-4749,,,,,,Parking Lot,,,,,,,,,Pull,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|ic1rtz:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,ISOM (SMC-114681),Pythia (SMC-114880),SäljaPro (SMC-365052),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
ILOFF Apps,CHI-1844,2513359,Launch,Backlog,CHI,China,software,chher8,,,Low,,emniu2,ancor47,jirabot,2023-Oct-18 13:03,2023-Dec-15 07:51,2024-Feb-28 03:24,,,CFF,,,0,CFF-StoreOperations,New,Pilot,,,,,,,"*[ILOFF Apps - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/ILOFF+Apps+-+Launch+One+Pager]*",,jirabot,kylli1,,,,,,0,0,,0%,0,0,,,,,LAUNCH-4519,,,,,,,,,,,,,,,,,,,,,,"Expected Business Value (country level) 

Financial benefits (four main types):

Sales growth/gross profit: N/A

Cost Savings:
The product launch is not expected to lead to direct cost savings. However, potential savings may be realized through improved efficiency, as explained below.

Efficiency:
The introduction of ILOFF Apps is designed to streamline the login and app-switching process for co-workers. This is anticipated to significantly reduce the need for repetitive logins and app searches. We project a reduction in logins to an average of one per day for each logistics co-worker, as opposed to the current rate, which varies between 1 and 15 logins per day per co-worker. To measure the impact of these changes, we will conduct a qualitative survey to measure the happiness index during and after usage. Additionally, we will track quantitative data through a Google Analytics dashboard to monitor how the platform is utilized.

Cost Avoidance:
The product launch primarily contributes to cost avoidance on the digital side, such as application development and the mobile devices team. It simplifies the way apps are accessed, eliminating the need to individually add apps to RDT home screens, and ensures a consistent user experience. 
Non-financial benefits:

Create an excellent omni-channel experience for more of the many:

Value: ILOFF Apps serve as a pivotal entry point, ensuring seamless design and user experience. This consistency reflects unique IKEA experiences, benefiting Ingka IKEA co-workers.
Create affordable services to make IKEA convenient:

Value: ILOFF Apps optimize co-worker tasks, minimizing login times, and align with an affordable, profitable omnichannel fulfillment network.
Create the IKEA stores of tomorrow:

Value: ILOFF Apps fully align with this principle, building a robust foundation with single login and consistent interface. This strengthens our retail base, enabling the sharing of best practices, enhancing stores, and preparing for future store experiences.
Create a relevant and affordable offer for growth:

Value: ILOFF Apps offer streamlined access, envisioning third-party tools integration. Supporting IKEA Business Network partners' growth within the IKEA ecosystem.
Create a more simple and low-cost IKEA:

Value: ILOFF Apps simplify tasks, minimize login efforts, enhance efficiency, saving time, contributing financially, and enriching the work environment.
Create a people movement and make our culture and values a living reality:

Value: ILOFF Apps reinforce an improved work environment, facilitating access, enhancing tasks, and supporting IKEA's culture and values, benefiting the entire workplace.",,,1.0,,,,,,,,,,,,,,,Simple,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Inventory and Logistics Operations (SMC-113952),Store Fulfilment Operations (SMC-346707),,,,,,695121348,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,,,,,"ILOFF Apps is a portal for Logistics, Fulfillment and Recovery co-workers to access internal work-related applications in one place",vivol,,,,,LAUNCH-4519,,,,,,Parking Lot,,,,,,,,,Push,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|ibd5ov:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,ILOFF Apps (SMC-528773),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
SAMLA for CFF,CHI-1790,2350848,Launch,Backlog,CHI,China,software,chher8,,,Low,,emniu2,daost1,jirabot,2023-Jul-11 09:10,2023-Dec-19 15:37,2024-Feb-28 03:28,,,CFF,,,0,People&Culture,,,,,,,,,"*[SAMLA for CFF - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/SAMLA+for+CFF+-+Launch+One+Pager]*",,jirabot,kylli1,,,,,,0,0,,0%,0,0,,,,,LAUNCH-4285,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Simple,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,People (SMC-113948),People Planning (SMC-113985),,,,,,659916900,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,,,,,,hykon,,,,,LAUNCH-4285,,,,,,Parking Lot,,,,,,,,,Pull,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|iao6qv:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SAMLA (SMC-344048),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Customer order storage solution with automated handout (COSS-T) ,CHI-1743,2289185,Launch,Backlog,CHI,China,software,chher8,,,Low,,emniu2,chja,jirabot,2023-Jun-02 14:34,2023-Oct-23 10:38,2024-Feb-28 03:24,,,CFF,,,0,CFF-ServiceFulfilmentOperations,Delay,IKEA-App,IKEA-RCMP,IKEA-Store,IKEA-Web,,,,"*[Customer order storage solution with automated handout (COSS-T) - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/Customer+order+storage+solution+with+automated+handout+%28COSS-T%29+-+Launch+One+Pager]*

Customer order storage solution (COSS) is an automatic handout on trolleys to the customers. The solution provides storage capacity in a more condensed area. The COSS-T automated handout will provide more accessibility with less queuing and waiting times and will free up time for the co-workers in Furniture handout",,chnic4,jirabot,kylli1,,,,,0,0,,0%,0,0,,,,,LAUNCH-4269,,,,,,,,,,,,,,,,,,,,,,"Expected Business Value (country level) 

IKEA Plaisir Store, Paris, FR:
Increased turnover per year of 2,4 MEUR creating ROI of around 1,3 years for the investment
Reduce waiting times and queues at the merchandise pick-up area (3 min per order)
Increase the accessibility of the store for customer, 24/7
Increase the ability to offer more pick up slots (increase by 100%)
COVID-19, safer environment: We will provide both the customers and the IKEA store co-worker the social distancing on one hand and keep on delivering on the other
Frees up co-workers to better serve customers in the store
Watch this video from IKEA Plaisir store team for more details: IKEA_PLAISIR STORY v7.eng.mp4

IKEA Amsterdam, NL:
Reduce operational costs, increase efficiency while securing capacity and improving customer convenience
An annual saving of approximately 3 MEUR per year with a payback period of 4,7 years and an IRR (Internal rate of return) of 19,7%
Increase Click & Collect Sales
",,,1.0,,,,,,,,,,,,,,,Medium,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Fulfilment (SMC-113949),Delivery and Services (SMC-116978),,,,,,647120466,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,,,,,,adkup,,,,,LAUNCH-4269,,,,,,Parking Lot,,,,,,,,,Pull,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|iaewxj:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Customer Order Storage Self Serve Trolley (SMC-316105),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2023-Oct-23 10:38;chnic4;Launch is currently paused and it has been decided that solution is still under the development. More information in the Launch One Pager.;;;",,,,,,,,,,
Ingka Linehaul in Store Fulfilment ,CHI-1677,2219445,Launch,Backlog,CHI,China,software,chher8,,,Medium,,emniu2,bosam,jirabot,2023-Apr-21 11:45,2023-Oct-19 15:33,2024-Feb-28 03:28,,,CFF,,,0,CFF-CustomerOrderManagement,Dependency,,,,,,,,"h1. [Ingka Linehaul in Store Fulfilment - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/Ingka+Linehaul+in+Store+Fulfilment+-+Launch+One+Pager]",,emniu2,jirabot,kylli1,,,,,0,0,,0%,0,0,,,,,LAUNCH-3127,,,,,,,,,,,,,,,,,,,,,,"Expected Business Value (country level)
(Please also check the table in the Launch One Pager)

""Ingka Linehual in Store Fulfilment"", enables Stores to deliver to longer distances which 
1) Reduces delivery costs
2) Increases sales

From our Pilot Country Denmark, here are the forecasted numbers: (Zone 3 is the further zip code which was only covered by CDC without ""Ingka Linehual in Store Fulfilment"")
1) Store FF via LSC to Zone 3 reducing delivery cost from 850 DKK to 722DKK per delivery (Details are below)
2) Store FF via LSC to Zone 3 bringing monthly 1,5M DKK additional sales 

And for countries that are using workaround solutions to send indirect orders from Stores
* Compliancy and requirement for BOT (STO-LSC transportation settlement will be done via SBS)
* Giving visibility to delivery cost structure
* Giving visibility to order tracking

Other benefits to be validated:
* Flexibility to choose Linehual carrier (currently last mile TSP is covering the STO-LSC, with this deployment, stores can choose any carriers for this leg)
* Delivery cost difference between Central fulfilment and Store fulfilment (steering FF to store instead of Central impact on Lead time and FF costs)
* (For Split orders) Merging orders at LSC and reduce last mile delivery costs (This will be an additional development → Timeline:TBD)
* Impact on CO2e emission

Non-financial benefits:
With the reduction in delivery cost we are creating affordable services to make IKEA convenient (Job 04)

Value Assumptions
Reducing Delivery costs on long distance deliveries from Store by 10% based on Denmark (Please refer to the calculations rules in Expected business value)
Increasing sales by X % by allowing long distance deliveries from Store (X to be calculated according to base line and additional 1,5 Mio DKK sales)

Receiving Cost 
To be concluded after Denmark pilot.

Other cost/details
-

Additional Information/Links
-",,knzh,1.0,,,,,,,,,,,,,,,Medium,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Fulfilment (SMC-113949),Delivery and Services (SMC-116978),,,,,,403095087,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,,,,,,sotun,,,,,LAUNCH-3127,,,,,,Parking Lot,,,,,,,,,Push,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|ia4dhz:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Line hauler (SMC-168020),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2023-Apr-27 12:32;emniu2;This project is depending on CFB 1.3.3;;;",,,,,,,,,,
Reverse Flow: External Returns via TSP to Store,CHI-1630,2125995,Launch,Backlog,CHI,China,software,chher8,,,Low,,emniu2,elkar13,jirabot,2023-Feb-27 08:52,2023-Oct-06 07:40,2024-Feb-19 03:25,,,CFF,,,0,CFF-Availability&Support,Dependency,Pilot,,,,,,,"h1. [Reverse Flow: External Returns via TSP to Store - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/Reverse+Flow%3A+External+Returns+via+TSP+to+Store+-+Launch+One+Pager]

 

We will introduce a new customer return application (LAMNA) that will use ISOM. For store we will extend the FMS reverse receiving application for internal returns. For Finance new financial reports will be created.

 

{color:#de350b}*PLEASE IGNORE THE LINK TO HEAT PUMP LAUNCH.*{color} ",,elkar13,emniu2,jirabot,kylli1,penl,,,0,0,,0%,0,0,,,,,LAUNCH-4087,,,,,,,,,,,,,,,,,,,,,,"Financial benefits 
Cost savings (connected to reduction of manual tasks): Today, a return begins in SAMS, then moves to Centiro where a work order is created for the TSP. After that, the process is paper-based. With the move of the Return flow to ISOM along with other improvements (e.g. a more transparent flow, product updates by Robotic Process Automation, implementation of receiving capabilities in the unit and better insights into the reverse flow), the potential saving from reduction of manual tasks, is estimated to be 4.5 MEur on a global level.
4.5Meur (global) is based on the cost saving for the flow External Return from Customer to Fulfilment Unit. As the majority of countries send External Returns to Store the cost saving are within this MVP, for those who use a manual process today to send to Central unit the cost will be shared between this MVP and the parallel release of MVP: External Returns via TSP to Central Unit 
Efficiency: the capabilities developed support transfer of information between digital products reducing the time investment today in stand alone solutions that need to be updated in silo. 

Non-financial benefits:
This development supports Job 4 M2 ""Develop and implement a state-of-the-art reverse logistics
The digital capabilities will enable automated communication to Customers, with updates on the progress of their return
Meet volume growth and higher demand on Reverse flow created by the growing diversity of CMP
Improved decision-making through analytic insights into the flow
Contribution towards FMS, One Order and SOM digital strategies

Value Assumptions 
4.5 MEur is based on the reduction of manual tasks in SAMS linking the customer to a fulfilment unit, receiving at the store and the return moving to  recovery or replenished into the sales location to become buyable and sellable for the next customer. The introduction of digital tools for receiving and processing the return, with information sharing between products with updates by RPA and the movement into ISOM will reduce the hours invested into manual tasks.

In addition, hours are currently invested in investigation as to where returns are within the flow by CSC/SFO supporting customer queries and COM with open orders, the transparency of the ISOM flow will support a reduction in the hours invested in these manual tasks.

Projection calculation considers the yearly sales & volume correlation with the separation of Reverse Flow (within the Fulfilment Network) and front door returns (that do not require movement within the Fulfilment Network), with the expansion plans considering both comparable & non-comparable units.   The Projection of Return Flow in euros represents only the returns processed within the Fulfilment Network.

Base for the projections:
Conservative:
o   Maintain a total Return rate of 4%
o   Sales growth of 5% in FY22 and 10% in the remaining years
o   Keep the same Online share

Realistic:
o   Same sales growth calculation
o   Keep a similar online share for FY22, increase the online share for the remaining years with 6%-points
o   Calculate the additional online share with a higher return rate of 6.2%

The savings potential has been evaluated with 2 options: Realistic and Conservative.  The rate at which the saving can be achieved is directly linked to the agility of development within Digital, with the development and roll-out to the countries.  The MVP is expected to deliver 20% of the saving potential for the movement of Reverse Flow into ISOM and delivery of digital capabilities to remove manual task  

Receiving Cost
The receiving costs linked to this launch will be Opex: Training and translation (in relevant markets) PJIN009298

Additional Information Links
N/A",,rihua,1.0,,,,,,,,,,,,,,,Complex,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Fulfilment (SMC-113949),Order Management (SMC-113979),,,,,,590416257,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,,,,,,dahas9,,,,,LAUNCH-4087,,,,,,Parking Lot,,,,,,,,,Push,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|i9qfxj:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2024-Feb-15 00:00,2023-Oct-18 00:00,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2023-Apr-27 12:28;emniu2;Receive internal returns in store is the one of the  prerequisites. Change the plan date to FY24 T3 ;;;","2023-May-12 13:19;elkar13;Hej, I have deleted Plan maturity/tertial as these fields will be filled in by Central launch lead when the global launch schedule is ready.  These fields are completed by the Central Launch Lead who is assigned to the Push launch and should not be completed within the country.  ;;;",,,,,,,,,
Reverse Flow: External Returns via TSP to Central Unit,CHI-1629,2124455,Launch,Backlog,CHI,China,software,chher8,,,Low,,emniu2,elkar13,jirabot,2023-Feb-24 12:50,2023-Nov-10 07:39,2024-Feb-28 03:23,,,CFF,,,0,CFF-Availability&Support,Pilot,,,,,,,,"h1. [Reverse Flow: External Returns via TSP to Central Unit - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/Reverse+Flow%3A+External+Returns+via+TSP+to+Central+Unit+-+Launch+One+Pager]

 

We will introduce a new customer return application (LAMNA) that will use ISOM. For store we will extend the FMS reverse receiving application for internal returns. For Finance new financial reports will be created.",,elkar13,emniu2,jirabot,kylli1,mlzan,,,0,0,,0%,0,0,,,,,LAUNCH-4097,,,,,,,,,,,,,,,,,,,,,,"Financial benefits 
Cost savings (connected to reduction of manual tasks): Today, a return begins in SAMS, then moves to Centiro where a work order is created for the TSP. After that, the process is paper-based. With the move of the Return flow to ISOM along with other improvements (e.g. a more transparent flow, product updates by Robotic Process Automation, implementation of receiving capabilities in the unit and better insights into the reverse flow), the potential saving from reduction of manual tasks, is estimated to be 4.5 MEur on a global level.
4.5Meur (global) is based on the cost saving for the flow External Return from Customer to Fulfilment Unit. As the majority of countries send External Returns to Store the cost saving are within that MVP, for those who use a manual process today to send to Central unit the cost will be shared between this MVP and MVP: External Returns via TSP to Store 
Efficiency: the capabilities developed support transfer of information between digital products reducing the time investment today in stand alone solutions that need to be updated in silo. 

Non-financial benefits:
This development supports Job 4 M2 ""Develop and implement a state-of-the-art  reverse logistics
The digital capabilities will enable automated communication to Customers, with updates on the progress of their return
Meet volume growth and higher demand on Reverse flow created by the growing diversity of CMP
Improved decision-making through analytic insights into the flow
Contribution towards FMS, One Order and SOM digital strategies

Value Assumptions 
4.5 MEur is based on the reduction of manual tasks in SAMS linking the customer to a fulfilment unit,  receiving at the store and the return moving to  recovery or replenished into the sales location to become buyable and sellable for the next customer. The introduction of digital tools for receiving and processing the return,  with information sharing between products with updates by RPA and the movement into ISOM will reduce the hours invested into manual tasks.

In addition hours are currently invested in investigation as to where returns are within the flow by CSC/SFO supporting customer queries and COM with open orders, the transparency of the ISOM flow will support a reduction in the hours invested in these manual tasks.

Projection calculation considers the yearly sales & volume correlation with the separation of Reverse Flow (within the Fulfilment Network) and front door returns (that do not require movement within the Fulfilment Network), with the expansion plans considering both comparable & non-comparable units.   The Projection of Return Flow in euros represents only the returns processed within the Fulfilment Network.

Base for the projections:
Conservative:
o   Maintain a total Return rate of 4%
o   Sales growth of 5% in FY22 and 10% in the remaining years
o   Keep the same Online share

Realistic:
o   Same sales growth calculation
o   Keep a similar online share for FY22, increase the online share for the remaining years with 6%-points
o   Calculate the additional online share with a higher return rate of 6.2%

The savings potential has been evaluated with 2 options: Realistic and Conservative.  The rate at which the saving can be achieved is directly linked to the agility of development within Digital, with the development and roll-out to the countries.  The MVP is expected to deliver 20% of the saving potential for the movement of Reverse Flow into ISOM and delivery of digital capabilities to remove manual tasks.

Receiving Cost
The receiving costs linked to this launch will be Opex: Training and translation (in relevant markets)   PJIN009298 ",,snwe,1.0,,,,,,,,,,,,,,,Complex,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Fulfilment (SMC-113949),Order Management (SMC-113979),,,,,,590420676,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,,,,,,dahas9,,,,,LAUNCH-4097,,,,,,Parking Lot,,,,,,,,,Pull,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|i9q7wf:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2024-Feb-22 00:00,2023-Nov-08 00:00,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2023-Apr-10 08:36;emniu2;Per Miles, it's too early to make the judgement based on one pager description so far. wait for the global awareness session to further understand it once been finalized later. ** **;;;","2023-May-12 13:29;elkar13;Hej, I have deleted Plan maturity/tertial as these fields will be filled in by Central launch lead when the global launch schedule is ready.  These fields are completed by the Central Launch Lead who is assigned to the Push launch and should not be completed within the country.  ;;;","2023-Jul-07 08:32;mlzan;CN will combine with External Return via TSP to Store project to evaluate if the central unit option is needed. ;;;",,,,,,,,
Goal based order allocation,CHI-1442,1775283,Launch,To Do,CHI,China,software,chher8,,,Highest,,emniu2,chja,jirabot,2022-Oct-07 11:11,2023-Dec-15 04:39,2024-Feb-28 03:20,,,CFF,,2024-Nov-29 00:00,1,cff,"CFF,",CFF-Availability&Support,pending,Pilot,,,,,"h1. [Goal based order allocation - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/Goal+based+order+allocation+-+Launch+One+Pager]",,jirabot,kylli1,penl,SHZHU,,,,0,0,,0%,0,0,,,,,LAUNCH-3128,,,,,,,,,,,,,,,,,,,,,,,,penl,,,,,,,,,,,,,,,,Complex,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Fulfilment (SMC-113949),Order Promise and Allocation (SMC-113994),,,,,,403095001,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,,,,,,chnue1,,,,,LAUNCH-3128,,,,,,FY25 - T1,,,,,,,,,Push,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|i87j7j:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Order Allocation (SMC-115014),,,,,2024-Nov-01 00:00,2024-Aug-02 00:00,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
STRUKTUR App,CHI-1351,1726965,Launch,To Do,CHI,China,software,chher8,,,Low,,SOLIU10,ansua10,jirabot,2022-Sep-09 11:41,2024-Feb-14 15:04,2024-Feb-28 03:28,,,CFF,,,0,CFF,CNSecurity_Reviewing,Commercial-IKEAforBusiness,Delay,Pilot,,,,,"h1. [STRUKTUR App - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/STRUKTUR+App+-+Launch+One+Pager]",,chher8,emniu2,jirabot,kylli1,maers,,,0,0,,0%,0,0,,,,,LAUNCH-3965,,,,,,,,,,,,,,,,,,,,,,"Expected Business Value (country level)

The business value stated below are rough estimations on Global level which will be validated during pilot period and as more countries start using the App and new functionality.

Expected financial impact of implementing full scope including segmentation:
STRUKTUR is a pre-requisite for the ""Extra Need Segmentation"" which together will contribute to:

80 MEUR of sales loss avoided globally per year
139.000 hours in reduction of manual workload and time saved globally per year. 

See more information on financial impact in Launch One Pager.

Note; The intent is not to reduce any hours connected to the B2B order process, but rather support that co-workers have the possibility to focus on what is important; Support the expected sales growth within the B2B and support our customers in best possible way.

Non-financial benefits:

Improved transparency
Increase reliability of information for B2B co-worker & customers
Improved data quality and communication to B2B customer
Strengthen end-to-end process of Handling B2B requests by adding more functions based on existing Working method
Data & analytics in order to have a better overview and improve the process continuously​

Value Assumptions

STRUKTUR is a pre-requisite for the ""Extra Need Segmentation"" which together will contribute to 80 MEUR of sales loss avoided globally per year. 

As an example, 100 Extra Needs are created in Japan per month.
In average, each Extra Need represents a value of 4 000 EUR.
When arriving at the fulfilment unit, let's assume that 50% of the articles are sold to other customers.
Impacts:

The co-worker will not be able to fulfil the order according to the agreement with the business customer, but will spend time to create and monitor another Extra Need
The sale can be 100% lost: 10 KEUR; or 50% lost if the customer agrees to buy the available articles: 2 KEUR.
IKEA image will be tarnished
The business customer will be highly dissatisfied and may not order through IKEA again. He or she may even complain on social media and lower the NPS
IKEA may have to pay high fines: + 2 KEUR. IKEA would have to pay extra storage costs for the remaining articles of the Extra Need unsold: 1 KEUR
According to the most pessimistic scenario, it could mean a direct loss of 2.4 to 4.8 MEUR per year in Japan. Not to mention the impacts on the image and loyalty.
By analyzing different countries and extrapolating: it could mean a direct loss of 80 million euros per year Globally. Not to mention the impact on the image and loyalty.

The Expected Business value of implementing the STRUKTUR App MVP is based on the following assumptions:

On average 318 Extra need requests for B2B customers are created/country/month (based on the average of input from four markets)
We estimate that implementing the STRUKTUR App MVP will contribute to save 36 minutes/extra need request
Based on 30 markets
Based on staff cost of 62 euro per hour 

Receiving Cost
 No

Other cost/details
How to cover for license cost for FY24 onwards is under discussion. How to cover for license cost for FY24 onwards is under discussion.",,reyan3,1.0,,,,,,,,,,,,,,,Medium,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,New Business (SMC-706399),IKEA for Business (SMC-113977),,,,,,435764915,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,,,,,,<EMAIL>,,,,,LAUNCH-3965,,,,,,FY24 - T2,,,,,,,,,Push,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|i80ci7:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2023-Dec-28 00:00,2023-Sep-04 00:00,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2022-Dec-19 05:57;chher8;[~WEHAN9] [~emniu2] we are going to do a UAT for STRUKTUR in China in January. As per my understanding the UAT will be using test data but I think it will still be good to have ISDP involvement to make sure we stick with local ISDP compliance even during UAT;;;","2023-Mar-24 08:29;emniu2;Struktur China pilot is planned in October 2023.;;;","2023-Oct-19 09:58;maers;The issues found in Pilot UAT turned out to be more challenging to solve than expected and more time is needed for investigation. 

What happens now? 
Existing Pilot plans are cancelled, and rollout needs to be re-planned. The team needs to do an analysis and conclude on what is needed to move forward. ;;;",,,,,,,,
Auto Recovery of customer order deviation,CHI-1232,1606046,Launch,In Progress,CHI,China,software,chher8,,,High,,SOLIU10,filu,jirabot,2022-Jun-17 11:46,2024-Jan-10 09:42,2024-Feb-28 03:28,,,CFF,RCMP,,0,CFF,CNSecurity_To_Do​,Dependency,Pending,RCMP,,,,,"h1. [Auto Recovery of customer order deviation - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/Auto+Recovery+of+customer+order+deviation+-+Launch+One+Pager]",,emniu2,jirabot,junlu5,kylli1,penl,SOLIU10,,0,0,,0%,0,0,,,,,LAUNCH-3095,,,,,,,,,,,,,,,,,,,,,,,,BIALI4,,,,,,,,,,,,,,,,Simple,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Fulfilment (SMC-113949),Order Management (SMC-113979),,,,,,412268773,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,,,,,,dipou1,,,,,LAUNCH-3095,,,,,,FY24 - T3,,,,,,,,,Pull,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|i7hx13:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2022-Jul-28 07:36;emniu2;go decision from CSC. The solution is ready for deploy, when and how will lead the implementation?

Business owner: Acho Shen;;;","2022-Jul-28 10:09;emniu2;Check with Acho who can lead this project;;;","2023-Mar-02 07:47;emniu2;ODM is not available in China. This project is blocked by ODM;;;","2023-Mar-06 07:52;emniu2;ODM ETA is in FY24 T1;;;","2023-Apr-10 09:11;emniu2;Missing ODM as a hard dependency for this initiative. ODM team can only deploy ODM in FY24 due to their capacity limitation.;;;","2023-Apr-11 06:23;SOLIU10;The current auto-recovery solution is in GCP, so at present, it can't be launched in China.

Once ODM is ready with the cloud solution for China (potentially FY24), global team will plan on this launch. ;;;","2024-Jan-10 09:42;SOLIU10;*Update -* 

Currently global team is working on setting up basic infrastructure of ODM on Ali Cloud, target launch in FY24 T2.

Auto recovery work will kick off once above is finalized, estimate kick off time in T3.;;;",,,,
Receive internal returns in store,CHI-1151,1518451,Launch,To Do,CHI,China,software,chher8,,,Low,,SHZHU,SGYK,jirabot,2022-Apr-25 09:18,2023-Nov-10 07:38,2024-Feb-28 03:28,,,CFF,Lead by Business,,0,CFF,CNSecurity_Reviewing,Dependency,,,,,,,"h1. [Receive internal returns in store - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/Receive+internal+returns+in+store+-+Launch+One+Pager]",,emniu2,jirabot,kylli1,penl,SHZHU,,,0,0,,0%,0,0,,,,,LAUNCH-2957,,,,,,,,,,,,,,,,,,,,,,"Expected Business Value (country level)

Financial benefits:

See assumptions in Value Assumptions.

Possible benefits in scope: 

62.5 hours per week on co-worker efficiency (Global) 
2 hours per week per country based on reporting efficiency 
2 hours per week per store based on reporting efficiency 
Possible benefits in scope (EUR): 

62.5 * 52 * 28 = 91.000 EUR possible cost savings
2 * 52 * 32 * 28 = 93.184 EUR possible cost savings
2 * 52 * 384 * 28 = 1.118.208 EUR possible cost savings
Total 1.302.392 EUR possible cost savings per year

Non-financial benefits:
Strategically unlocking further rollout of ISOM external returns receiving, and minimising related technical and user experience -related risks
Support and contribute with a better stock accuracy and less oversell

Value Assumptions

These value assumptions and benefits to be validated and confirmed end of T3 as part of the pilot.

62,5 hours / week processing efficiency (global)
Potential time saved for registering a return CDU (parcel / pallet): 30 sec (from 1.5mins to 1min)
2 hours / week SO time saved for creating open order report and following-up with the stores (per market)
2 hours / week store inbound team lead following-up on the open orders based on the report (per store)
Global average hourly cost - 28 EUR
Expected volume of internal returns (global), based on FY21 and not taking into account volume increase: 7500 CDU / week
Potential time saved for central SFO & store logistics for follow-up activities regarding open orders: 2hrs / week / market + 2hrs / week / store
Complete elimination of issues where return cannot be received in the 'not planned' store
Follow-up enabled for the store logistics team lead / gatekeeper with additional data insight regarding return CDU status (expected / dispatched / delivered)
Actionable insights enabled for 'open orders / CDUs' for store logistics
Potential time saved for store logistics co-workers:
Reducing the amount of returns which are today 'Not found' in SGF W13 and have to be registered manually
By eliminating the issues where return cannot be received in the 'not planned' store
By streamlining the integration with ISOM
Reducing time to register each given CDU with improved UI / UX
Minimising mistakes and required rework during CDU registration with improved UI/UX
Reduced oversells for sellable articles not physically at sales locations due to the stock transactions are initiated and then confirmed when being put back to stock

Receiving Cost
No 

Other cost/details
Solution will be a web-app available on desktop and handheld RDT devices. It is assumed that the necessary hardware is already available at the stores
Translations

",,maliu23,1.0,,,,,,,,,,,,,,,Medium,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Inventory and Logistics Operations (SMC-113952),Store Fulfilment Operations (SMC-346707),,,,,,390849328,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,Digital/Business Capability -> Store Logistic Operations,,,,,ankly1,,,,,LAUNCH-2957,,,,,,FY24 - T1,,,,,,,,,Push,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|i74uvb:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,FMS Inbound Planning (SMC-346715),FMS Reverse Receiving (SMC-346717),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2022-Jun-22 12:06;emniu2;China is  planned in T3 fy23. The launch lead will be decided when the launch is close.;;;","2022-Oct-31 06:33;SHZHU;[~emniu2] ,

Please check whether cloud migration is needed for this project. ;;;","2023-Jan-12 09:17;emniu2;Cloud migration is needed. Will contact global engineering team to check if when they plan to deploy it for China;;;","2023-Mar-01 06:36;emniu2;cloud migration ETA is in FY24 T1;;;",,,,,,,
Picking Capacity at Order Line Level,CHI-1124,1443931,Launch,Backlog,CHI,China,software,chher8,,,Low,,mlzan,bosam,jirabot,2022-Mar-08 08:47,2024-Jan-15 09:45,2024-Feb-28 03:21,,,CFF,Lead by Business,,1,CFF,Dependency,,,,,,,,"h1. [Picking Capacity at Order Line Level - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/Picking+Capacity+at+Order+Line+Level+-+Launch+One+Pager]


 ",,emniu2,jirabot,kylli1,penl,,,,0,0,,0%,0,0,,,,,LAUNCH-2884,,,,,,,,,,,,,,,,,,,,,,"Financial benefits consist of four main types:
Cost saving: 21 hours/month per country of manual work (within SFO team) to enforce picking capacity

Non-financial benefits:
Reduce the risk of delaying orders and hence not fulfilling the promise to our customers​
Better utilizing existing Picking capacity​
Decreasing the lead time for delivery

Value Assumptions
Calculation of 21 hours/month spent on closing timeslots (based on Italy)

Low season period (8 months): 10 co-workers*1h = 10h or 10h/8months=1,25h per month or ~8min per month per SFO co-worker
High season (4 months Oct-Jan): 10 co-workers*24h = 240h or 240h/4month=60h per month or 6h per month per SFO co-worker
On average, it is around ~5 minutes per slot and in the high season it will be 3-4 slots per work day per co-worker. All 10 SFO co-workers need to close the slots in the delivery templates in their sourcing areas.

Receiving Cost
No receiving cost",,,0.1,,,,,,,,,,,,,,,Complex,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Fulfilment (SMC-113949),Order Promise and Allocation (SMC-113994),,,,,,381128155,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,Digital/Business Capability -> Order Promise,,,,,samur4,,,,,LAUNCH-2884,,,,,,FY24 - T2,,,,,,,,,Push,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|i6uc5z:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Picking Capacity (SMC-180957),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2022-Mar-11 03:32;emniu2;Will talk to global product owner to discuss if the cloub migration is needed for this project or not.;;;","2023-Feb-28 10:27;emniu2;Cloud migration is needed. will get a ETA from global team for cloud migration.;;;",,,,,,,,,
Full serve orders,CHI-1065,1380153,Launch,Backlog,CHI,China,software,chher8,,,Low,,SHZHU,chja,jirabot,2022-Jan-31 09:48,2023-Oct-06 07:40,2024-Feb-28 03:28,,,CFF,,2023-Mar-31 00:00,0,CFF,CNSecurity_Reviewing​,Dependency,,,,,,,"[Full serve orders - Launch One Pager|https://confluence.build.ingka.ikea.com/display/DCPD/Full+serve+orders+-+Launch+One+Pager]",,emniu2,fogue,jirabot,kylli1,SHZHU,,,0,0,,0%,0,0,,,,,LAUNCH-2840,,,,,,,,,,,,,,,,,,,,,,"Expected Business Value (country level)



Financial benefits:

10h/week= 520 hours saved for other value adding activities/store/year

520h x 25€ = 13000€ saved for other value adding activities/store/year

Non-financial benefits:.

Shorter overall waiting time for customer creating a better shopping experience.

The solution as such is closing the gap between channels, enhancing and creating a better omnichannel experience for our customers.

Better capacity and handout capacity management create more growth opportunities, enabling us to utilize our resources in a more efficient way:

The expected time saved from staging is 15 mins on average!
Value Assumptions

An average store doing 2000 Full serve order/week, with a time of 2 mins, with a 15% productivity increase, this will be 10 hr/week. 

We expect 15% increase in the picking of full serve, as the outcome of being able to plan orders based on Cut off, as well as group to support cluster picking in a much better way. 

This is estimated based on similar and previously changes in the customer order flow within CPS where we had added more features/data to pick in a smarter way.

Receiving Cost

No

Other cost/details

The effort for change management and training of the new front end how to sell this service will be determined after the MVP.

Additional Information/Links

Most likely we will do a co-worker survey before, during and after the MVP to understand the soft values for our co-workers when it comes to stress, prioritisation and working environment.",,LYYAN1,0.1,,,,,,,,,,,,,,,Complex,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Fulfilment (SMC-113949),Order Management (SMC-113979),,,,,,380473759,,,,,,,,,,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,Digital/Business Capability -> Order Management,,,,,dahas9,,,,,LAUNCH-2840,,,Draft,,,FY24 - T2,,,,,,,,,Push,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|i6gt73:",2023-Feb-27 00:00,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2023-Dec-31 00:00,2022-Sep-30 00:00,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2022-Mar-11 03:33;emniu2;New picking module is a dependency for this project. 
Change the plan tertial to T3.;;;","2022-Apr-07 11:51;emniu2;The importance of full serve order is the system continuity. we need to update the system in order to retire the legacy system, hence mark the business value as 1.;;;","2022-Jul-28 10:32;emniu2;The rollout plan for China is around T2 FY23. New picking module is planned between T1 to T2 FY23 for rollout.;;;","2023-Apr-27 09:56;emniu2;Global GIS team doesn't have capacity to deploy GIS for China. This project is pending because of missing GIS;;;","2023-Jun-07 10:16;fogue;[~SHZHU] I asked about the GIS capacity , the answer is that estimation is that this will not be earlier than start of next calendar year . thanks ;;;",,,,,,
Self-service – Modify Order,CHI-609,1065249,Launch,Backlog,CHI,China,software,chher8,,,Low,,SOLIU10,isaber,isaber,2021-Aug-27 10:31,2024-Feb-14 15:33,2024-Feb-28 03:28,,Parking Lot,RCMP,,,0,CFF,CN_local_dev_need,Commercial,CSC,Delay,Dependency,Pilot,RCMP,Self_Service,Link to one pager: https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=387368986,,ELCHE36,FACAI,jimgu,junlu5,kylli1,,,,,,,,,,,CHI-1118,,,LAUNCH-2719,"2022-Jul-07 14:58;makat16;Copy of OCM - NO (1).xlsx;https://jira.digital.ingka.com/secure/attachment/524701/Copy+of+OCM+-+NO+%281%29.xlsx",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Medium,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Digital and Remote Customer Meeting Points (SMC-706400),Remote Meeting Points (SMC-113962),,,,,,387368986,,,,,,,,,Work package placeholder,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,Digital/Business Capability -> Customer Support and Returns,,,,,nidas1,,,,,LAUNCH-2719,,,,,,Parking Lot,,,,,,,,,Pull,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|i5855z:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Self-service Order Management (SMC-116974),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2021-Nov-03 06:46;jimgu;GAP assessment result has done, in below url:
https://confluence.build.ingka.ikea.com/display/COS/Block+2+Self+Service+Gap+Analysis

Plan to start plan details with product team in T2 when it setup adjustment done, and CNOS launched in December.;;;","2022-Jul-07 14:59;makat16;After discussion with the global team (PO - [<EMAIL>|mailto:<EMAIL>], PS - [<EMAIL>|mailto:<EMAIL>], Devs - [<EMAIL>|mailto:<EMAIL>], [<EMAIL>|mailto:<EMAIL>]):
 * This launch can cover partial cancellation (items and services) capability. Global order cancellation launch CHI-197 only covers whole order cancellation
 * Modify order launch CHI-609 covers: ""adding/removing articles from an existing order before the order is picked in the fulfilment units.""
 * Nirupom told me about adding/removing services: There is plan but we have to wait for SOP API support, that API is not built yet

 * Also from Nirupom about partial refund: If we are talking about remove item/remove quantity, then surely there has to be refund of that remove item/quantity

 * Global development is currently in early MVP stage - waiting for some APIs to be developed. We either wait for global launch (scheduled at T3 FY22, but can be later) or go with local development. Global launch is preferred
 * Here is the link to the most recent OCM (order change matrix) - [https://confluence.build.ingka.ikea.com/display/SELLING/Order+Change+Matrix]. Also, there is a file [^Copy of OCM - NO (1).xlsx]

 ;;;","2022-Oct-27 07:37;FACAI;hi [~makat16]  Is there any update about  order partical cancellation?;;;","2022-Nov-03 06:35;ELCHE36;*Dependency* :   SOP CHI-1118  -- FS --> Modify Order

Modify order ticket is pending with SOP ( will be updated by Nina );;;",,,,,,,
Prime Slot Pricing,CHI-295,638828,Launch,Backlog,CHI,China,software,chher8,,,Low,,javan52,FRKNU4,isaber,2021-Feb-15 15:07,2023-Aug-16 04:57,2024-Feb-28 03:21,,Parking Lot,Commercial,,2024-Jul-01 00:00,0,CFF,Commercial,Resource_bus,,,,,,,"Link to one pager: [https://iweof.sharepoint.com/:f:/r/teams/o365g_digitalcountrieslaunchworkspace_itsemal/Shared%20Documents/Launches/Prime%20Slot%20Pricing?csf=1&web=1&e=MydGaj] 

 

training material updated",,javan52,kylli1,lewen,sojia,,,,,,,,,0,,,,,,LAUNCH-2364,,,,,,,,,,,,,,,,,,,,,,,DVWG,,,,,,,,,,,,,,,,Medium,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Fulfilment (SMC-113949),Delivery and Services (SMC-116978),,,,,,378407025,,,,,,,,,Work package placeholder,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,Digital/Business Capability -> Delivery and Services,,,,,muram5,,,,,LAUNCH-2364,,,,,,Parking Lot,,,,,,,,,Pull,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|i3dyjz:",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,ISOM (SMC-114681),,,,,2024-Sep-04 00:00,2024-Aug-31 00:00,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2021-Jul-20 08:33;sojia;To check with commercial to see if it's needed for China.;;;","2021-Sep-13 07:41;sojia;Business Owner: David Wang;;;","2021-Sep-13 07:41;sojia;Email has been sent to Daivd to understand the business value for CN.;;;","2021-Nov-18 07:01;sojia;Feedback from David Wang:

Due to the quantity of the first round customer survey is not enough, so we are contacting more channels to help us gather customer insights, we aim to finish this patrt by the end of Nov.;;;","2021-Nov-29 03:14;lewen;Been in contact with David, business need is expected to be communicated by w.48;;;","2021-Dec-15 11:02;lewen;Retail are NOT ready to provide input on need for this solution at this time. Target is to provide feedback on need during T2. for now nothing will be done until this has been provided.

I have communicated that assumed earliest expected deploy is T3;;;","2022-Nov-24 02:31;lewen;[~javan52]Business (David) have not provided input on need, ie. we do not have a BRD. I have also not followed up David on the topic. 

Suggested next step is to Sync with David to define the need

Jaap will take over ownership from CN digital for this solution 

;;;","2023-Jan-04 05:10;javan52;spoke with [~DVWG]  and it's not a priority for now;;;","2023-May-24 05:07;javan52;reached out to commercial team again to see if it's needed for China ;;;","2023-Aug-03 08:24;javan52;not the right time for China, RoD has been removed and limited how many changes we can/will make on the pricing to avoid confusion. Will check again next tertial;;;","2023-Aug-16 04:57;javan52;discussed with Business Owner, We will not pursue this for the upcoming year. revisit the proposition towards FY25. ;;;"
Transport Desk after the Cash line (CFB 1.3.1),CHI-187,519014,Launch,In Progress,CHI,China,software,chher8,,,High,,emniu2,chja,sanor3,2020-Nov-25 14:46,2023-Dec-06 10:24,2024-Feb-28 03:28,,,Commercial,,2023-Mar-03 00:00,0,CFF,CNSecurity_Reviewing,Commercial,In_Store,,,,,,"One pager can be found here: https://iweof.sharepoint.com/teams/o365g_digitalcountrieslaunchworkspace_itsemal/Shared%20Documents/Forms/AllItems.aspx?RootFolder=%2fteams%2fo365g%5fdigitalcountrieslaunchworkspace%5fitsemal%2fShared%20Documents%2fLaunches%2fTransport%20Desk%20after%20the%20Cash%20line%20%28CFB%201%2e3%2e1%29&FolderCTID=0x012000C1D60394C2559A46A0139FB21150E0CA
",,alpos6,chher8,emniu2,kylli1,lewen,manag6,sojia,,,,,,,,,,CHI-553,,LAUNCH-2193,,,,,,,,,,,,,,,,,,,,,"Expected Value: Transport Desk do not have direct impact on incremental sales or on the operational cost as the solution will integrate orders from iSOM to Salja Go, there will be non financial benefits leading to a better customer experience and towards ease of shopping.
Value Assumptions: 
Making the transport desk order flow legally and financially compliant.
Better transport capacity management for home delivery flows from Store.
Better customer experience with improved pack & handout process.
Remove manual steps in the operational flow. ""Convenience ladder” can be applied to transport desk orders as well.
One of the big steps towards “One Order Strategy”.
Another big step towards de-commissioning COMFACADE.
SIM, an important product in MHS transformation goes live with this solution.
Solution fulfils important pre-requisite for cross-border delivery from store",,LYYAN1,0.1,,,,,,,,,,,,,,,Medium,,,,,,,China,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Fulfilment (SMC-113949),Order Promise and Allocation (SMC-113994),,,,,,374576222,,,,,,,,,Work package placeholder,,,,,,,,,,,,,,,,,Central,,,,,,,,,,,Digital/Business Capability -> Order Management,,,,,dahas9,,,,,LAUNCH-2193,,,Target,,,FY24 - T2,,,,,,,,,Push,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|i2w247:",2024-Jan-23 00:00,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,ISOM (SMC-114681),,,,,2023-Nov-07 00:00,2022-Feb-01 00:00,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2021-May-20 13:02;chher8;Resource conflicts for CFB related projects. Assessing timeline;;;","2021-Jul-20 08:07;sojia;To check with commercial for the next step and requested timeline;;;","2021-Jul-26 08:19;lewen;Since we don't have Sälja Go/Pro in china we do not know if we can implement this solution since there is a dependency on Sälja Go/Pro. 

If we can ""workaround"" the dependency then the plan is to deploy this solution in china during T122. 

Plan is to implement Salja Pro in China during T222, time is not confirmed yet. 

*Next Step:*
 * Identify dependency on Sälja Go/Pro 
 ** If we can deploy the solution without Sälja Go/pro - target implementation time is FY22T1
 ** If we can +NOT+ deploy the solution without Sälja Go/pro - target implementation time is FY22T3

br

Lee 

 ;;;","2021-Jul-26 12:18;lewen;Feedback from Adam Prescott is that Transport desk orders can only be created in Salja GO.  Based on this input the assumption is that we can not deploy transport desk without ether Salja Pro or Go. 

Most likely scenario is that we will need to go for FY22T3 deployment of this solution 

*Next step:*
 # Identify if we can launch a lite version of Salja Go, short term i.e. only enable for the coworkers that will use it for transport desk and validate that no of the solutions needed is subject for local development

 

 ;;;","2021-Jul-26 13:52;chher8;[~manag6] should be able to support on this. He has worked with other countries to rollout Transport Desk Solution that did not have Salja Go enabled. ;;;","2021-Aug-19 13:26;manag6;[~dahas9] [~alpos6] can you please support in answering the question on Salja Go dependency for Transport desk launch? Is there any workaround/lite version possible? Thanks.;;;","2021-Aug-19 14:48;alpos6;It is possible to roll out SÄLJA GO for transport desk co-workers only. Other co-workers can continue to use iSell and only people on transport desk will use SÄLJA GO to create transport desk orders. Later country can launch the SäljaPro in all areas and it will replace SÄLJA GO on transport desks.

But we need help from Chinese team to assess SÄLJA GO before start.;;;","2021-Sep-09 07:12;lewen;Assessment of SALJA GO is ongoing (together with overall technical discussion to move Sälja PRO to china) . We have identified technical changes to Sälja GO to enable the transport desk related features to be enabled in china. Meeting to be booked on the topic with [~alpos6] and other stakeholders during next week. 

 

 ;;;","2021-Oct-27 09:26;lewen;We have alignment that Sälja Go will be deployed to china we also have got a OK from PO to do needed changes in Salja GO to deploy to china 

Time line is that we are targeting full rollout in China for T2 (transport desk related components, if possible full solution). - analysis on changes needed to be done is expected to be done during T1 

focus is:
 # Understand what features is needed for Transport desk and prioritize deployment/localization of this components 
 # Understand remaining components that transport desk is not dependent on. Purpose is to understand the feasibility to deploy full solution by T2.

 ;;;","2021-Nov-04 08:22;sojia;To handover to [~emniu2];;;","2022-Jan-11 10:52;emniu2;Since transport desk is depending on Salija pro. Salija pro is under development. This project most likely will move to T3.;;;"
