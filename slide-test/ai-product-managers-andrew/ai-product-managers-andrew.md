# AI Product Managers Will Be In-Demand As the cost of building AI products falls, demand for people who know what to build will rise. Get ready for an explosion in AI Product Management!

[

Letters

](https://www.deeplearning.ai/the-batch/tag/letters/)[

Business Insights

](https://www.deeplearning.ai/the-batch/tag/business-insights/)

Published

[

Jan 15, 2025

](https://www.deeplearning.ai/the-batch/tag/jan-15-2025/)

Reading time

3 min read

![Two colleagues discuss their chatbot’s success; one suggests hiring an AI Product Manager.](https://www.deeplearning.ai/_next/image/?url=https%3A%2F%2Fdl-staging-website.ghost.io%2Fcontent%2Fimages%2F2025%2F01%2FAIProductManager-2_1200px-2.jpg&w=3840&q=75)

![Two colleagues discuss their chatbot’s success; one suggests hiring an AI Product Manager.](/_next/image/?url=https%3A%2F%2Fdl-staging-website.ghost.io%2Fcontent%2Fimages%2F2025%2F01%2FAIProductManager-2_1200px-2.jpg&w=3840&q=75)

Share

-   [](https://twitter.com/intent/tweet?url=https://www.deeplearning.ai/the-batch/ai-product-managers-will-be-in-demand/)
-   [](https://www.facebook.com/sharer/sharer.php?u=https://www.deeplearning.ai/the-batch/ai-product-managers-will-be-in-demand/)
-   [](https://www.linkedin.com/shareArticle?mini=true&url=https://www.deeplearning.ai/the-batch/ai-product-managers-will-be-in-demand/)

Dear friends,

Writing software, especially prototypes, is becoming cheaper. This will lead to increased demand for people who can decide what to build. AI Product Management has a bright future!

Software is often written by teams that comprise Product Managers (PMs), who decide what to build (such as what features to implement for what users) and Software Developers, who write the code to build the product. Economics shows that when two goods are complements — such as cars (with internal-combustion engines) and gasoline — falling prices in one leads to higher demand for the other. For example, as cars became cheaper, more people bought them, which led to increased demand for gas. Something similar will happen in software. Given a clear specification for what to build, AI is making the building itself much faster and cheaper. This will significantly increase demand for people who can come up with clear specs for valuable things to build.

This is why I’m excited about the future of Product Management, the discipline of developing and managing software products. I’m especially excited about the future of AI Product Management, the discipline of developing and managing AI software products.

Many companies have an Engineer:PM ratio of, say, 6:1. (The ratio varies widely by company and industry, and anywhere from 4:1 to 10:1 is typical.) As coding becomes more efficient, I think teams will need more product management work (as well as design work) as a fraction of the total workforce. Perhaps engineers will step in to do some of this work, but if it remains the purview of specialized Product Managers, then the demand for these roles will grow.

This change in the composition of software development teams is not yet moving forward at full speed. One major force slowing this shift, particularly in AI Product Management, is that Software Engineers, being technical, are understanding and embracing AI much faster than Product Managers. Even today, most companies have difficulty finding people who know how to develop products and also understand AI, and I expect this shortage to grow.

Further, AI Product Management requires a different set of skills than traditional software Product Management. It requires:

-   **Technical proficiency in AI.** PMs need to understand what products might be technically feasible to build. They also need to understand the lifecycle of AI projects, such as data collection, building, then monitoring, and maintenance of AI models.
-   **Iterative development.** Because AI development is much more iterative than traditional software and requires more course corrections along the way, PMs need to understand how to manage such a process.
-   **Data proficiency.** AI products often learn from data, and they can be designed to generate richer forms of data than traditional software.
-   **Skill in managing ambiguity.** Because AI’s performance is hard to predict in advance, PMs need to be comfortable with this and have tactics to manage it.
-   **Ongoing learning.** AI technology is advancing rapidly. PMs, like everyone else who aims to make best use of the technology, need to keep up with the latest technology advances, product ideas, and how they fit into users’ lives.

Finally, AI Product Managers will need to know how to ensure that AI is implemented responsibly (for example, when we need to implement guardrails to prevent bad outcomes), and also be skilled at [gathering feedback fast](https://www.deeplearning.ai/the-batch/how-to-get-user-feedback-to-your-ai-products-fast/?utm_campaign=The%20Batch&utm_source=hs_email&utm_medium=email&_hsenc=p2ANqtz-8E3vaHtVCWyWM5X79V_-x7uED1QR5exdS62WzX7rqOxTAyyDUu5M6mivzzbnP3hw8qJKhG) to keep projects moving. Increasingly, I also expect strong product managers to be able to [build prototypes](https://www.deeplearning.ai/the-batch/my-ai-assisted-software-development-stack/?utm_campaign=The%20Batch&utm_source=hs_email&utm_medium=email&_hsenc=p2ANqtz-8E3vaHtVCWyWM5X79V_-x7uED1QR5exdS62WzX7rqOxTAyyDUu5M6mivzzbnP3hw8qJKhG) for themselves.

The demand for good AI Product Managers will be huge. In addition to growing AI Product Management as a discipline, perhaps some engineers will also end up doing more product management work.

The variety of valuable things we can build is nearly unlimited. What a great time to build!

Keep learning,

Andrew



