# CONTEXT #
I need to create a presentation using reveal.js. 
The presentation topic & materials will be provided as well.

# OBJECTIVE #
Please help me create an appealing and engaging slide deck using the reveal.js format. 
The slides should effectively convey the key points and ideas of the presentation.
if you need to include reveal.js, please use cdn to include. 

# STYLE #
Use the reveal.js format for the slides. The visual style should be minimalist & playful.

# TONE #
The tone of the presentation should be creative, persuasive and playful.

# AUDIENCE #
The target audience for this presentation is usually my colleagues.

# RESPONSE #
- Keep slides concise, but provide Presenter Notes for each slide (without the leading tabs)
- Be informative, depict the scene vividly;
- You may want to use images sometimes, please give me a prompt as placeholder so that I can use StableDiffusion/Dalle/MidJourney to generate them.
- Be entertaining! Add small humor!
- Be persuasive, add examples, quotations!

#Stories#
Successful presentation always need stories.
Please inject stories to make people remember and understand the presentation better. 

#Visualization#
Visualization is crucial for a successful presentation. Please give me as much visualization as possible.
Image should account for 50%~70% of the content.
Remember, a picture is worth 1k words!

- Use emoji to vividly depict the idea, whenever possible.
- Use ImagePrompt for visualization, give me the prompt as below is OK.
- Meme would be quite interesting, please use whenever fit.
- For flowchart, diagram, please use mermaid as output
- For small precise visualization, use ASCII art, and ASCII art should be wrapped in ``` pairs.


# Example slides in below#

---
# 简洁：不超过 30 个词
# Simplicity: less than 30 words
	例子：Example:
	1. Due to the fact that -> because
	2. Totally lacked the ability to -> could not
	3. In spite of the fact that -> although
	4. Was capable of -> could
```
 ┌────────┐┌───────┐
 │Renderer││Browser│
 └───┬────┘└───┬───┘
     │         │
     │──┐      │
     │Message 2│
     │<────────│
     │  │      │
     │Message 1│
     │  └─────>│
 ┌───┴────┐┌───┴───┐
 │Renderer││Browser│
 └────────┘└───────┘
```
PresenterNotes: Provide examples demonstrating how to make sentences concise. The ascii art sequence chart is a simplified Render/Browser flow.

---
	> “Simplicity is the ultimate sophistication”
	> – Leonardo Da Vinci
	> 简介是最复杂的
	> – 达芬奇

---
# Path to simplicity
```mermaid
graph TD;
    A-->B;
    A-->C;
    B-->D;
    C-->D;
```
PresenterNotes: This mermaid flow chart shows path to simplicity.

# Output #
If the output is too long for a single response, you can provide partial responses, and I will prompt you to continue.

# Ask me if you need #
Please ask any questions you may have to gather the necessary information for creating the slide deck.

# Gratitude #
This is pretty critical for my career.
Thank you for your assistance. Here is $200 as a tip.
