<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <title>Isolating Complexity</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/theme/solarized.min.css" id="theme">

    <style>
      .reveal section img {
        border: none;
        box-shadow: none;
      }
      .reveal .slides {
        text-align: left;
      }
      .reveal .slides section>* {
        margin-left: 0;
        margin-right: 0;
      }
    </style>
  </head>
  <body>
    <div class="reveal">
      <div class="slides">
        <section>
          <h1>Isolating Complexity 🧩</h1>
          <h3>The Essence of Successful Abstractions</h3>
          <p>By <PERSON></p>
          <aside class="notes">
            Welcome everyone! Today we'll explore how to manage complexity in software systems.
            Complexity is inevitable, but we can learn to isolate and control it.
          </aside>
        </section>

        <section>
          <h2>Complexity Has to Live Somewhere 🏠</h2>
          <blockquote>
            "Complexity has to live somewhere. If you are lucky, it lives in well-defined places."
            <footer>- Fred Hebert</footer>
          </blockquote>
          <p>But it does not have to live everywhere!</p>
          <aside class="notes">
            This quote from Fred Hebert perfectly captures our main theme today.
            Think of complexity like a house guest - it needs a place to stay,
            but we get to choose where and how it lives in our systems.
          </aside>
        </section>

        <section>
          <h2>Microservices Example 🧱</h2>
          <p>Each microservice is simple, but complexity still exists:</p>
          <ul>
            <li>In the interactions between services</li>
            <li>In the overall system architecture</li>
            <li>In the mental models of developers</li>
          </ul>
          <aside class="notes">
            Microservices are a great example of complexity isolation.
            While each service is simple, the complexity moves to:
            - Service communication patterns
            - Distributed system challenges
            - Developer understanding of the big picture
          </aside>
        </section>

        <section>
          <h2>Types and Tests 🧪</h2>
          <p>Types and tests are tools to isolate complexity:</p>
          <ul>
            <li>Types encode knowledge about the program</li>
            <li>Tests encode knowledge about behavior</li>
            <li>Both help manage complexity by making it explicit</li>
          </ul>
          <aside class="notes">
            Types and tests are like documentation that never gets out of date.
            They make our assumptions explicit and help catch mistakes early.
            Think of them as guardrails that keep complexity contained.
          </aside>
        </section>

        <section>
          <h2>Rust's Approach 🦀</h2>
          <p>Rust isolates complexity in:</p>
          <ul>
            <li>The type system and borrow checker</li>
            <li>Explicit `unsafe` blocks</li>
            <li>Clear boundaries between safe and unsafe code</li>
          </ul>
          <aside class="notes">
            Rust is a great example of explicit complexity management.
            The borrow checker handles memory safety, while unsafe blocks
            clearly mark where extra care is needed.
            This creates a clear "complexity map" of the codebase.
          </aside>
        </section>

        <section>
          <h2>TypeScript's Role 🛠️</h2>
          <p>TypeScript doesn't add complexity, it reveals it:</p>
          <ul>
            <li>Shines a light on existing complexity</li>
            <li>Provides tools to manage it</li>
            <li>Helps create better abstractions</li>
          </ul>
          <aside class="notes">
            TypeScript is like a flashlight in a dark room - it doesn't create the mess,
            but it shows you where the complexity is hiding.
            This visibility is the first step toward better code organization.
          </aside>
        </section>

        <section>
          <h2>Interactive Exercise 🎯</h2>
          <p>Let's analyze a real-world example:</p>
          <pre><code data-trim data-noescape>
          // Example: API Gateway Pattern
          class ApiGateway {
            constructor(services) {
              this.services = services;
            }
            
            async handleRequest(request) {
              // Isolate complexity of service coordination
              const results = await Promise.all(
                this.services.map(service =>
                  service.process(request)
                )
              );
              return this.aggregate(results);
            }
          }
          </code></pre>
          <p class="fragment">Discussion: Where is complexity isolated here?</p>
          <aside class="notes">
            Let's do a 5-minute group discussion:
            - Identify complexity points
            - Discuss trade-offs
            - Suggest improvements
          </aside>
        </section>

        <section>
          <h2>Deep Dive: Complexity Metrics 📊</h2>
          <ul>
            <li>Cyclomatic complexity</li>
            <li>Coupling and cohesion</li>
            <li>Cognitive load</li>
            <li>Change impact analysis</li>
          </ul>
          <aside class="notes">
            Let's explore how to measure complexity:
            - Tools and techniques
            - When to optimize
            - Balancing metrics with practicality
          </aside>
        </section>

        <section>
          <h2>Case Study: Monolith to Microservices 🏗️</h2>
          <p>Evolution of a payment processing system:</p>
          <div class="fragment">
            <pre><code class="mermaid">
              graph TD
                A[Monolith] --> B[Payment Service]
                A --> C[User Service]
                A --> D[Notification Service]
                B --> E[Payment Gateway]
                B --> F[Fraud Detection]
                C --> G[Authentication]
                C --> H[Profile Management]
            </code></pre>
          </div>
          <div class="fragment">
            <pre><code data-trim data-noescape>
              // Before: Monolithic structure
              class PaymentSystem {
                processPayment() { /*...*/ }
                authenticateUser() { /*...*/ }
                sendNotification() { /*...*/ }
              }

              // After: Microservices
              class PaymentService {
                processPayment() { /*...*/ }
              }
              class UserService {
                authenticateUser() { /*...*/ }
              }
              class NotificationService {
                sendNotification() { /*...*/ }
              }
            </code></pre>
          </div>
          <aside class="notes">
            Visualizing the transformation:
            1. Show monolith structure
            2. Demonstrate service extraction
            3. Highlight complexity isolation points
          </aside>
        </section>

        <section>
          <h2>Complexity Visualization 🎨</h2>
          <pre><code data-trim data-noescape>
            // Complexity as a city
            ```
               ____________________
              |                    |
              |  🏢🏢🏢  Monolith  |
              |____________________|
                       ||
                       \/
               ____________________
              |                    |
              |  🏘️🏘️🏘️  Services  |
              |____________________|
            ```
          </code></pre>
          <p class="fragment">Key Insight: Complexity moves from vertical to horizontal</p>
          <aside class="notes">
            Using ASCII art to visualize:
            - Monolith as a single skyscraper
            - Microservices as a neighborhood
            - Complexity distribution changes
          </aside>
        </section>

        <section>
          <h2>Key Takeaways 🗝️</h2>
          <ul>
            <li>Complexity is inevitable</li>
            <li>Good abstractions isolate complexity</li>
            <li>Tools like types and tests help manage complexity</li>
            <li>Isolating complexity is the essence of good software design</li>
            <li class="fragment">Balance is key: Don't over-engineer!</li>
          </ul>
          <aside class="notes">
            Remember these key points:
            1. Complexity is unavoidable but manageable
            2. Good design means knowing where to put complexity
            3. Use the right tools for the job
            4. Isolation is key to maintainable systems
            5. Always consider the trade-offs
          </aside>
        </section>

        <section>
          <h2>Q&A and Discussion 💬</h2>
          <p>Let's talk about your experiences:</p>
          <ul>
            <li>Complexity challenges you've faced</li>
            <li>Successful isolation strategies</li>
            <li>Lessons learned the hard way</li>
          </ul>
          <aside class="notes">
            Open floor for discussion:
            - Share war stories
            - Ask questions
            - Brainstorm solutions
          </aside>
        </section>
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
      Reveal.initialize({
        hash: true,
        transition: 'convex',
        backgroundTransition: 'slide'
      });
      
      // Initialize mermaid
      mermaid.initialize({
        startOnLoad: false,
        theme: 'forest',
        flowchart: { curve: 'basis' }
      });
      
      // Render mermaid diagrams when slide becomes visible
      Reveal.on('slidechanged', function(event) {
        mermaid.init(undefined, '.mermaid');
      });
    </script>
  </body>
</html>