# CNODI

```mermaid
graph TD
    oh[OrderHub]
    isell[iSell] 
    isom[ISOM]
    mhs[MHS Store Tool]
    astro[Astro]
    centiro[Centiro]
    isomdb[ISOM DB]
    oh --> |SAPI: order creation| isell
    
    isell -->|Orchestration flow| isom
    isom --> |CWIS| astro
    isom --> |CWIS| mhs 
    isom --> |Create da, dispatch Wo| centiro

    iselle[iSell Events]
    isell -->|DB changes| iselle
    iselle -.-> oh

    isome[ISOM Events]
    isom --> isome
    isome --> oh
    isome --> pythia
    pytina -.-> oh

    ipos -.-> oh
    ipos --> ucloudIkeaIt
    ucloudIkeaIt --> family
    family --> oh

    tmall --> baozun
    baozun --> |tmall order info| family
    baozun -->|create order| oh
    oh -->|status update| baozun
    
    isom --> solace[Solace]
    %% solace --> |events| oh

    isom --> isomdb
    atp[Available To Promise]

    isomdb --> atp
    gis[Global Inventory System]
    mhs --> gis

    ipos[IPOS: IKEA Point Of Sale]
    click ipos "https://confluence.build.ingka.ikea.com/display/IPOSCCHPIP"

    lip[Local Integration Point]
    ipos-->lip
    lip --> mhs

    tsp[Transport Service Provider]
    centiro --> |dispatch| tsp
    tsp --> |status update| centiro
    centiro --> oh

    isom --> |dispatch| centiro
    
```
