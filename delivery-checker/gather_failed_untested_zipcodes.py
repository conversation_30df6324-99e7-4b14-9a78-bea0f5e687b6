#!/usr/bin/env python3
"""
G<PERSON> failed and untested zipcodes for parcel and truck deliveries
to prepare for retesting
"""

import pandas as pd
import subprocess
import os
import glob
import re
from datetime import datetime

def get_tested_zipcodes_from_logs():
    """Extract all tested zipcodes from log files, categorized by item type and result"""
    parcel_successful = set()
    parcel_failed = set()
    truck_successful = set()
    truck_failed = set()
    
    log_files = glob.glob("results/timewindows*/timewindows_test_*.log")
    
    print("📊 EXTRACTING TESTED ZIPCODES FROM LOG FILES:")
    
    for log_file in log_files:
        print(f"  📁 Processing {os.path.basename(log_file)}")
        
        # Get item ID
        item_id = None
        try:
            result = subprocess.run(['grep', 'Item ID:', log_file], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout:
                match = re.search(r'Item ID:\s*(\d+)', result.stdout)
                if match:
                    item_id = match.group(1)
        except:
            continue
        
        if not item_id:
            continue
        
        # Extract successful zipcodes
        try:
            success_cmd = f"grep 'Row.*: .* - SUCCESS' {log_file} | sed 's/.*Row [0-9]*: \\([0-9]*\\) - SUCCESS.*/\\1/'"
            success_result = subprocess.run(success_cmd, shell=True, 
                                          capture_output=True, text=True, timeout=30)
            if success_result.returncode == 0:
                successful_zipcodes = set(success_result.stdout.strip().split('\n'))
                successful_zipcodes.discard('')  # Remove empty strings
                
                if item_id == '10534224':  # Parcel
                    parcel_successful.update(successful_zipcodes)
                elif item_id == '70570836':  # Truck
                    truck_successful.update(successful_zipcodes)
        except Exception as e:
            print(f"    ⚠️  Error extracting successful zipcodes: {e}")
        
        # Extract failed zipcodes
        try:
            failed_cmd = f"grep 'Row.*: .* - FAILED' {log_file} | sed 's/.*Row [0-9]*: \\([0-9]*\\) - FAILED.*/\\1/'"
            failed_result = subprocess.run(failed_cmd, shell=True, 
                                         capture_output=True, text=True, timeout=30)
            if failed_result.returncode == 0:
                failed_zipcodes = set(failed_result.stdout.strip().split('\n'))
                failed_zipcodes.discard('')  # Remove empty strings
                
                if item_id == '10534224':  # Parcel
                    parcel_failed.update(failed_zipcodes)
                elif item_id == '70570836':  # Truck
                    truck_failed.update(failed_zipcodes)
        except Exception as e:
            print(f"    ⚠️  Error extracting failed zipcodes: {e}")
    
    return {
        'parcel_successful': parcel_successful,
        'parcel_failed': parcel_failed,
        'truck_successful': truck_successful,
        'truck_failed': truck_failed
    }

def load_masterfile_zipcodes():
    """Load all zipcodes from the masterfile"""
    masterfile_path = "masterfile-0805.xlsx"
    if not os.path.exists(masterfile_path):
        print(f"❌ Masterfile not found: {masterfile_path}")
        return set()
    
    try:
        df = pd.read_excel(masterfile_path)
        print(f"📋 Masterfile has {len(df):,} total rows")
        all_zipcodes = set(str(zipcode) for zipcode in df['ZipCode'].dropna())
        print(f"📋 Loaded {len(all_zipcodes):,} unique zipcodes from masterfile")
        print(f"📋 Total zipcode entries (including duplicates): {len(df['ZipCode'].dropna()):,}")
        return all_zipcodes
    except Exception as e:
        print(f"❌ Error loading masterfile: {e}")
        return set()

def main():
    """Main function"""
    print("🔍 GATHERING FAILED AND UNTESTED ZIPCODES")
    print("=" * 60)
    
    # Load all zipcodes from masterfile
    all_zipcodes = load_masterfile_zipcodes()
    if not all_zipcodes:
        return
    
    # Get tested zipcodes from logs
    tested_results = get_tested_zipcodes_from_logs()
    
    print(f"\n📊 TESTING RESULTS SUMMARY:")
    print(f"  📦 Parcel successful: {len(tested_results['parcel_successful']):,}")
    print(f"  📦 Parcel failed: {len(tested_results['parcel_failed']):,}")
    print(f"  🚛 Truck successful: {len(tested_results['truck_successful']):,}")
    print(f"  🚛 Truck failed: {len(tested_results['truck_failed']):,}")
    
    # Calculate untested zipcodes
    parcel_tested = tested_results['parcel_successful'] | tested_results['parcel_failed']
    truck_tested = tested_results['truck_successful'] | tested_results['truck_failed']
    
    parcel_untested = all_zipcodes - parcel_tested
    truck_untested = all_zipcodes - truck_tested
    
    print(f"\n📊 COVERAGE ANALYSIS:")
    print(f"  📦 Parcel tested: {len(parcel_tested):,} ({len(parcel_tested)/len(all_zipcodes)*100:.1f}%)")
    print(f"  📦 Parcel untested: {len(parcel_untested):,} ({len(parcel_untested)/len(all_zipcodes)*100:.1f}%)")
    print(f"  🚛 Truck tested: {len(truck_tested):,} ({len(truck_tested)/len(all_zipcodes)*100:.1f}%)")
    print(f"  🚛 Truck untested: {len(truck_untested):,} ({len(truck_untested)/len(all_zipcodes)*100:.1f}%)")
    
    # Create retest lists
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Parcel retest list (failed + untested)
    parcel_retest = tested_results['parcel_failed'] | parcel_untested
    if parcel_retest:
        parcel_retest_sorted = sorted([int(zc) for zc in parcel_retest if zc.isdigit()])
        parcel_df = pd.DataFrame({'zipcode': parcel_retest_sorted})
        parcel_file = f"parcel_retest_zipcodes_{timestamp}.xlsx"
        parcel_df.to_excel(parcel_file, index=False)
        print(f"\n📦 PARCEL RETEST FILE CREATED:")
        print(f"  📄 File: {parcel_file}")
        print(f"  📊 Total zipcodes to retest: {len(parcel_retest_sorted):,}")
        print(f"    ❌ Failed zipcodes: {len(tested_results['parcel_failed']):,}")
        print(f"    ❓ Untested zipcodes: {len(parcel_untested):,}")
    
    # Truck retest list (failed + untested)
    truck_retest = tested_results['truck_failed'] | truck_untested
    if truck_retest:
        truck_retest_sorted = sorted([int(zc) for zc in truck_retest if zc.isdigit()])
        truck_df = pd.DataFrame({'zipcode': truck_retest_sorted})
        truck_file = f"truck_retest_zipcodes_{timestamp}.xlsx"
        truck_df.to_excel(truck_file, index=False)
        print(f"\n🚛 TRUCK RETEST FILE CREATED:")
        print(f"  📄 File: {truck_file}")
        print(f"  📊 Total zipcodes to retest: {len(truck_retest_sorted):,}")
        print(f"    ❌ Failed zipcodes: {len(tested_results['truck_failed']):,}")
        print(f"    ❓ Untested zipcodes: {len(truck_untested):,}")
    
    # Create separate files for failed only (for priority retesting)
    if tested_results['parcel_failed']:
        parcel_failed_sorted = sorted([int(zc) for zc in tested_results['parcel_failed'] if zc.isdigit()])
        parcel_failed_df = pd.DataFrame({'zipcode': parcel_failed_sorted})
        parcel_failed_file = f"parcel_failed_only_{timestamp}.xlsx"
        parcel_failed_df.to_excel(parcel_failed_file, index=False)
        print(f"\n📦 PARCEL FAILED-ONLY FILE:")
        print(f"  📄 File: {parcel_failed_file}")
        print(f"  📊 Failed zipcodes: {len(parcel_failed_sorted):,}")
    
    if tested_results['truck_failed']:
        truck_failed_sorted = sorted([int(zc) for zc in tested_results['truck_failed'] if zc.isdigit()])
        truck_failed_df = pd.DataFrame({'zipcode': truck_failed_sorted})
        truck_failed_file = f"truck_failed_only_{timestamp}.xlsx"
        truck_failed_df.to_excel(truck_failed_file, index=False)
        print(f"\n🚛 TRUCK FAILED-ONLY FILE:")
        print(f"  📄 File: {truck_failed_file}")
        print(f"  📊 Failed zipcodes: {len(truck_failed_sorted):,}")
    
    # Create untested-only files
    if parcel_untested:
        parcel_untested_sorted = sorted([int(zc) for zc in parcel_untested if zc.isdigit()])
        parcel_untested_df = pd.DataFrame({'zipcode': parcel_untested_sorted})
        parcel_untested_file = f"parcel_untested_only_{timestamp}.xlsx"
        parcel_untested_df.to_excel(parcel_untested_file, index=False)
        print(f"\n📦 PARCEL UNTESTED-ONLY FILE:")
        print(f"  📄 File: {parcel_untested_file}")
        print(f"  📊 Untested zipcodes: {len(parcel_untested_sorted):,}")
    
    if truck_untested:
        truck_untested_sorted = sorted([int(zc) for zc in truck_untested if zc.isdigit()])
        truck_untested_df = pd.DataFrame({'zipcode': truck_untested_sorted})
        truck_untested_file = f"truck_untested_only_{timestamp}.xlsx"
        truck_untested_df.to_excel(truck_untested_file, index=False)
        print(f"\n🚛 TRUCK UNTESTED-ONLY FILE:")
        print(f"  📄 File: {truck_untested_file}")
        print(f"  📊 Untested zipcodes: {len(truck_untested_sorted):,}")
    
    # Summary for next steps
    print(f"\n🎯 RETEST STRATEGY RECOMMENDATIONS:")
    print(f"  1️⃣  Priority: Test failed zipcodes first (higher chance of issues)")
    print(f"  2️⃣  Coverage: Test untested zipcodes for complete coverage")
    print(f"  3️⃣  Focus: Truck has very few failures, parcel needs more attention")
    
    print(f"\n📋 FILES CREATED FOR RETESTING:")
    if parcel_retest:
        print(f"  📦 {parcel_file} - Complete parcel retest list")
    if truck_retest:
        print(f"  🚛 {truck_file} - Complete truck retest list")
    
    print(f"\n🏁 RETEST PREPARATION COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
