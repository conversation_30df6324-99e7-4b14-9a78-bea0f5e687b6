#!/usr/bin/env python3
"""
Test time-windows API using delivery arrangement data
"""

from delivery_test import DeliveryTester
import json
import requests
from datetime import datetime, timezone

def test_time_windows_api(zipcode, item_id):
    """Test time-windows API for a specific zipcode and item"""
    
    print(f'🔍 测试time-windows接口')
    print(f'商品ID: {item_id}')
    print(f'邮编: {zipcode}')
    print('=' * 60)
    
    # Step 1: Get delivery arrangement first
    tester = DeliveryTester()
    
    try:
        token = tester.get_access_token()
        print('✅ 访问令牌获取成功')
    except Exception as e:
        print(f'❌ 获取访问令牌失败: {e}')
        return False
    
    # Step 2: Call delivery-arrangement API first
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    delivery_payload = {
        "checkCapacity": True,
        "checkInventory": True,
        "businessUnit": {
            "type": "STO",
            "code": "1228"
        },
        "channelReferences": {
            "sellingChannelName": "TimeWindowsTester"
        },
        "shipToAddress": {
            "country": "CN",
            "zipCode": str(zipcode)
        },
        "itemLines": {
            "itemLine": [
                {
                    "itemType": "ART",
                    "itemNo": str(item_id),
                    "id": "1",
                    "requiredQty": 1
                }
            ]
        },
        "serviceTypes": {
            "serviceType": [
                {
                    "id": "HOME_DELIVERY"
                }
            ]
        },
        "useLeadTimeOrchestration": True,
        "checkNoStock": True
    }
    
    print(f'🚚 第一步: 调用delivery-arrangement接口...')
    
    try:
        delivery_response = requests.post(
            tester.api_url, 
            headers=headers, 
            json=delivery_payload, 
            timeout=30, 
            verify=False
        )
        
        print(f'delivery-arrangement状态码: {delivery_response.status_code}')
        
        if delivery_response.status_code != 200:
            print(f'❌ delivery-arrangement调用失败: {delivery_response.text}')
            return False
            
        delivery_result = delivery_response.json()
        print(f'✅ delivery-arrangement调用成功')

        # Extract delivery arrangement ID
        delivery_arrangement_id = delivery_result.get('deliveryArrangementsId')
        if not delivery_arrangement_id:
            print(f'❌ 未找到deliveryArrangementsId')
            return False

        print(f'📋 Delivery Arrangement ID: {delivery_arrangement_id}')

        # Extract solution information
        service_types = delivery_result.get('serviceTypes', {})
        service_type_list = service_types.get('serviceType', [])

        if not service_type_list:
            print(f'❌ 未找到服务类型')
            return False

        # Get first service type
        first_service = service_type_list[0] if isinstance(service_type_list, list) else service_type_list

        # Use possibleSolutions instead of solutions
        possible_solutions = first_service.get('possibleSolutions', {})
        solution_list = possible_solutions.get('possibleSolution', [])

        if not solution_list:
            print(f'❌ 未找到解决方案')
            return False
            
        # Get first solution
        first_solution = solution_list[0] if isinstance(solution_list, list) else solution_list
        solution_id = first_solution.get('id')  # Use 'id' instead of 'solutionId'

        if not solution_id:
            print(f'❌ 未找到solutionId')
            return False

        print(f'📋 Solution ID: {solution_id}')
        
        # Extract delivery lines
        delivery_lines = first_solution.get('deliveryLines', {})
        delivery_line_list = delivery_lines.get('deliveryLine', [])
        
        if not delivery_line_list:
            print(f'❌ 未找到配送线路')
            return False
            
        # Get first delivery line
        first_delivery_line = delivery_line_list[0] if isinstance(delivery_line_list, list) else delivery_line_list
        delivery_id = first_delivery_line.get('deliveryId')
        
        if not delivery_id:
            print(f'❌ 未找到deliveryId')
            return False
            
        print(f'📋 Delivery ID: {delivery_id}')
        
    except Exception as e:
        print(f'❌ delivery-arrangement调用异常: {e}')
        return False
    
    # Step 3: Call time-windows API
    print(f'\n⏰ 第二步: 调用time-windows接口...')
    
    time_windows_url = "https://private-api.ingka.prodcn.ikea.com/cfb/customer-promise/cn/time-windows"
    
    # Build time-windows payload based on the log example
    current_time = datetime.now(timezone.utc).isoformat()
    
    time_windows_payload = {
        "deliveryArrangementsId": delivery_arrangement_id,
        "returnMultipleSrvcSlots": False,
        "selectedSolution": {
            "solutionId": solution_id,
            "deliveryLines": {
                "deliveryLine": [
                    {
                        "deliveryId": delivery_id,
                        "serviceSearchWindow": 15,
                        "reqstartDate": current_time,
                        "isExceptionalQty": False,
                        "isExceptionalVolume": False,
                        "returnMultipleSrvcSlots": False
                    }
                ]
            }
        }
    }
    
    print(f'time-windows请求数据:')
    print(json.dumps(time_windows_payload, indent=2))
    
    try:
        time_windows_response = requests.post(
            time_windows_url,
            headers=headers,
            json=time_windows_payload,
            timeout=30,
            verify=False
        )
        
        print(f'\n📊 time-windows API响应结果:')
        print(f'状态码: {time_windows_response.status_code}')
        print(f'响应时间: {time_windows_response.elapsed.total_seconds():.3f}秒')
        
        if time_windows_response.status_code == 200:
            time_windows_result = time_windows_response.json()
            print(f'\n✅ time-windows请求成功!')
            print(f'完整响应:')
            print(json.dumps(time_windows_result, indent=2, ensure_ascii=False))
            return True
        else:
            print(f'\n❌ time-windows请求失败!')
            print(f'错误信息: {time_windows_response.text}')
            return False
            
    except Exception as e:
        print(f'\n❌ time-windows请求异常: {e}')
        return False

def main():
    """主函数"""
    # 测试参数 - 使用轻型商品60561007 (包裹运输)
    zipcode = '226553'
    item_id = '60561007'  # 轻型商品，0.076KG，包裹运输

    print(f'🧪 测试time-windows接口')
    print(f'商品: {item_id} (轻型商品，包裹运输), 邮编: {zipcode}\n')
    
    success = test_time_windows_api(zipcode, item_id)
    
    if success:
        print(f'\n🎉 time-windows接口测试完成!')
    else:
        print(f'\n❌ time-windows接口测试失败!')

if __name__ == '__main__':
    main()
