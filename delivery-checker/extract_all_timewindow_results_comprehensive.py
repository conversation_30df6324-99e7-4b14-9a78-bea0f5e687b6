#!/usr/bin/env python3
"""
Comprehensive extraction of ALL valid time window execution history from API response JSON files
and create comprehensive Excel files - one for parcel, one for truck.
This version also checks log files for item IDs when progress files are missing.
"""

import json
import pandas as pd
import os
import glob
import re
from datetime import datetime
import sys

# Item ID mappings
PARCEL_ITEM_ID = "10534224"
TRUCK_ITEM_ID = "70570836"

def get_item_id_from_progress(progress_file):
    """Extract item ID from progress file"""
    try:
        if os.path.exists(progress_file):
            with open(progress_file, 'r') as f:
                progress_data = json.load(f)
                return str(progress_data.get('item_id', ''))
    except Exception as e:
        print(f"    Error reading progress file {progress_file}: {e}")
    return None

def get_item_id_from_log(log_file):
    """Extract item ID from log file"""
    try:
        if os.path.exists(log_file):
            with open(log_file, 'r') as f:
                for line in f:
                    # Look for "Item ID: XXXXXXXX" pattern
                    match = re.search(r'Item ID:\s*(\d+)', line)
                    if match:
                        return match.group(1)
    except Exception as e:
        print(f"    Error reading log file {log_file}: {e}")
    return None

def extract_api_responses(json_file):
    """Extract all API responses from a JSON file"""
    responses = []
    try:
        with open(json_file, 'r') as f:
            # Try to load as JSON array first
            try:
                data = json.load(f)
                if isinstance(data, list):
                    responses = data
                else:
                    responses = [data]
            except json.JSONDecodeError:
                # If that fails, try line-by-line JSON
                f.seek(0)
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            response_data = json.loads(line)
                            responses.append(response_data)
                        except json.JSONDecodeError:
                            continue
    except Exception as e:
        print(f"    Error reading {json_file}: {e}")
    
    return responses

def process_response_data(response_data):
    """Process a single API response and extract relevant fields"""
    result = {}
    
    # Basic info
    result['zipcode'] = response_data.get('zipcode', '')
    result['row_index'] = response_data.get('row_index', '')
    result['timestamp'] = response_data.get('timestamp', '')
    
    # Delivery API response
    delivery_response = response_data.get('delivery_response', {})
    if delivery_response:
        result['delivery_success'] = delivery_response.get('success', False)
        result['delivery_arrangement_id'] = delivery_response.get('data', {}).get('arrangementId', '')
        result['delivery_solution_id'] = delivery_response.get('data', {}).get('solutionId', '')
        result['delivery_id'] = delivery_response.get('data', {}).get('deliveryId', '')
        result['delivery_error'] = delivery_response.get('error', '')
        result['delivery_response_time'] = delivery_response.get('response_time', '')
    
    # Time windows API response
    timewindows_response = response_data.get('timewindows_response', {})
    if timewindows_response:
        result['timewindows_success'] = timewindows_response.get('success', False)
        result['timewindows_error'] = timewindows_response.get('error', '')
        result['timewindows_response_time'] = timewindows_response.get('response_time', '')
        
        # Extract time window data
        tw_data = timewindows_response.get('data', {})
        if tw_data:
            result['has_time_slots'] = bool(tw_data.get('timeWindows'))
            
            # Available dates
            time_windows = tw_data.get('timeWindows', [])
            if time_windows:
                dates = []
                all_time_windows = []
                tsp_names = []
                tsp_ids = []
                nodes = []
                capacity_available = []
                payment_cutoff_times = []
                resource_pool_ids = []
                time_window_ids = []
                
                for tw in time_windows:
                    date = tw.get('date', '')
                    if date and date not in dates:
                        dates.append(date)
                    
                    # Time slots for this date
                    time_slots = tw.get('timeSlots', [])
                    for slot in time_slots:
                        all_time_windows.append(f"{date} {slot.get('startTime', '')}-{slot.get('endTime', '')}")
                        tsp_names.append(slot.get('tspName', ''))
                        tsp_ids.append(str(slot.get('tspId', '')))
                        nodes.append(slot.get('node', ''))
                        capacity_available.append(str(slot.get('capacityAvailable', '')))
                        payment_cutoff_times.append(slot.get('paymentCutoffTime', ''))
                        resource_pool_ids.append(str(slot.get('resourcePoolId', '')))
                        time_window_ids.append(str(slot.get('timeWindowId', '')))
                
                result['available_dates'] = '; '.join(dates)
                result['time_windows'] = '; '.join(all_time_windows)
                result['tsp_names'] = '; '.join(set(filter(None, tsp_names)))
                result['tsp_ids'] = '; '.join(set(filter(None, tsp_ids)))
                result['nodes'] = '; '.join(set(filter(None, nodes)))
                result['capacity_available'] = '; '.join(set(filter(None, capacity_available)))
                result['payment_cutoff_times'] = '; '.join(set(filter(None, payment_cutoff_times)))
                result['resource_pool_ids'] = '; '.join(set(filter(None, resource_pool_ids)))
                result['time_window_ids'] = '; '.join(set(filter(None, time_window_ids)))
    
    return result

def main():
    """Main function"""
    print("🔍 COMPREHENSIVE EXTRACTION OF ALL TIME WINDOW EXECUTION HISTORY")
    print("=" * 70)
    
    # Find all timewindows test directories
    timewindows_dirs = glob.glob("results/timewindows_test_*")
    timewindows_dirs.sort()
    
    parcel_responses = []
    truck_responses = []
    
    print(f"Found {len(timewindows_dirs)} time window test directories")
    
    for test_dir in timewindows_dirs:
        print(f"\n📁 Processing: {test_dir}")
        
        # Extract timestamp from directory name
        timestamp = os.path.basename(test_dir).replace('timewindows_test_', '')
        
        # Try to get item ID from progress file first
        progress_file = os.path.join(test_dir, f"progress_{timestamp}.json")
        item_id = get_item_id_from_progress(progress_file)
        
        # If no progress file or no item ID, try log file
        if not item_id:
            log_file = os.path.join(test_dir, f"timewindows_test_{timestamp}.log")
            item_id = get_item_id_from_log(log_file)
            if item_id:
                print(f"  📄 Item ID found in log file: {item_id}")
        else:
            print(f"  📄 Item ID found in progress file: {item_id}")
        
        if not item_id:
            print(f"  ⚠️  No item ID found, skipping")
            continue
        
        # Find API responses file
        api_responses_file = os.path.join(test_dir, f"api_responses_{timestamp}.json")
        
        if not os.path.exists(api_responses_file):
            print(f"  ⚠️  No API responses file found, skipping")
            continue
        
        print(f"  📄 API responses: {api_responses_file}")
        
        # Extract responses
        responses = extract_api_responses(api_responses_file)
        print(f"  ✅ Extracted {len(responses)} API responses")
        
        if len(responses) == 0:
            continue
        
        # Process responses
        processed_responses = []
        for response_data in responses:
            processed = process_response_data(response_data)
            processed['test_directory'] = test_dir
            processed['item_id'] = item_id
            processed_responses.append(processed)
        
        # Categorize by item type
        if item_id == PARCEL_ITEM_ID:
            parcel_responses.extend(processed_responses)
            print(f"  📦 Added {len(processed_responses)} parcel responses")
        elif item_id == TRUCK_ITEM_ID:
            truck_responses.extend(processed_responses)
            print(f"  🚛 Added {len(processed_responses)} truck responses")
        else:
            print(f"  ❓ Unknown item ID: {item_id}")
    
    print(f"\n📊 COMPREHENSIVE SUMMARY:")
    print(f"  📦 Total parcel responses: {len(parcel_responses)}")
    print(f"  🚛 Total truck responses: {len(truck_responses)}")
    
    # Create Excel files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if parcel_responses:
        parcel_df = pd.DataFrame(parcel_responses)
        # Sort by timestamp for better organization
        if 'timestamp' in parcel_df.columns:
            parcel_df = parcel_df.sort_values('timestamp')
        parcel_file = f"comprehensive_parcel_timewindows_{timestamp}.xlsx"
        parcel_df.to_excel(parcel_file, index=False)
        print(f"  ✅ Comprehensive Parcel Excel saved: {parcel_file}")
        print(f"     📊 Contains {len(parcel_df)} parcel time window tests")
    
    if truck_responses:
        truck_df = pd.DataFrame(truck_responses)
        # Sort by timestamp for better organization
        if 'timestamp' in truck_df.columns:
            truck_df = truck_df.sort_values('timestamp')
        truck_file = f"comprehensive_truck_timewindows_{timestamp}.xlsx"
        truck_df.to_excel(truck_file, index=False)
        print(f"  ✅ Comprehensive Truck Excel saved: {truck_file}")
        print(f"     📊 Contains {len(truck_df)} truck time window tests")
    
    print(f"\n🏁 COMPREHENSIVE EXTRACTION COMPLETE")
    print("=" * 70)

if __name__ == "__main__":
    main()
