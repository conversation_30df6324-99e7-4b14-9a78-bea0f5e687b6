#!/usr/bin/env python3
"""
Check testing speed from recent test results to estimate full test duration
"""

import json
from datetime import datetime
import os

def analyze_testing_speed():
    """Analyze testing speed from recent log files"""
    
    # Check the most recent parcel test results
    log_file = 'results/timewindows_test_20250805_232751/timewindows_test_20250805_232751.log'
    
    if not os.path.exists(log_file):
        print(f"❌ Log file not found: {log_file}")
        return
    
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        # Find start and end times
        start_time = None
        end_time = None
        
        for line in lines:
            if 'Starting time-windows batch test' in line:
                # Extract timestamp from log line
                timestamp_str = line.split(' - ')[0]
                start_time = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
            elif 'Time-windows batch test completed successfully' in line:
                timestamp_str = line.split(' - ')[0]
                end_time = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
        
        if start_time and end_time:
            duration = end_time - start_time
            total_seconds = duration.total_seconds()
            zipcodes_tested = 49
            
            print(f"📊 RECENT PARCEL TEST PERFORMANCE:")
            print(f"  ✅ Zipcodes tested: {zipcodes_tested}")
            print(f"  ⏱️  Duration: {total_seconds:.1f} seconds")
            print(f"  🚀 Speed: {zipcodes_tested/total_seconds:.2f} zipcodes/second")
            print(f"  🚀 Speed: {zipcodes_tested/(total_seconds/60):.1f} zipcodes/minute")
            
            # Estimate for full parcel test
            parcel_failed = 13668
            parcel_untested = 2298
            total_parcel_zipcodes = parcel_failed + parcel_untested
            
            base_speed = zipcodes_tested/total_seconds  # zipcodes per second
            
            print(f"\n📋 FULL PARCEL TEST ESTIMATES:")
            print(f"  📦 Parcel failed zipcodes: {parcel_failed:,}")
            print(f"  📦 Parcel untested zipcodes: {parcel_untested:,}")
            print(f"  📊 Total parcel zipcodes: {total_parcel_zipcodes:,}")
            
            # Estimates with different concurrency levels
            concurrency_levels = [10, 15, 20, 25, 30]
            
            print(f"\n⏱️  TIME ESTIMATES BY CONCURRENCY:")
            for concurrency in concurrency_levels:
                # Assume linear speedup up to a point, then diminishing returns
                if concurrency <= 10:
                    speedup_factor = concurrency / 10
                elif concurrency <= 20:
                    speedup_factor = 1 + (concurrency - 10) * 0.8 / 10
                else:
                    speedup_factor = 1.8 + (concurrency - 20) * 0.4 / 10
                
                estimated_speed = base_speed * speedup_factor
                estimated_seconds = total_parcel_zipcodes / estimated_speed
                estimated_minutes = estimated_seconds / 60
                estimated_hours = estimated_minutes / 60
                
                print(f"    Concurrency {concurrency:2d}: {estimated_hours:.1f} hours ({estimated_minutes:.0f} minutes)")
            
            # Recommended approach
            print(f"\n🎯 RECOMMENDED TESTING STRATEGY:")
            print(f"  1️⃣  Start with concurrency 20 (estimated ~{total_parcel_zipcodes/(base_speed*1.8)/3600:.1f} hours)")
            print(f"  2️⃣  Monitor progress and adjust concurrency if needed")
            print(f"  3️⃣  Test in batches to allow for monitoring and interruption")
            
            # Batch recommendations
            batch_sizes = [1000, 2000, 5000]
            print(f"\n📦 BATCH SIZE RECOMMENDATIONS:")
            for batch_size in batch_sizes:
                batch_time_minutes = batch_size / (base_speed * 1.8) / 60
                num_batches = (total_parcel_zipcodes + batch_size - 1) // batch_size
                print(f"    {batch_size:,} zipcodes/batch: ~{batch_time_minutes:.0f} min/batch, {num_batches} batches total")
            
        else:
            print("❌ Could not find start/end times in log")
            
    except Exception as e:
        print(f"❌ Error analyzing log: {e}")

def main():
    """Main function"""
    print("🔍 TESTING SPEED ANALYSIS")
    print("=" * 50)
    
    analyze_testing_speed()
    
    print("=" * 50)

if __name__ == "__main__":
    main()
