#!/usr/bin/env python3
"""
Analyze the comprehensive time window results Excel files
"""

import pandas as pd
import os
from datetime import datetime

def analyze_excel_file(excel_path, file_type):
    """Analyze a comprehensive Excel file"""
    if not os.path.exists(excel_path):
        print(f"❌ File not found: {excel_path}")
        return
    
    print(f"\n{'='*70}")
    print(f"📊 ANALYZING: {file_type.upper()} TIME WINDOW RESULTS")
    print(f"📄 File: {excel_path}")
    print(f"{'='*70}")
    
    try:
        # Load the Excel file
        df = pd.read_excel(excel_path)
        print(f"📋 Total records: {len(df):,}")
        
        # Basic statistics
        if 'delivery_success' in df.columns:
            delivery_success = df['delivery_success'].sum()
            delivery_total = df['delivery_success'].notna().sum()
            print(f"🚚 Delivery Success: {delivery_success:,}/{delivery_total:,} ({delivery_success/delivery_total*100:.1f}%)")
        
        if 'timewindows_success' in df.columns:
            tw_success = df['timewindows_success'].sum()
            tw_total = df['timewindows_success'].notna().sum()
            print(f"⏰ Time Windows Success: {tw_success:,}/{tw_total:,} ({tw_success/tw_total*100:.1f}%)")
        
        # Test directories breakdown
        if 'test_directory' in df.columns:
            print(f"\n📁 BREAKDOWN BY TEST DIRECTORY:")
            dir_counts = df['test_directory'].value_counts()
            for test_dir, count in dir_counts.items():
                print(f"  {test_dir}: {count:,} records")
        
        # Time range analysis
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            earliest = df['timestamp'].min()
            latest = df['timestamp'].max()
            print(f"\n⏰ TIME RANGE:")
            print(f"  Earliest: {earliest}")
            print(f"  Latest: {latest}")
            print(f"  Duration: {latest - earliest}")
        
        # Available dates analysis
        if 'available_dates' in df.columns:
            available_dates = df['available_dates'].dropna()
            if len(available_dates) > 0:
                print(f"\n📅 AVAILABLE DELIVERY DATES:")
                all_dates = []
                for dates_str in available_dates:
                    if dates_str and dates_str != '':
                        dates = dates_str.split('; ')
                        all_dates.extend(dates)
                
                unique_dates = sorted(set(all_dates))
                print(f"  Unique delivery dates found: {len(unique_dates)}")
                if unique_dates:
                    print(f"  Date range: {unique_dates[0]} to {unique_dates[-1]}")
                    print(f"  Sample dates: {', '.join(unique_dates[:5])}")
        
        # TSP analysis
        if 'tsp_names' in df.columns:
            tsp_names = df['tsp_names'].dropna()
            if len(tsp_names) > 0:
                print(f"\n🚛 TRANSPORT SERVICE PROVIDERS:")
                all_tsps = []
                for tsp_str in tsp_names:
                    if tsp_str and tsp_str != '':
                        tsps = tsp_str.split('; ')
                        all_tsps.extend(tsps)
                
                unique_tsps = sorted(set(all_tsps))
                print(f"  Unique TSPs found: {len(unique_tsps)}")
                if unique_tsps:
                    print(f"  Sample TSPs: {', '.join(unique_tsps[:3])}")
        
        # Success rate by zipcode (top 10)
        if 'zipcode' in df.columns and 'delivery_success' in df.columns:
            print(f"\n📍 TOP 10 ZIPCODES BY SUCCESS RATE:")
            zipcode_stats = df.groupby('zipcode').agg({
                'delivery_success': ['count', 'sum']
            }).round(2)
            zipcode_stats.columns = ['total_tests', 'successful_tests']
            zipcode_stats['success_rate'] = (zipcode_stats['successful_tests'] / zipcode_stats['total_tests'] * 100).round(1)
            zipcode_stats = zipcode_stats[zipcode_stats['total_tests'] >= 2]  # At least 2 tests
            top_zipcodes = zipcode_stats.sort_values('success_rate', ascending=False).head(10)
            
            for zipcode, row in top_zipcodes.iterrows():
                print(f"  {zipcode}: {row['success_rate']}% ({int(row['successful_tests'])}/{int(row['total_tests'])})")
        
        # Sample successful records
        if 'delivery_success' in df.columns:
            successful_records = df[df['delivery_success'] == True]
            if len(successful_records) > 0:
                print(f"\n✅ SAMPLE SUCCESSFUL RECORDS:")
                for i, (idx, row) in enumerate(successful_records.head(3).iterrows()):
                    zipcode = row.get('zipcode', 'N/A')
                    available_dates = row.get('available_dates', 'N/A')
                    tsp_names = row.get('tsp_names', 'N/A')
                    print(f"  {i+1}. Zipcode: {zipcode}")
                    print(f"     Available dates: {available_dates}")
                    print(f"     TSPs: {tsp_names}")
        
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")

def main():
    """Main function"""
    print("🔍 COMPREHENSIVE TIME WINDOW RESULTS ANALYSIS")
    print("=" * 70)
    
    # Find the latest comprehensive files
    import glob
    parcel_files = glob.glob("comprehensive_parcel_timewindows_*.xlsx")
    truck_files = glob.glob("comprehensive_truck_timewindows_*.xlsx")
    
    if parcel_files:
        latest_parcel = sorted(parcel_files)[-1]
        analyze_excel_file(latest_parcel, "parcel")
    
    if truck_files:
        latest_truck = sorted(truck_files)[-1]
        analyze_excel_file(latest_truck, "truck")
    
    print(f"\n{'='*70}")
    print("🏁 ANALYSIS COMPLETE")
    print(f"{'='*70}")

if __name__ == "__main__":
    main()
