#!/usr/bin/env python3
"""
Continue existing time-windows test from where it left off
"""

import os
import json
import argparse
import subprocess
import sys
from pathlib import Path

def find_latest_test_dir(pattern="timewindows_test_"):
    """Find the latest test directory"""
    results_dir = Path("results")
    if not results_dir.exists():
        print("❌ Results directory not found")
        return None
    
    test_dirs = [d for d in results_dir.iterdir() if d.is_dir() and d.name.startswith(pattern)]
    if not test_dirs:
        print(f"❌ No test directories found with pattern: {pattern}")
        return None
    
    # Sort by creation time, get latest
    latest_dir = max(test_dirs, key=lambda d: d.stat().st_mtime)
    return latest_dir

def get_progress_info(test_dir):
    """Get progress information from test directory"""
    progress_files = list(test_dir.glob("progress_*.json"))
    if not progress_files:
        print(f"❌ No progress file found in {test_dir}")
        return None
    
    progress_file = progress_files[0]
    try:
        with open(progress_file, 'r') as f:
            progress = json.load(f)
        return progress
    except Exception as e:
        print(f"❌ Error reading progress file: {e}")
        return None

def extract_item_id_from_log(test_dir):
    """Extract item ID from log file"""
    log_files = list(test_dir.glob("*.log"))
    if not log_files:
        return None
    
    log_file = log_files[0]
    try:
        with open(log_file, 'r') as f:
            for line in f:
                if "Item ID:" in line:
                    # Extract item ID from line like "Item ID: 70570836"
                    item_id = line.split("Item ID:")[-1].strip()
                    return item_id
    except Exception as e:
        print(f"⚠️ Could not extract item ID from log: {e}")
    
    return None

def main():
    parser = argparse.ArgumentParser(description='Continue existing time-windows test')
    parser.add_argument('--test-dir', help='Specific test directory to continue')
    parser.add_argument('--item-id', help='Override item ID')
    parser.add_argument('--batch-size', type=int, default=100, help='Batch size')
    parser.add_argument('--concurrency', type=int, help='Concurrency level')
    parser.add_argument('--list', action='store_true', help='List available test directories')
    
    args = parser.parse_args()
    
    # List available test directories
    if args.list:
        results_dir = Path("results")
        if results_dir.exists():
            test_dirs = [d for d in results_dir.iterdir() if d.is_dir() and "timewindows_test_" in d.name]
            print("📁 Available test directories:")
            for test_dir in sorted(test_dirs, key=lambda d: d.stat().st_mtime, reverse=True):
                progress_file = list(test_dir.glob("progress_*.json"))
                if progress_file:
                    try:
                        with open(progress_file[0], 'r') as f:
                            progress = json.load(f)
                        completed = progress.get('completed_rows', 0)
                        total = progress.get('total_rows', 0)
                        print(f"  {test_dir.name}: {completed}/{total} rows completed")
                    except:
                        print(f"  {test_dir.name}: (progress unknown)")
                else:
                    print(f"  {test_dir.name}: (no progress file)")
        return
    
    # Find test directory
    if args.test_dir:
        test_dir = Path("results") / args.test_dir
        if not test_dir.exists():
            print(f"❌ Test directory not found: {test_dir}")
            return
    else:
        test_dir = find_latest_test_dir()
        if not test_dir:
            return
    
    print(f"📁 Using test directory: {test_dir.name}")
    
    # Get progress information
    progress = get_progress_info(test_dir)
    if not progress:
        return
    
    completed_rows = progress.get('completed_rows', 0)
    total_rows = progress.get('total_rows', 30898)
    current_row = progress.get('current_row', completed_rows)
    completion_pct = progress.get('completion_percentage', 0)
    estimated_time = progress.get('estimated_remaining_time', 'Unknown')
    last_update = progress.get('last_update_time', 'Unknown')

    print(f"📊 Progress: {completed_rows}/{total_rows} rows completed")
    print(f"🎯 Item ID: {progress.get('item_id', item_id)}")
    print(f"🔄 Concurrency: {progress.get('concurrency', args.concurrency)}")
    print(f"🚀 Continuing from row {current_row}")
    print(f"📦 Remaining rows: {total_rows - completed_rows}")
    print(f"📈 Completion: {completion_pct:.1f}%")
    if estimated_time != 'Unknown':
        print(f"⏱️ Estimated remaining time: {estimated_time}")
    print(f"🕐 Last update: {last_update}")

    if completed_rows >= total_rows:
        print("✅ Test already completed!")
        return
    
    # Extract item ID
    item_id = args.item_id
    if not item_id:
        item_id = extract_item_id_from_log(test_dir)
    
    if not item_id:
        print("❌ Could not determine item ID. Please specify with --item-id")
        return
    
    print(f"🏷️ Item ID: {item_id}")
    
    # Determine concurrency
    concurrency = args.concurrency
    if not concurrency:
        # Default concurrency based on item type
        if item_id in ['70570836', '00588793']:  # Truck items
            concurrency = 10
        else:  # Parcel items
            concurrency = 15
    
    print(f"🔄 Concurrency: {concurrency}")
    
    # Calculate start row (continue from where we left off)
    start_row = completed_rows
    
    print(f"🚀 Continuing from row {start_row}")
    print(f"📦 Remaining rows: {total_rows - start_row}")
    
    # Build command
    cmd = [
        'python3', 'time_windows_test.py',
        '--item-id', item_id,
        '--start-row', str(start_row),
        '--batch-size', str(args.batch_size),
        '--concurrency', str(concurrency)
    ]
    
    print(f"🔧 Command: {' '.join(cmd)}")
    print("=" * 60)
    
    # Ask for confirmation
    response = input("Continue? (y/N): ")
    if response.lower() != 'y':
        print("❌ Cancelled")
        return
    
    # Execute command
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n⏹️ Stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed with exit code {e.returncode}")

if __name__ == '__main__':
    main()
