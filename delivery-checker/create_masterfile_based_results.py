#!/usr/bin/env python3
"""
Create comprehensive Excel files based on masterfile structure,
using all time window API responses as source of truth.
Store per item, zipcode: delivery arrangement ID, timewindow ID, and timewindow date.
"""

import json
import pandas as pd
import os
import glob
import re
from datetime import datetime
import sys

# Item ID mappings
PARCEL_ITEM_ID = "10534224"
TRUCK_ITEM_ID = "70570836"

def get_item_id_from_log(log_file):
    """Extract item ID from log file"""
    try:
        if os.path.exists(log_file):
            with open(log_file, 'r') as f:
                for line in f:
                    # Look for "Item ID: XXXXXXXX" pattern
                    match = re.search(r'Item ID:\s*(\d+)', line)
                    if match:
                        return match.group(1)
    except Exception as e:
        print(f"    Error reading log file {log_file}: {e}")
    return None

def get_item_id_from_progress(progress_file):
    """Extract item ID from progress file"""
    try:
        if os.path.exists(progress_file):
            with open(progress_file, 'r') as f:
                progress_data = json.load(f)
                return str(progress_data.get('item_id', ''))
    except Exception as e:
        print(f"    Error reading progress file {progress_file}: {e}")
    return None

def extract_api_responses(json_file):
    """Extract all API responses from a JSON file"""
    responses = []
    try:
        with open(json_file, 'r') as f:
            # Try to load as JSON array first
            try:
                data = json.load(f)
                if isinstance(data, list):
                    responses = data
                else:
                    responses = [data]
            except json.JSONDecodeError:
                # If that fails, try line-by-line JSON
                f.seek(0)
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            response_data = json.loads(line)
                            responses.append(response_data)
                        except json.JSONDecodeError:
                            continue
    except Exception as e:
        print(f"    Error reading {json_file}: {e}")
    
    return responses

def process_response_for_masterfile(response_data):
    """Process a single API response and extract key fields for masterfile"""
    result = {}
    
    # Basic info
    result['zipcode'] = response_data.get('zipcode', '')
    result['row_index'] = response_data.get('row_index', '')
    result['timestamp'] = response_data.get('timestamp', '')
    
    # Delivery API response - extract arrangement ID
    delivery_response = response_data.get('delivery_response', {})
    if delivery_response and delivery_response.get('success'):
        delivery_data = delivery_response.get('data', {})
        result['delivery_arrangement_id'] = delivery_data.get('arrangementId', '')
        result['delivery_solution_id'] = delivery_data.get('solutionId', '')
        result['delivery_id'] = delivery_data.get('deliveryId', '')
        result['delivery_success'] = True
    else:
        result['delivery_arrangement_id'] = ''
        result['delivery_solution_id'] = ''
        result['delivery_id'] = ''
        result['delivery_success'] = False
        result['delivery_error'] = delivery_response.get('error', '') if delivery_response else ''
    
    # Time windows API response - extract timewindow IDs and dates
    timewindows_response = response_data.get('timewindows_response', {})
    if timewindows_response and timewindows_response.get('success'):
        result['timewindows_success'] = True
        
        # Extract time window data
        tw_data = timewindows_response.get('data', {})
        if tw_data and tw_data.get('timeWindows'):
            time_windows = tw_data.get('timeWindows', [])
            
            # Collect all timewindow IDs and dates
            timewindow_ids = []
            timewindow_dates = []
            available_time_slots = []
            tsp_names = []
            
            for tw in time_windows:
                date = tw.get('date', '')
                if date:
                    timewindow_dates.append(date)
                
                # Time slots for this date
                time_slots = tw.get('timeSlots', [])
                for slot in time_slots:
                    tw_id = slot.get('timeWindowId')
                    if tw_id:
                        timewindow_ids.append(str(tw_id))
                    
                    # Build time slot description
                    start_time = slot.get('startTime', '')
                    end_time = slot.get('endTime', '')
                    if start_time and end_time:
                        available_time_slots.append(f"{date} {start_time}-{end_time}")
                    
                    tsp_name = slot.get('tspName', '')
                    if tsp_name:
                        tsp_names.append(tsp_name)
            
            result['timewindow_ids'] = '; '.join(set(timewindow_ids))
            result['timewindow_dates'] = '; '.join(set(timewindow_dates))
            result['available_time_slots'] = '; '.join(available_time_slots)
            result['tsp_names'] = '; '.join(set(tsp_names))
            result['has_timewindows'] = True
        else:
            result['timewindow_ids'] = ''
            result['timewindow_dates'] = ''
            result['available_time_slots'] = ''
            result['tsp_names'] = ''
            result['has_timewindows'] = False
    else:
        result['timewindows_success'] = False
        result['timewindow_ids'] = ''
        result['timewindow_dates'] = ''
        result['available_time_slots'] = ''
        result['tsp_names'] = ''
        result['has_timewindows'] = False
        result['timewindows_error'] = timewindows_response.get('error', '') if timewindows_response else ''
    
    return result

def load_masterfile():
    """Load the masterfile Excel"""
    masterfile_path = "masterfile-0805.xlsx"
    if not os.path.exists(masterfile_path):
        print(f"❌ Masterfile not found: {masterfile_path}")
        return None
    
    try:
        df = pd.read_excel(masterfile_path)
        print(f"📋 Loaded masterfile: {len(df):,} rows, {len(df.columns)} columns")
        print(f"📋 Masterfile columns: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"❌ Error loading masterfile: {e}")
        return None

def main():
    """Main function"""
    print("🔍 CREATING MASTERFILE-BASED TIME WINDOW RESULTS")
    print("=" * 70)
    
    # Load masterfile
    masterfile_df = load_masterfile()
    if masterfile_df is None:
        return
    
    # Find all API response files using ls command results
    api_files = glob.glob("results/timewindows*/api*.json")
    api_files.sort()
    
    print(f"📄 Found {len(api_files)} API response files")
    
    # Collect all responses by item ID
    parcel_responses = {}  # zipcode -> response data
    truck_responses = {}   # zipcode -> response data
    
    for api_file in api_files:
        print(f"\n📁 Processing: {api_file}")
        
        # Extract directory and timestamp
        dir_path = os.path.dirname(api_file)
        timestamp = os.path.basename(dir_path).replace('timewindows_test_', '')
        
        # Try to get item ID from progress file first
        progress_file = os.path.join(dir_path, f"progress_{timestamp}.json")
        item_id = get_item_id_from_progress(progress_file)
        
        # If no progress file or no item ID, try log file
        if not item_id:
            log_file = os.path.join(dir_path, f"timewindows_test_{timestamp}.log")
            item_id = get_item_id_from_log(log_file)
        
        if not item_id:
            print(f"  ⚠️  No item ID found, skipping")
            continue
        
        print(f"  📊 Item ID: {item_id}")
        
        # Extract responses
        responses = extract_api_responses(api_file)
        print(f"  ✅ Extracted {len(responses)} API responses")
        
        if len(responses) == 0:
            continue
        
        # Process responses and store by zipcode (keep latest/best result per zipcode)
        for response_data in responses:
            processed = process_response_for_masterfile(response_data)
            zipcode = processed.get('zipcode', '')
            
            if not zipcode:
                continue
            
            # Store in appropriate collection
            if item_id == PARCEL_ITEM_ID:
                # Keep the response with successful delivery or timewindows, or latest timestamp
                if zipcode not in parcel_responses:
                    parcel_responses[zipcode] = processed
                else:
                    # Keep better result (successful over failed, or latest timestamp)
                    current = parcel_responses[zipcode]
                    if (processed.get('delivery_success') and not current.get('delivery_success')) or \
                       (processed.get('timewindows_success') and not current.get('timewindows_success')) or \
                       (processed.get('timestamp', '') > current.get('timestamp', '')):
                        parcel_responses[zipcode] = processed
            
            elif item_id == TRUCK_ITEM_ID:
                # Same logic for truck
                if zipcode not in truck_responses:
                    truck_responses[zipcode] = processed
                else:
                    current = truck_responses[zipcode]
                    if (processed.get('delivery_success') and not current.get('delivery_success')) or \
                       (processed.get('timewindows_success') and not current.get('timewindows_success')) or \
                       (processed.get('timestamp', '') > current.get('timestamp', '')):
                        truck_responses[zipcode] = processed
    
    print(f"\n📊 COLLECTED RESPONSES:")
    print(f"  📦 Parcel responses: {len(parcel_responses)} unique zipcodes")
    print(f"  🚛 Truck responses: {len(truck_responses)} unique zipcodes")
    
    # Create Excel files based on masterfile
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create parcel Excel file
    if parcel_responses:
        parcel_df = masterfile_df.copy()
        
        # Add new columns for time window data
        parcel_df['delivery_arrangement_id'] = ''
        parcel_df['delivery_solution_id'] = ''
        parcel_df['delivery_id'] = ''
        parcel_df['delivery_success'] = False
        parcel_df['delivery_error'] = ''
        parcel_df['timewindow_ids'] = ''
        parcel_df['timewindow_dates'] = ''
        parcel_df['available_time_slots'] = ''
        parcel_df['tsp_names'] = ''
        parcel_df['timewindows_success'] = False
        parcel_df['timewindows_error'] = ''
        parcel_df['has_timewindows'] = False
        parcel_df['test_timestamp'] = ''
        
        # Fill in data for zipcodes we have responses for
        filled_count = 0
        for idx, row in parcel_df.iterrows():
            zipcode = str(row.get('ZipCode', '')).strip()
            if zipcode in parcel_responses:
                response = parcel_responses[zipcode]
                for key, value in response.items():
                    if key in parcel_df.columns:
                        parcel_df.at[idx, key] = value
                    elif key == 'timestamp':
                        parcel_df.at[idx, 'test_timestamp'] = value
                filled_count += 1
        
        parcel_file = f"masterfile_parcel_timewindows_{timestamp}.xlsx"
        parcel_df.to_excel(parcel_file, index=False)
        print(f"  ✅ Parcel masterfile saved: {parcel_file}")
        print(f"     📊 {len(parcel_df):,} total rows, {filled_count:,} with time window data")
    
    # Create truck Excel file
    if truck_responses:
        truck_df = masterfile_df.copy()
        
        # Add new columns for time window data
        truck_df['delivery_arrangement_id'] = ''
        truck_df['delivery_solution_id'] = ''
        truck_df['delivery_id'] = ''
        truck_df['delivery_success'] = False
        truck_df['delivery_error'] = ''
        truck_df['timewindow_ids'] = ''
        truck_df['timewindow_dates'] = ''
        truck_df['available_time_slots'] = ''
        truck_df['tsp_names'] = ''
        truck_df['timewindows_success'] = False
        truck_df['timewindows_error'] = ''
        truck_df['has_timewindows'] = False
        truck_df['test_timestamp'] = ''
        
        # Fill in data for zipcodes we have responses for
        filled_count = 0
        for idx, row in truck_df.iterrows():
            zipcode = str(row.get('ZipCode', '')).strip()
            if zipcode in truck_responses:
                response = truck_responses[zipcode]
                for key, value in response.items():
                    if key in truck_df.columns:
                        truck_df.at[idx, key] = value
                    elif key == 'timestamp':
                        truck_df.at[idx, 'test_timestamp'] = value
                filled_count += 1
        
        truck_file = f"masterfile_truck_timewindows_{timestamp}.xlsx"
        truck_df.to_excel(truck_file, index=False)
        print(f"  ✅ Truck masterfile saved: {truck_file}")
        print(f"     📊 {len(truck_df):,} total rows, {filled_count:,} with time window data")
    
    print(f"\n🏁 MASTERFILE-BASED EXTRACTION COMPLETE")
    print("=" * 70)

if __name__ == "__main__":
    main()
