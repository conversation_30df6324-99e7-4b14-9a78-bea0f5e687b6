#!/usr/bin/env python3
"""
Fix zipcode format in all retest files to ensure 6-character strings with leading zeros
"""

import pandas as pd
import glob

def fix_zipcode_format(input_file, output_file):
    """Fix zipcode format in a file"""
    try:
        df = pd.read_excel(input_file)
        
        # Convert ZipCode to string with leading zeros (6 characters)
        df['ZipCode'] = df['ZipCode'].astype(str).str.zfill(6)

        # Ensure it stays as string when saving to Excel
        df['ZipCode'] = df['ZipCode'].astype(str)

        # Save the corrected file with explicit string format
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Sheet1')
            # Format the ZipCode column as text
            worksheet = writer.sheets['Sheet1']
            for row in range(2, len(df) + 2):  # Start from row 2 (after header)
                cell = worksheet[f'A{row}']  # Assuming ZipCode is in column A
                cell.number_format = '@'  # Text format
        
        print(f"✅ Fixed {input_file} -> {output_file}")
        print(f"   📊 {len(df):,} zipcodes")
        
        # Show sample of corrected zipcodes
        print(f"   📋 Sample corrected zipcodes:")
        for i in range(min(5, len(df))):
            zipcode = df.iloc[i]['ZipCode']
            print(f"     {i+1}. {zipcode} (length: {len(zipcode)})")
        
        return True
    except Exception as e:
        print(f"❌ Error fixing {input_file}: {e}")
        return False

def main():
    """Main function"""
    print("🔧 FIXING ZIPCODE FORMAT IN ALL RETEST FILES")
    print("=" * 60)
    
    # Files to fix with proper zipcode formatting
    files_to_fix = [
        ('truck_failed_only_fixed.xlsx', 'truck_failed_only_zipcode_fixed.xlsx'),
        ('parcel_failed_only_fixed.xlsx', 'parcel_failed_only_zipcode_fixed.xlsx'),
        ('truck_retest_zipcodes_fixed.xlsx', 'truck_retest_zipcodes_zipcode_fixed.xlsx'),
        ('parcel_retest_zipcodes_fixed.xlsx', 'parcel_retest_zipcodes_zipcode_fixed.xlsx'),
        ('truck_untested_only_fixed.xlsx', 'truck_untested_only_zipcode_fixed.xlsx'),
        ('parcel_untested_only_fixed.xlsx', 'parcel_untested_only_zipcode_fixed.xlsx'),
    ]
    
    success_count = 0
    for input_file, output_file in files_to_fix:
        print(f"\n📁 Processing: {input_file}")
        if fix_zipcode_format(input_file, output_file):
            success_count += 1
    
    print(f"\n🏁 ZIPCODE FORMAT FIXING COMPLETE")
    print(f"✅ Successfully fixed: {success_count}/{len(files_to_fix)} files")
    print("=" * 60)
    
    # Verify the fixes
    print(f"\n🔍 VERIFICATION:")
    for input_file, output_file in files_to_fix:
        try:
            df = pd.read_excel(output_file)
            all_6_chars = all(len(str(zc)) == 6 for zc in df['ZipCode'])
            print(f"  {output_file}: {'✅ All 6 chars' if all_6_chars else '❌ Some not 6 chars'}")
        except:
            print(f"  {output_file}: ❌ Could not verify")

if __name__ == "__main__":
    main()
