#!/usr/bin/env python3
"""
Analyze log files to check how many zipcodes have been tested for truck vs parcel
"""

import os
import glob
import re
from collections import defaultdict

def analyze_log_file(log_file):
    """Analyze a single log file"""
    if not os.path.exists(log_file):
        return None
    
    result = {
        'file': log_file,
        'item_id': None,
        'item_type': 'Unknown',
        'total_zipcodes_tested': 0,
        'successful_deliveries': 0,
        'successful_timewindows': 0,
        'failed_deliveries': 0,
        'failed_timewindows': 0,
        'zipcodes_with_timewindows': 0,
        'unique_zipcodes': set(),
        'start_time': None,
        'end_time': None,
        'errors': []
    }
    
    try:
        with open(log_file, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # Extract item ID
                if 'Item ID:' in line and not result['item_id']:
                    match = re.search(r'Item ID:\s*(\d+)', line)
                    if match:
                        result['item_id'] = match.group(1)
                        result['item_type'] = '📦 Parcel' if result['item_id'] == '10534224' else '🚛 Truck' if result['item_id'] == '70570836' else '❓ Unknown'
                
                # Extract start/end times
                if 'Starting time window test' in line:
                    time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if time_match:
                        result['start_time'] = time_match.group(1)
                
                if 'Test completed' in line or 'Completed time window test' in line:
                    time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if time_match:
                        result['end_time'] = time_match.group(1)
                
                # Count zipcode tests - Updated pattern for actual log format
                # Pattern: "Row XXXX: ZIPCODE - SUCCESS" or "Row XXXX: ZIPCODE - FAILED"
                zipcode_match = re.search(r'Row \d+: (\d+) - (SUCCESS|FAILED)', line)
                if zipcode_match:
                    zipcode = zipcode_match.group(1)
                    status = zipcode_match.group(2)
                    result['unique_zipcodes'].add(zipcode)
                    result['total_zipcodes_tested'] += 1

                    if status == 'SUCCESS':
                        result['successful_deliveries'] += 1
                        result['successful_timewindows'] += 1
                    else:
                        result['failed_deliveries'] += 1
                        result['failed_timewindows'] += 1

                # Count zipcodes with available time windows
                if 'Time slots available:' in line:
                    result['zipcodes_with_timewindows'] += 1
                
                # Collect errors
                if 'ERROR' in line.upper() or 'FAILED' in line.upper():
                    if len(result['errors']) < 5:  # Keep only first 5 errors
                        result['errors'].append(line[:100])
        
        # Convert set to count
        result['unique_zipcodes_count'] = len(result['unique_zipcodes'])
        del result['unique_zipcodes']  # Remove the set to save memory
        
    except Exception as e:
        result['errors'].append(f"Error reading log file: {e}")
    
    return result

def main():
    """Main function"""
    print("🔍 ANALYZING LOG FILES FOR ZIPCODE TESTING")
    print("=" * 70)
    
    # Find all log files
    log_files = glob.glob("results/timewindows*/timewindows_test_*.log")
    log_files.sort()
    
    print(f"📄 Found {len(log_files)} log files")
    
    # Analyze each log file
    parcel_results = []
    truck_results = []
    unknown_results = []
    
    print(f"\n📊 ANALYZING LOG FILES:")
    
    for log_file in log_files:
        print(f"\n📁 {os.path.basename(log_file)}")
        
        result = analyze_log_file(log_file)
        if not result:
            print("  ❌ Could not analyze file")
            continue
        
        print(f"  🏷️  Item ID: {result['item_id']} ({result['item_type']})")
        print(f"  📊 Total zipcode tests: {result['total_zipcodes_tested']:,}")
        print(f"  📍 Unique zipcodes: {result['unique_zipcodes_count']:,}")
        print(f"  ✅ Successful deliveries: {result['successful_deliveries']:,}")
        print(f"  ✅ Successful time windows: {result['successful_timewindows']:,}")
        print(f"  ❌ Failed deliveries: {result['failed_deliveries']:,}")
        print(f"  ❌ Failed time windows: {result['failed_timewindows']:,}")
        print(f"  ⏰ Time windows available: {result['zipcodes_with_timewindows']:,}")
        
        if result['start_time'] and result['end_time']:
            print(f"  🕐 Duration: {result['start_time']} to {result['end_time']}")
        
        if result['errors']:
            print(f"  ⚠️  Sample errors: {len(result['errors'])} found")
        
        # Categorize results
        if result['item_id'] == '10534224':
            parcel_results.append(result)
        elif result['item_id'] == '70570836':
            truck_results.append(result)
        else:
            unknown_results.append(result)
    
    # Summary statistics
    print(f"\n{'='*70}")
    print(f"📊 COMPREHENSIVE SUMMARY:")
    
    def summarize_results(results, category_name):
        if not results:
            print(f"  {category_name}: No data found")
            return
        
        total_tests = sum(r['total_zipcodes_tested'] for r in results)
        unique_zipcodes = set()
        for r in results:
            # We need to re-read to get unique zipcodes across all files
            pass
        total_successful_deliveries = sum(r['successful_deliveries'] for r in results)
        total_successful_timewindows = sum(r['successful_timewindows'] for r in results)
        total_failed_deliveries = sum(r['failed_deliveries'] for r in results)
        total_failed_timewindows = sum(r['failed_timewindows'] for r in results)
        total_with_timewindows = sum(r['zipcodes_with_timewindows'] for r in results)
        
        print(f"  {category_name}:")
        print(f"    📄 Log files: {len(results)}")
        print(f"    📊 Total zipcode tests: {total_tests:,}")
        print(f"    ✅ Successful deliveries: {total_successful_deliveries:,}")
        print(f"    ✅ Successful time windows: {total_successful_timewindows:,}")
        print(f"    ❌ Failed deliveries: {total_failed_deliveries:,}")
        print(f"    ❌ Failed time windows: {total_failed_timewindows:,}")
        print(f"    ⏰ With time windows: {total_with_timewindows:,}")
        
        if total_tests > 0:
            delivery_success_rate = total_successful_deliveries / total_tests * 100
            timewindow_success_rate = total_successful_timewindows / total_tests * 100
            print(f"    📈 Delivery success rate: {delivery_success_rate:.1f}%")
            print(f"    📈 Time window success rate: {timewindow_success_rate:.1f}%")
    
    summarize_results(parcel_results, "📦 PARCEL TESTS")
    summarize_results(truck_results, "🚛 TRUCK TESTS")
    summarize_results(unknown_results, "❓ UNKNOWN TESTS")
    
    print(f"{'='*70}")

if __name__ == "__main__":
    main()
