#!/usr/bin/env python3
"""
Debug the masterfile-based results to see why success rates are 0%
"""

import pandas as pd
import os
import glob

def debug_masterfile_excel(excel_path, file_type):
    """Debug a masterfile-based Excel file"""
    if not os.path.exists(excel_path):
        print(f"❌ File not found: {excel_path}")
        return
    
    print(f"\n{'='*60}")
    print(f"🔍 DEBUGGING: {file_type.upper()} MASTERFILE")
    print(f"📄 File: {excel_path}")
    print(f"{'='*60}")
    
    try:
        # Load the Excel file
        df = pd.read_excel(excel_path)
        
        # Check data types of key columns
        key_columns = ['delivery_success', 'timewindows_success', 'has_timewindows', 'test_timestamp']
        print(f"📊 DATA TYPES:")
        for col in key_columns:
            if col in df.columns:
                dtype = df[col].dtype
                unique_values = df[col].unique()[:10]  # First 10 unique values
                print(f"  {col}: {dtype}")
                print(f"    Unique values: {unique_values}")
        
        # Check rows with test_timestamp (these should have actual data)
        rows_with_timestamp = df[df['test_timestamp'].notna()]
        print(f"\n📊 ROWS WITH TEST TIMESTAMP: {len(rows_with_timestamp)}")
        
        if len(rows_with_timestamp) > 0:
            print(f"📋 SAMPLE ROWS WITH TIMESTAMP:")
            for i, (idx, row) in enumerate(rows_with_timestamp.head(5).iterrows()):
                zipcode = row.get('ZipCode', 'N/A')
                delivery_success = row.get('delivery_success', 'N/A')
                timewindows_success = row.get('timewindows_success', 'N/A')
                has_timewindows = row.get('has_timewindows', 'N/A')
                arrangement_id = row.get('delivery_arrangement_id', 'N/A')
                timewindow_dates = row.get('timewindow_dates', 'N/A')
                timestamp = row.get('test_timestamp', 'N/A')
                
                print(f"  Row {idx}: ZipCode={zipcode}")
                print(f"    delivery_success={delivery_success} ({type(delivery_success)})")
                print(f"    timewindows_success={timewindows_success} ({type(timewindows_success)})")
                print(f"    has_timewindows={has_timewindows} ({type(has_timewindows)})")
                print(f"    arrangement_id={arrangement_id}")
                print(f"    timewindow_dates={timewindow_dates}")
                print(f"    timestamp={timestamp}")
        
        # Check for True values specifically
        delivery_true_count = (df['delivery_success'] == True).sum()
        timewindows_true_count = (df['timewindows_success'] == True).sum()
        has_timewindows_true_count = (df['has_timewindows'] == True).sum()
        
        print(f"\n📊 TRUE VALUE COUNTS:")
        print(f"  delivery_success == True: {delivery_true_count}")
        print(f"  timewindows_success == True: {timewindows_true_count}")
        print(f"  has_timewindows == True: {has_timewindows_true_count}")
        
        # Check for any non-empty arrangement IDs
        non_empty_arrangement_ids = df[df['delivery_arrangement_id'].notna() & (df['delivery_arrangement_id'] != '')]
        print(f"  Non-empty arrangement IDs: {len(non_empty_arrangement_ids)}")
        
        if len(non_empty_arrangement_ids) > 0:
            print(f"📋 SAMPLE ROWS WITH ARRANGEMENT IDs:")
            for i, (idx, row) in enumerate(non_empty_arrangement_ids.head(3).iterrows()):
                zipcode = row.get('ZipCode', 'N/A')
                arrangement_id = row.get('delivery_arrangement_id', 'N/A')
                print(f"  Row {idx}: ZipCode={zipcode}, ArrangementID={arrangement_id}")
        
    except Exception as e:
        print(f"❌ Error debugging file: {e}")

def main():
    """Main function"""
    print("🔍 DEBUGGING MASTERFILE-BASED RESULTS")
    print("=" * 60)
    
    # Find the latest masterfile-based files
    parcel_files = glob.glob("masterfile_parcel_timewindows_*.xlsx")
    truck_files = glob.glob("masterfile_truck_timewindows_*.xlsx")
    
    if parcel_files:
        latest_parcel = sorted(parcel_files)[-1]
        debug_masterfile_excel(latest_parcel, "parcel")
    
    if truck_files:
        latest_truck = sorted(truck_files)[-1]
        debug_masterfile_excel(latest_truck, "truck")

if __name__ == "__main__":
    main()
