# Delivery Checker Configuration
# Copy this file to .env and fill in your actual values

# Microsoft OAuth Configuration
# Get these from your Azure AD app registration
CLIENT_ID=your_client_id_here
CLIENT_SECRET=your_client_secret_here

# API Configuration
CONCURRENCY=5

# File Configuration
INPUT_EXCEL_FILE=masterfile-0805.xlsx
OUTPUT_EXCEL_FILE=delivery_test_results.xlsx
PROGRESS_FILE=progress.json
RESPONSE_LOG_FILE=api_responses.json

# Output Directory Configuration
OUTPUT_DIR=results

# Optional: Override default endpoints (usually not needed)
# TOKEN_URL=https://login.microsoftonline.com/720b637a-655a-40cf-816a-f22f40755c2c/oauth2/v2.0/token
# API_URL=https://private-api.ingka.prodcn.ikea.com/cfb/customer-promise/cn/delivery-arrangement

# Logging Configuration (optional)
# LOG_LEVEL=INFO
# LOG_FILE=delivery_test.log
