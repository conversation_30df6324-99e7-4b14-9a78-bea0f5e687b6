#!/usr/bin/env python3
"""
Fix retest files to have the correct column name (ZipCode instead of zipcode)
"""

import pandas as pd
import glob

def fix_retest_file(input_file, output_file):
    """Fix column name in retest file"""
    try:
        df = pd.read_excel(input_file)
        if 'zipcode' in df.columns:
            df = df.rename(columns={'zipcode': 'ZipCode'})
            df.to_excel(output_file, index=False)
            print(f"✅ Fixed {input_file} -> {output_file}")
            print(f"   📊 {len(df):,} zipcodes")
            return True
        else:
            print(f"⚠️  {input_file} already has correct column names")
            return False
    except Exception as e:
        print(f"❌ Error fixing {input_file}: {e}")
        return False

def main():
    """Main function"""
    print("🔧 FIXING RETEST FILES COLUMN NAMES")
    print("=" * 50)
    
    # Files to fix
    files_to_fix = [
        ('truck_failed_only_20250805_231642.xlsx', 'truck_failed_only_fixed.xlsx'),
        ('parcel_failed_only_20250805_231642.xlsx', 'parcel_failed_only_fixed.xlsx'),
        ('truck_retest_zipcodes_20250805_231642.xlsx', 'truck_retest_zipcodes_fixed.xlsx'),
        ('parcel_retest_zipcodes_20250805_231642.xlsx', 'parcel_retest_zipcodes_fixed.xlsx'),
        ('truck_untested_only_20250805_231642.xlsx', 'truck_untested_only_fixed.xlsx'),
        ('parcel_untested_only_20250805_231642.xlsx', 'parcel_untested_only_fixed.xlsx'),
    ]
    
    for input_file, output_file in files_to_fix:
        fix_retest_file(input_file, output_file)
    
    print(f"\n🏁 COLUMN NAME FIXING COMPLETE")
    print("=" * 50)

if __name__ == "__main__":
    main()
