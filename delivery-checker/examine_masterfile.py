#!/usr/bin/env python3
"""
Examine the masterfile structure to understand its columns and data
"""

import pandas as pd
import os

def main():
    """Main function"""
    print("🔍 EXAMINING MASTERFILE STRUCTURE")
    print("=" * 50)
    
    masterfile_path = "masterfile-0805.xlsx"
    if not os.path.exists(masterfile_path):
        print(f"❌ Masterfile not found: {masterfile_path}")
        return
    
    try:
        df = pd.read_excel(masterfile_path)
        print(f"📋 Masterfile: {len(df):,} rows, {len(df.columns)} columns")
        
        print(f"\n📋 COLUMNS:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1:2d}. {col}")
        
        print(f"\n📋 SAMPLE DATA (first 5 rows):")
        print(df.head().to_string())
        
        print(f"\n📋 DATA TYPES:")
        print(df.dtypes.to_string())
        
        # Check if zipcode column exists
        if 'zipcode' in df.columns:
            print(f"\n📍 ZIPCODE ANALYSIS:")
            print(f"  Total zipcodes: {len(df)}")
            print(f"  Unique zipcodes: {df['zipcode'].nunique()}")
            print(f"  Sample zipcodes: {df['zipcode'].head(10).tolist()}")
        
    except Exception as e:
        print(f"❌ Error examining masterfile: {e}")

if __name__ == "__main__":
    main()
