[{"zipcode": "638002", "timestamp": "2025-08-05T16:26:06.218532", "success": true, "status_code": 200, "response_time": 0.486191, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "638002"}, "deliveryArrangementsId": "20250805082606047986880", "itemLines": {"itemLine": [{"unitWeight": "0.076", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0005", "id": "1", "itemNo": "60561007", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T08:26:06"}]}}]}, "checkInventory": "true"}, "delivery_arrangement_id": "20250805082606047986880", "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1162", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96916415", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1162", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 08:26:06 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "8f74c1e9-b9b7-464c-bde4-b2c466f3534f", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}]