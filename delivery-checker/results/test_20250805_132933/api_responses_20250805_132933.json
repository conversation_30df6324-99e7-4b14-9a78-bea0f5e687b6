[{"zipcode": 404101, "timestamp": "2025-08-05T13:29:46.501656", "success": true, "status_code": 200, "response_time": 0.42395, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "404101"}, "deliveryArrangementsId": "20250805052946294683576", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T05:29:46"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1192", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96957244", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1192", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 05:29:46 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "3daf24d6-3956-467c-8bfe-daa8391d35a5", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": 409600, "timestamp": "2025-08-05T13:30:04.898120", "success": true, "status_code": 200, "response_time": 0.487235, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "409600"}, "deliveryArrangementsId": "20250805053004637214555", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T05:30:04"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1197", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96956779", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1197", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 05:30:04 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "19987c20-e0ce-4fab-a7dc-25e6951c1faa", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}]