#!/usr/bin/env python3
"""
Robust counting and extraction of API responses, handling corrupted JSON files
"""

import json
import glob
import os
import re
from collections import defaultdict

def safe_json_load(json_file):
    """Safely load JSON file, handling various formats and corruption"""
    responses = []
    
    try:
        # First try: Load as complete JSON array
        with open(json_file, 'r') as f:
            data = json.load(f)
            if isinstance(data, list):
                return data
            else:
                return [data]
    except json.JSONDecodeError:
        pass
    
    try:
        # Second try: Line-by-line JSON
        with open(json_file, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:
                    try:
                        response_data = json.loads(line)
                        responses.append(response_data)
                    except json.JSONDecodeError as e:
                        # Skip corrupted lines but continue
                        if line_num <= 10:  # Only report first 10 errors
                            print(f"    ⚠️  Line {line_num}: JSON error - {str(e)[:50]}...")
                        continue
    except Exception as e:
        print(f"    ❌ Error reading file: {e}")
    
    return responses

def extract_item_id_from_responses(responses):
    """Try to extract item ID from the actual API responses"""
    for response in responses[:10]:  # Check first 10 responses
        # Look for item ID in delivery response
        delivery_response = response.get('delivery_response', {})
        if delivery_response and isinstance(delivery_response, dict):
            data = delivery_response.get('data', {})
            if data and isinstance(data, dict):
                # Check various possible fields
                for field in ['itemId', 'item_id', 'productId', 'product_id']:
                    if field in data:
                        return str(data[field])
        
        # Look for item ID in the request data
        if 'item_id' in response:
            return str(response['item_id'])
    
    return None

def get_item_id_comprehensive(json_file):
    """Get item ID using multiple methods"""
    dir_path = os.path.dirname(json_file)
    timestamp = os.path.basename(dir_path).replace('timewindows_test_', '')
    
    # Method 1: Progress file
    progress_file = os.path.join(dir_path, f"progress_{timestamp}.json")
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r') as f:
                progress_data = json.load(f)
                item_id = str(progress_data.get('item_id', ''))
                if item_id:
                    return item_id
        except:
            pass
    
    # Method 2: Log file
    log_file = os.path.join(dir_path, f"timewindows_test_{timestamp}.log")
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r') as f:
                for line in f:
                    match = re.search(r'Item ID:\s*(\d+)', line)
                    if match:
                        return match.group(1)
        except:
            pass
    
    # Method 3: Extract from API responses themselves
    responses = safe_json_load(json_file)
    if responses:
        item_id = extract_item_id_from_responses(responses)
        if item_id:
            return item_id
    
    return "Unknown"

def main():
    """Main function"""
    print("🔍 ROBUST API RESPONSE COUNTING AND EXTRACTION")
    print("=" * 70)
    
    # Find all API response files
    api_files = glob.glob("results/timewindows*/api*.json")
    api_files.sort()
    
    print(f"📄 Found {len(api_files)} API response files")
    
    # Statistics
    stats = {
        'total_files': len(api_files),
        'valid_files': 0,
        'corrupted_files': 0,
        'total_responses': 0,
        'parcel_responses': 0,
        'truck_responses': 0,
        'unknown_responses': 0
    }
    
    file_details = []
    
    print(f"\n📊 PROCESSING FILES:")
    
    for api_file in api_files:
        print(f"\n📁 {os.path.basename(api_file)}")
        
        # Get file size
        file_size = os.path.getsize(api_file)
        size_mb = file_size / (1024 * 1024)
        print(f"  📏 Size: {size_mb:.1f} MB")
        
        # Get item ID
        item_id = get_item_id_comprehensive(api_file)
        item_type = "📦 Parcel" if item_id == "10534224" else "🚛 Truck" if item_id == "70570836" else "❓ Unknown"
        print(f"  🏷️  Item ID: {item_id} ({item_type})")
        
        # Load and count responses
        responses = safe_json_load(api_file)
        count = len(responses)
        print(f"  📊 Responses: {count:,}")
        
        if count > 0:
            stats['valid_files'] += 1
            stats['total_responses'] += count
            
            if item_id == "10534224":
                stats['parcel_responses'] += count
            elif item_id == "70570836":
                stats['truck_responses'] += count
            else:
                stats['unknown_responses'] += count
        else:
            stats['corrupted_files'] += 1
        
        file_details.append({
            'file': api_file,
            'size_mb': size_mb,
            'item_id': item_id,
            'item_type': item_type,
            'count': count
        })
    
    print(f"\n{'='*70}")
    print(f"📊 COMPREHENSIVE SUMMARY:")
    print(f"  📄 Total files processed: {stats['total_files']}")
    print(f"  ✅ Valid files: {stats['valid_files']}")
    print(f"  ❌ Corrupted/empty files: {stats['corrupted_files']}")
    print(f"  📊 Total responses: {stats['total_responses']:,}")
    print(f"  📦 Parcel responses: {stats['parcel_responses']:,}")
    print(f"  🚛 Truck responses: {stats['truck_responses']:,}")
    print(f"  ❓ Unknown item responses: {stats['unknown_responses']:,}")
    
    print(f"\n📋 DETAILED BREAKDOWN:")
    for detail in file_details:
        if detail['count'] > 0:
            print(f"  {detail['item_type']} {os.path.basename(detail['file'])}: {detail['count']:,} responses")
    
    print(f"{'='*70}")

if __name__ == "__main__":
    main()
