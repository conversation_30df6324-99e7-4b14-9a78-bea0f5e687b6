#!/usr/bin/env python3
"""
Analyze the masterfile-based time window results Excel files
"""

import pandas as pd
import os
import glob
from datetime import datetime

def analyze_masterfile_excel(excel_path, file_type):
    """Analyze a masterfile-based Excel file"""
    if not os.path.exists(excel_path):
        print(f"❌ File not found: {excel_path}")
        return
    
    print(f"\n{'='*80}")
    print(f"📊 ANALYZING: {file_type.upper()} MASTERFILE-BASED RESULTS")
    print(f"📄 File: {excel_path}")
    print(f"{'='*80}")
    
    try:
        # Load the Excel file
        df = pd.read_excel(excel_path)
        print(f"📋 Total rows: {len(df):,}")
        print(f"📋 Total columns: {len(df.columns)}")
        
        # Count rows with time window data
        rows_with_data = df['delivery_success'].notna().sum()
        rows_with_delivery_success = df['delivery_success'].sum()
        rows_with_timewindows_success = df['timewindows_success'].sum()
        rows_with_timewindows = df['has_timewindows'].sum()
        
        print(f"\n📊 TIME WINDOW DATA COVERAGE:")
        print(f"  Rows with test data:        {rows_with_data:,} ({rows_with_data/len(df)*100:.1f}%)")
        print(f"  Delivery API success:       {rows_with_delivery_success:,} ({rows_with_delivery_success/len(df)*100:.1f}%)")
        print(f"  Time Windows API success:   {rows_with_timewindows_success:,} ({rows_with_timewindows_success/len(df)*100:.1f}%)")
        print(f"  Has available time windows: {rows_with_timewindows:,} ({rows_with_timewindows/len(df)*100:.1f}%)")
        
        # Success rates among tested zipcodes
        if rows_with_data > 0:
            delivery_success_rate = rows_with_delivery_success / rows_with_data * 100
            timewindows_success_rate = rows_with_timewindows_success / rows_with_data * 100
            timewindows_available_rate = rows_with_timewindows / rows_with_data * 100
            
            print(f"\n📊 SUCCESS RATES (among tested zipcodes):")
            print(f"  Delivery API success rate:       {delivery_success_rate:.1f}%")
            print(f"  Time Windows API success rate:   {timewindows_success_rate:.1f}%")
            print(f"  Time windows available rate:     {timewindows_available_rate:.1f}%")
        
        # Geographic distribution of successful tests
        successful_df = df[df['delivery_success'] == True]
        if len(successful_df) > 0:
            print(f"\n🌍 GEOGRAPHIC DISTRIBUTION (successful deliveries):")
            province_counts = successful_df['Province-EN'].value_counts().head(10)
            for province, count in province_counts.items():
                print(f"  {province}: {count:,} zipcodes")
        
        # Time window dates analysis
        timewindow_dates = df['timewindow_dates'].dropna()
        if len(timewindow_dates) > 0:
            print(f"\n📅 AVAILABLE DELIVERY DATES:")
            all_dates = []
            for dates_str in timewindow_dates:
                if dates_str and dates_str != '':
                    dates = dates_str.split('; ')
                    all_dates.extend(dates)
            
            unique_dates = sorted(set(all_dates))
            print(f"  Unique delivery dates found: {len(unique_dates)}")
            if unique_dates:
                print(f"  Date range: {unique_dates[0]} to {unique_dates[-1]}")
                print(f"  Sample dates: {', '.join(unique_dates[:5])}")
        
        # TSP analysis
        tsp_names = df['tsp_names'].dropna()
        if len(tsp_names) > 0:
            print(f"\n🚛 TRANSPORT SERVICE PROVIDERS:")
            all_tsps = []
            for tsp_str in tsp_names:
                if tsp_str and tsp_str != '':
                    tsps = tsp_str.split('; ')
                    all_tsps.extend(tsps)
            
            unique_tsps = sorted(set(all_tsps))
            print(f"  Unique TSPs found: {len(unique_tsps)}")
            if unique_tsps:
                for i, tsp in enumerate(unique_tsps[:10]):  # Show top 10
                    print(f"    {i+1:2d}. {tsp}")
        
        # Sample successful records with full geographic info
        successful_with_timewindows = df[(df['delivery_success'] == True) & (df['has_timewindows'] == True)]
        if len(successful_with_timewindows) > 0:
            print(f"\n✅ SAMPLE SUCCESSFUL RECORDS WITH TIME WINDOWS:")
            for i, (idx, row) in enumerate(successful_with_timewindows.head(5).iterrows()):
                zipcode = row.get('ZipCode', 'N/A')
                province = row.get('Province-EN', 'N/A')
                city = row.get('City-EN', 'N/A')
                district = row.get('District-EN', 'N/A')
                available_dates = row.get('timewindow_dates', 'N/A')
                tsp_names = row.get('tsp_names', 'N/A')
                arrangement_id = row.get('delivery_arrangement_id', 'N/A')
                
                print(f"  {i+1}. Zipcode: {zipcode} ({province}, {city}, {district})")
                print(f"     Arrangement ID: {arrangement_id}")
                print(f"     Available dates: {available_dates}")
                print(f"     TSPs: {tsp_names}")
        
        # Show new columns added
        new_columns = [
            'delivery_arrangement_id', 'delivery_solution_id', 'delivery_id', 
            'delivery_success', 'delivery_error', 'timewindow_ids', 'timewindow_dates',
            'available_time_slots', 'tsp_names', 'timewindows_success', 
            'timewindows_error', 'has_timewindows', 'test_timestamp'
        ]
        
        print(f"\n📋 NEW COLUMNS ADDED TO MASTERFILE:")
        for i, col in enumerate(new_columns):
            non_empty = df[col].notna().sum() if col in df.columns else 0
            print(f"  {i+1:2d}. {col}: {non_empty:,} non-empty values")
        
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")

def main():
    """Main function"""
    print("🔍 MASTERFILE-BASED TIME WINDOW RESULTS ANALYSIS")
    print("=" * 80)
    
    # Find the latest masterfile-based files
    parcel_files = glob.glob("masterfile_parcel_timewindows_*.xlsx")
    truck_files = glob.glob("masterfile_truck_timewindows_*.xlsx")
    
    if parcel_files:
        latest_parcel = sorted(parcel_files)[-1]
        analyze_masterfile_excel(latest_parcel, "parcel")
    
    if truck_files:
        latest_truck = sorted(truck_files)[-1]
        analyze_masterfile_excel(latest_truck, "truck")
    
    print(f"\n{'='*80}")
    print("🏁 MASTERFILE ANALYSIS COMPLETE")
    print(f"{'='*80}")
    
    # Show file sizes
    print(f"\n📁 FILE INFORMATION:")
    if parcel_files:
        latest_parcel = sorted(parcel_files)[-1]
        size = os.path.getsize(latest_parcel) / (1024*1024)  # MB
        print(f"  📦 Parcel file: {latest_parcel} ({size:.1f} MB)")
    
    if truck_files:
        latest_truck = sorted(truck_files)[-1]
        size = os.path.getsize(latest_truck) / (1024*1024)  # MB
        print(f"  🚛 Truck file: {latest_truck} ({size:.1f} MB)")

if __name__ == "__main__":
    main()
