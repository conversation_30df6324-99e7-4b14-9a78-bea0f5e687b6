#!/usr/bin/env python3
"""
Time Windows API Batch Testing Script

Tests time-windows API for all zipcodes in masterfile using delivery-arrangement data.
Features: OAuth token management, concurrent processing, comprehensive logging.
"""

import os
import sys
import json
import logging
import argparse
import requests
import pandas as pd
from datetime import datetime, timedelta, timezone
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
from dotenv import load_dotenv
import urllib3
import time
import random

# Disable SSL warnings for internal APIs
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
load_dotenv()

logger = logging.getLogger(__name__)

class TimeWindowsTester:
    def __init__(self, output_dir=None):
        """Initialize the TimeWindowsTester"""
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if output_dir:
            self.output_dir = output_dir
            # Extract timestamp from existing directory name if possible
            if 'timewindows_test_' in output_dir:
                try:
                    self.timestamp = output_dir.split('timewindows_test_')[1]
                except:
                    pass
        else:
            self.output_dir = f"results/timewindows_test_{self.timestamp}"

        os.makedirs(self.output_dir, exist_ok=True)
        
        # Configuration
        self.client_id = os.getenv('CLIENT_ID')
        self.client_secret = os.getenv('CLIENT_SECRET')
        self.token_url = os.getenv('TOKEN_URL')
        self.delivery_api_url = os.getenv('API_URL')
        self.timewindows_api_url = "https://private-api.ingka.prodcn.ikea.com/cfb/customer-promise/cn/time-windows"
        self.concurrency = int(os.getenv('CONCURRENCY', 15))
        self.input_file = os.getenv('INPUT_EXCEL_FILE', 'masterfile-0805.xlsx')
        
        # Output files
        self.excel_output = f"{self.output_dir}/timewindows_test_results_{self.timestamp}.xlsx"
        self.log_file = f"{self.output_dir}/timewindows_test_{self.timestamp}.log"
        self.api_responses_file = f"{self.output_dir}/api_responses_{self.timestamp}.json"
        self.progress_file = f"{self.output_dir}/progress_{self.timestamp}.json"
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        
        logger.info(f"TimeWindows test initialized: {self.output_dir}")
        logger.info(f"Concurrency: {self.concurrency}")
        
        # Thread safety
        self.lock = Lock()
        self.access_token = None
        self.token_expires_at = None
        
        # API response storage
        self.api_responses = []

        # Load existing progress if continuing
        self.progress = self.load_existing_progress()

        # Load existing API responses if continuing
        self.load_existing_api_responses()

    def load_existing_progress(self):
        """Load existing progress if continuing a test"""
        progress_files = [f for f in os.listdir(self.output_dir) if f.startswith('progress_') and f.endswith('.json')]

        if progress_files:
            # Use the most recent progress file
            progress_file = max(progress_files, key=lambda f: os.path.getmtime(os.path.join(self.output_dir, f)))
            progress_path = os.path.join(self.output_dir, progress_file)

            try:
                with open(progress_path, 'r') as f:
                    existing_progress = json.load(f)

                logger.info(f"Loaded existing progress: {existing_progress.get('completed_rows', 0)} rows completed")
                return existing_progress

            except Exception as e:
                logger.warning(f"Could not load existing progress: {e}")

        # Return default progress if no existing progress found
        return {
            'total_rows': 0,
            'completed_rows': 0,
            'successful_delivery': 0,
            'successful_timewindows': 0,
            'failed_delivery': 0,
            'failed_timewindows': 0,
            'start_time': datetime.now().isoformat(),
            'last_update_time': datetime.now().isoformat(),
            'item_id': None,
            'concurrency': self.concurrency,
            'start_row': 0,
            'end_row': 0,
            'current_row': 0,
            'input_file': self.input_file,
            'test_directory': self.output_dir,
            'timestamp': self.timestamp,
            'batch_size': 100,
            'completion_percentage': 0.0,
            'estimated_remaining_time': None,
            'average_time_per_row': None
        }

    def load_existing_api_responses(self):
        """Load existing API responses if continuing a test"""
        api_files = [f for f in os.listdir(self.output_dir) if f.startswith('api_responses_') and f.endswith('.json')]

        if api_files:
            # Use the most recent API responses file
            api_file = max(api_files, key=lambda f: os.path.getmtime(os.path.join(self.output_dir, f)))
            api_path = os.path.join(self.output_dir, api_file)

            try:
                with open(api_path, 'r') as f:
                    existing_responses = json.load(f)

                if isinstance(existing_responses, list):
                    self.api_responses = existing_responses
                    logger.info(f"Loaded {len(self.api_responses)} existing API responses")

            except Exception as e:
                logger.warning(f"Could not load existing API responses: {e}")

    def get_access_token(self):
        """Get or refresh OAuth access token"""
        with self.lock:
            if self.access_token and self.token_expires_at and datetime.now() < self.token_expires_at:
                return self.access_token
            
            logger.info("Requesting new access token...")
            
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            data = {
                'scope': 'https://api.prod.cn.ingka.com/.default',
                'grant_type': 'client_credentials',
                'client_id': self.client_id,
                'client_secret': self.client_secret
            }
            
            try:
                response = self.retry_on_network_error(
                    requests.post, self.token_url, headers=headers, data=data, verify=False
                )

                if response.status_code == 200:
                    token_data = response.json()
                    self.access_token = token_data['access_token']
                    expires_in = int(token_data.get('expires_in', 3600))
                    self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 60)
                    logger.info(f"Access token obtained, expires at: {self.token_expires_at}")
                    return self.access_token
                else:
                    raise Exception(f"Failed to get access token: {response.status_code} - {response.text}")
            except Exception as e:
                logger.error(f"Failed to get access token after retries: {e}")
                raise

    def call_delivery_arrangement(self, zipcode, item_id):
        """Call delivery-arrangement API"""
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.get_access_token()}'
        }
        
        payload = {
            "checkCapacity": True,
            "checkInventory": True,
            "businessUnit": {
                "type": "STO",
                "code": "1228"
            },
            "channelReferences": {
                "sellingChannelName": "TimeWindowsBatchTester"
            },
            "shipToAddress": {
                "country": "CN",
                "zipCode": str(zipcode)
            },
            "itemLines": {
                "itemLine": [
                    {
                        "itemType": "ART",
                        "itemNo": str(item_id),
                        "id": "1",
                        "requiredQty": 1
                    }
                ]
            },
            "serviceTypes": {
                "serviceType": [
                    {
                        "id": "HOME_DELIVERY"
                    }
                ]
            },
            "useLeadTimeOrchestration": True,
            "checkNoStock": True
        }
        
        response = requests.post(
            self.delivery_api_url,
            headers=headers,
            json=payload,
            timeout=30,
            verify=False
        )
        
        return response, payload

    def call_time_windows(self, delivery_arrangement_id, solution_id, delivery_id):
        """Call time-windows API"""
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.get_access_token()}'
        }
        
        current_time = datetime.now(timezone.utc).isoformat()
        
        payload = {
            "deliveryArrangementsId": delivery_arrangement_id,
            "returnMultipleSrvcSlots": False,
            "selectedSolution": {
                "solutionId": solution_id,
                "deliveryLines": {
                    "deliveryLine": [
                        {
                            "deliveryId": delivery_id,
                            "serviceSearchWindow": 15,
                            "reqstartDate": current_time,
                            "isExceptionalQty": False,
                            "isExceptionalVolume": False,
                            "returnMultipleSrvcSlots": False
                        }
                    ]
                }
            }
        }
        
        response = requests.post(
            self.timewindows_api_url,
            headers=headers,
            json=payload,
            timeout=30,
            verify=False
        )
        
        return response, payload

    def extract_delivery_info(self, delivery_result):
        """Extract delivery arrangement information"""
        delivery_arrangement_id = delivery_result.get('deliveryArrangementsId')
        if not delivery_arrangement_id:
            return None, None, None, "No deliveryArrangementsId found"

        service_types = delivery_result.get('serviceTypes', {})
        service_type_list = service_types.get('serviceType', [])

        if not service_type_list:
            return delivery_arrangement_id, None, None, "No service types found"

        first_service = service_type_list[0] if isinstance(service_type_list, list) else service_type_list
        possible_solutions = first_service.get('possibleSolutions', {})
        solution_list = possible_solutions.get('possibleSolution', [])

        if not solution_list:
            return delivery_arrangement_id, None, None, "No solutions found"

        first_solution = solution_list[0] if isinstance(solution_list, list) else solution_list
        solution_id = first_solution.get('id')

        if not solution_id:
            return delivery_arrangement_id, None, None, "No solution ID found"

        delivery_lines = first_solution.get('deliveryLines', {})
        delivery_line_list = delivery_lines.get('deliveryLine', [])

        if not delivery_line_list:
            return delivery_arrangement_id, solution_id, None, "No delivery lines found"

        first_delivery_line = delivery_line_list[0] if isinstance(delivery_line_list, list) else delivery_line_list
        delivery_id = first_delivery_line.get('deliveryId')

        if not delivery_id:
            return delivery_arrangement_id, solution_id, None, "No delivery ID found"

        return delivery_arrangement_id, solution_id, delivery_id, None

    def extract_timewindow_info(self, timewindows_result):
        """Extract time window information for Excel output"""
        info = {
            'has_slots': False,
            'available_dates': [],
            'time_windows': [],
            'tsp_names': [],
            'tsp_ids': [],
            'nodes': [],
            'capacity_available': [],
            'payment_cutoff_times': [],
            'resource_pool_ids': [],
            'time_window_ids': []
        }

        try:
            selected_solution = timewindows_result.get('selectedSolution', {})
            delivery_lines = selected_solution.get('deliveryLines', {})
            delivery_line_list = delivery_lines.get('deliveryLine', [])

            if not delivery_line_list:
                return info

            for delivery_line in delivery_line_list:
                slots = delivery_line.get('slots', {})
                slot_list = slots.get('slot', [])

                if not slot_list:
                    continue

                info['has_slots'] = True

                for slot in slot_list:
                    start_time = slot.get('startTime', '')
                    end_time = slot.get('endTime', '')
                    if start_time and end_time:
                        info['time_windows'].append(f"{start_time}-{end_time}")

                    available_dates = slot.get('availableDates', {})
                    available_date_list = available_dates.get('availableDate', [])

                    for date_info in available_date_list:
                        info['available_dates'].append(date_info.get('date', ''))
                        info['tsp_names'].append(date_info.get('tspName', ''))
                        info['tsp_ids'].append(date_info.get('tspID', ''))
                        info['nodes'].append(date_info.get('node', ''))
                        info['capacity_available'].append(date_info.get('capacityAvailable', ''))
                        info['payment_cutoff_times'].append(date_info.get('paymentCutOffDateTime', ''))
                        info['resource_pool_ids'].append(date_info.get('resourcePoolId', ''))
                        info['time_window_ids'].append(date_info.get('timeWindowId', ''))

        except Exception as e:
            logger.warning(f"Error extracting timewindow info: {e}")

        return info

    def save_api_response(self, zipcode, item_id, delivery_request, delivery_response,
                         timewindows_request=None, timewindows_response=None):
        """Save detailed API request/response"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'zipcode': zipcode,
            'item_id': item_id,
            'delivery_arrangement': {
                'request': delivery_request,
                'response': {
                    'status_code': delivery_response.status_code,
                    'headers': dict(delivery_response.headers),
                    'body': delivery_response.json() if delivery_response.status_code == 200 else delivery_response.text
                }
            }
        }

        if timewindows_request and timewindows_response:
            entry['time_windows'] = {
                'request': timewindows_request,
                'response': {
                    'status_code': timewindows_response.status_code,
                    'headers': dict(timewindows_response.headers),
                    'body': timewindows_response.json() if timewindows_response.status_code == 200 else timewindows_response.text
                }
            }

        self.api_responses.append(entry)

        # Save to file immediately
        with open(self.api_responses_file, 'w', encoding='utf-8') as f:
            json.dump(self.api_responses, f, indent=2, ensure_ascii=False)

    def retry_on_network_error(self, func, *args, max_retries=3, delay=5, **kwargs):
        """Retry a function on network errors with exponential backoff"""
        for attempt in range(max_retries + 1):
            try:
                return func(*args, **kwargs)
            except (requests.exceptions.RequestException,
                    requests.exceptions.ConnectionError,
                    requests.exceptions.Timeout,
                    requests.exceptions.HTTPError) as e:

                if attempt == max_retries:
                    logger.error(f"Network error after {max_retries} retries: {e}")
                    raise e

                # Calculate delay with jitter to avoid thundering herd
                actual_delay = delay * (2 ** attempt) + random.uniform(0, 1)
                logger.warning(f"Network error (attempt {attempt + 1}/{max_retries + 1}): {e}")
                logger.info(f"Retrying in {actual_delay:.1f} seconds...")
                time.sleep(actual_delay)
            except Exception as e:
                # Non-network errors should not be retried
                logger.error(f"Non-network error (not retrying): {e}")
                raise e

    def test_single_zipcode(self, zipcode, item_id):
        """Test time-windows for a single zipcode"""
        result = {
            'zipcode': zipcode,
            'item_id': item_id,
            'timestamp': datetime.now().isoformat(),
            'delivery_success': False,
            'timewindows_success': False,
            'delivery_arrangement_id': None,
            'solution_id': None,
            'delivery_id': None,
            'delivery_error': None,
            'timewindows_error': None,
            'delivery_response_time': None,
            'timewindows_response_time': None,
            'has_time_slots': False,
            'available_dates': '',
            'time_windows': '',
            'tsp_names': '',
            'tsp_ids': '',
            'nodes': '',
            'capacity_available': '',
            'payment_cutoff_times': '',
            'resource_pool_ids': '',
            'time_window_ids': ''
        }

        try:
            # Step 1: Call delivery-arrangement API with retry
            start_time = datetime.now()
            delivery_response, delivery_request = self.retry_on_network_error(
                self.call_delivery_arrangement, zipcode, item_id
            )
            delivery_time = (datetime.now() - start_time).total_seconds()
            result['delivery_response_time'] = delivery_time

            if delivery_response.status_code != 200:
                result['delivery_error'] = f"HTTP {delivery_response.status_code}: {delivery_response.text}"
                self.save_api_response(zipcode, item_id, delivery_request, delivery_response)
                return result

            delivery_result = delivery_response.json()
            result['delivery_success'] = True

            # Extract delivery information
            delivery_arrangement_id, solution_id, delivery_id, error = self.extract_delivery_info(delivery_result)

            if error:
                result['delivery_error'] = error
                self.save_api_response(zipcode, item_id, delivery_request, delivery_response)
                return result

            result['delivery_arrangement_id'] = delivery_arrangement_id
            result['solution_id'] = solution_id
            result['delivery_id'] = delivery_id

            # Step 2: Call time-windows API with retry
            start_time = datetime.now()
            timewindows_response, timewindows_request = self.retry_on_network_error(
                self.call_time_windows, delivery_arrangement_id, solution_id, delivery_id
            )
            timewindows_time = (datetime.now() - start_time).total_seconds()
            result['timewindows_response_time'] = timewindows_time

            # Save API responses
            self.save_api_response(zipcode, item_id, delivery_request, delivery_response,
                                 timewindows_request, timewindows_response)

            if timewindows_response.status_code != 200:
                result['timewindows_error'] = f"HTTP {timewindows_response.status_code}: {timewindows_response.text}"
                return result

            timewindows_result = timewindows_response.json()

            # Check for API-level errors in response
            if 'error' in timewindows_result:
                error_info = timewindows_result['error']
                result['timewindows_error'] = f"{error_info.get('errorCode', 'UNKNOWN')}: {error_info.get('errorDescription', 'Unknown error')}"
                return result

            result['timewindows_success'] = True

            # Extract time window information
            timewindow_info = self.extract_timewindow_info(timewindows_result)
            result['has_time_slots'] = timewindow_info['has_slots']
            result['available_dates'] = '; '.join(timewindow_info['available_dates'])
            result['time_windows'] = '; '.join(timewindow_info['time_windows'])
            result['tsp_names'] = '; '.join(timewindow_info['tsp_names'])
            result['tsp_ids'] = '; '.join(timewindow_info['tsp_ids'])
            result['nodes'] = '; '.join(timewindow_info['nodes'])
            result['capacity_available'] = '; '.join(timewindow_info['capacity_available'])
            result['payment_cutoff_times'] = '; '.join(timewindow_info['payment_cutoff_times'])
            result['resource_pool_ids'] = '; '.join(timewindow_info['resource_pool_ids'])
            result['time_window_ids'] = '; '.join(timewindow_info['time_window_ids'])

        except Exception as e:
            result['delivery_error'] = f"Exception: {str(e)}"
            logger.error(f"Error testing zipcode {zipcode}: {e}")

        return result

    def run_batch_test(self, item_id='10534224', start_row=0, end_row=None, batch_size=100):
        """Run batch time-windows test"""
        logger.info(f"Starting time-windows batch test with item {item_id}")

        # Load input Excel file
        try:
            df = pd.read_excel(self.input_file, dtype={'ZipCode': str})
            # Ensure zipcodes are 6-character strings with leading zeros
            df['ZipCode'] = df['ZipCode'].astype(str).str.zfill(6)
            logger.info(f"Loaded input Excel file with {len(df)} rows")
            logger.info(f"Sample zipcodes: {df['ZipCode'].head(3).tolist()}")
        except Exception as e:
            logger.error(f"Failed to load input file: {e}")
            return False

        # Determine row range
        if end_row is None:
            end_row = len(df)

        end_row = min(end_row, len(df))
        total_rows = end_row - start_row

        # Update progress with test parameters
        # Note: total_rows should be the entire dataset, not just current batch
        dataset_total_rows = len(df)

        self.progress.update({
            'total_rows': dataset_total_rows,  # Total rows in entire dataset
            'current_batch_start': start_row,
            'current_batch_end': end_row,
            'current_batch_size': total_rows,  # Size of current batch
            'current_row': start_row,
            'batch_size': batch_size,
            'item_id': item_id
        })
        logger.info(f"Processing rows {start_row} to {end_row-1} (batch: {total_rows} rows, dataset: {dataset_total_rows} rows)")

        # Process in batches
        results = []

        for batch_start in range(start_row, end_row, batch_size):
            batch_end = min(batch_start + batch_size, end_row)
            batch_zipcodes = df.iloc[batch_start:batch_end]['ZipCode'].tolist()

            logger.info(f"Processing batch: rows {batch_start}-{batch_end-1} ({len(batch_zipcodes)} zipcodes)")

            # Process batch concurrently
            with ThreadPoolExecutor(max_workers=self.concurrency) as executor:
                future_to_zipcode = {
                    executor.submit(self.test_single_zipcode, zipcode, item_id): zipcode
                    for zipcode in batch_zipcodes
                }

                batch_results = []
                for future in as_completed(future_to_zipcode):
                    zipcode = future_to_zipcode[future]
                    try:
                        result = future.result()
                        batch_results.append(result)

                        # Update progress
                        with self.lock:
                            self.progress['completed_rows'] += 1
                            if result['delivery_success']:
                                self.progress['successful_delivery'] += 1
                            else:
                                self.progress['failed_delivery'] += 1

                            if result['timewindows_success']:
                                self.progress['successful_timewindows'] += 1
                            else:
                                self.progress['failed_timewindows'] += 1

                        # Log progress
                        status = "SUCCESS" if result['timewindows_success'] else "FAILED"
                        logger.info(f"Row {batch_start + len(batch_results) - 1}: {zipcode} - {status}")

                        if result['timewindows_success'] and result['has_time_slots']:
                            logger.info(f"  Time slots available: {result['available_dates']}")
                        elif result['timewindows_error']:
                            logger.info(f"  Error: {result['timewindows_error']}")

                    except Exception as e:
                        logger.error(f"Error processing zipcode {zipcode}: {e}")
                        batch_results.append({
                            'zipcode': zipcode,
                            'item_id': item_id,
                            'delivery_success': False,
                            'timewindows_success': False,
                            'delivery_error': f"Processing exception: {str(e)}"
                        })

            results.extend(batch_results)

            # Update progress with current status
            current_time = datetime.now()
            elapsed_time = (current_time - datetime.fromisoformat(self.progress['start_time'])).total_seconds()

            with self.lock:
                self.progress.update({
                    'current_row': batch_end,
                    'last_update_time': current_time.isoformat(),
                    'completion_percentage': (self.progress['completed_rows'] / self.progress['total_rows']) * 100 if self.progress['total_rows'] > 0 else 0,
                    'average_time_per_row': elapsed_time / self.progress['completed_rows'] if self.progress['completed_rows'] > 0 else None
                })

                # Calculate estimated remaining time
                if self.progress['average_time_per_row'] and self.progress['completed_rows'] > 0:
                    remaining_rows = self.progress['total_rows'] - self.progress['completed_rows']
                    estimated_seconds = remaining_rows * self.progress['average_time_per_row']
                    self.progress['estimated_remaining_time'] = f"{estimated_seconds/3600:.1f} hours" if estimated_seconds > 3600 else f"{estimated_seconds/60:.1f} minutes"

            # Save progress
            with open(self.progress_file, 'w') as f:
                json.dump(self.progress, f, indent=2)

            logger.info(f"Completed batch: rows {batch_start}-{batch_end-1}")

        # Save results to Excel
        self.save_results_to_excel(df, results, start_row, end_row)

        # Final summary
        logger.info(f"Batch test completed!")
        logger.info(f"Total processed: {self.progress['completed_rows']}")
        logger.info(f"Delivery success: {self.progress['successful_delivery']}")
        logger.info(f"TimeWindows success: {self.progress['successful_timewindows']}")
        logger.info(f"Results saved to: {self.excel_output}")

        return True

    def save_results_to_excel(self, original_df, results, start_row, end_row):
        """Save results to Excel file (incremental update)"""
        logger.info("Saving results to Excel...")

        # Check if Excel file already exists
        if os.path.exists(self.excel_output):
            # Load existing results
            try:
                existing_df = pd.read_excel(self.excel_output)
                logger.info(f"Loaded existing Excel with {len(existing_df)} rows")
            except Exception as e:
                logger.warning(f"Could not load existing Excel: {e}, creating new one")
                existing_df = None
        else:
            existing_df = None

        # Create dataframe for current batch
        current_df = original_df.iloc[start_row:end_row].copy().reset_index(drop=True)

        # Define result columns
        result_columns = [
            'tw_delivery_success', 'tw_timewindows_success', 'tw_delivery_arrangement_id',
            'tw_solution_id', 'tw_delivery_id', 'tw_delivery_error', 'tw_timewindows_error',
            'tw_delivery_response_time', 'tw_timewindows_response_time', 'tw_has_time_slots',
            'tw_available_dates', 'tw_time_windows', 'tw_tsp_names', 'tw_tsp_ids',
            'tw_nodes', 'tw_capacity_available', 'tw_payment_cutoff_times',
            'tw_resource_pool_ids', 'tw_time_window_ids', 'tw_timestamp'
        ]

        if existing_df is not None:
            # Update existing dataframe
            df = existing_df.copy()

            # Ensure all result columns exist
            for col in result_columns:
                if col not in df.columns:
                    df[col] = None

            # Update rows with new results
            for i, result in enumerate(results):
                row_index = start_row + i
                if row_index < len(df):
                    df.at[row_index, 'tw_delivery_success'] = result.get('delivery_success', False)
                    df.at[row_index, 'tw_timewindows_success'] = result.get('timewindows_success', False)
                    df.at[row_index, 'tw_delivery_arrangement_id'] = result.get('delivery_arrangement_id')
                    df.at[row_index, 'tw_solution_id'] = result.get('solution_id')
                    df.at[row_index, 'tw_delivery_id'] = result.get('delivery_id')
                    df.at[row_index, 'tw_delivery_error'] = result.get('delivery_error')
                    df.at[row_index, 'tw_timewindows_error'] = result.get('timewindows_error')
                    df.at[row_index, 'tw_delivery_response_time'] = result.get('delivery_response_time')
                    df.at[row_index, 'tw_timewindows_response_time'] = result.get('timewindows_response_time')
                    df.at[row_index, 'tw_has_time_slots'] = result.get('has_time_slots', False)
                    df.at[row_index, 'tw_available_dates'] = result.get('available_dates', '')
                    df.at[row_index, 'tw_time_windows'] = result.get('time_windows', '')
                    df.at[row_index, 'tw_tsp_names'] = result.get('tsp_names', '')
                    df.at[row_index, 'tw_tsp_ids'] = result.get('tsp_ids', '')
                    df.at[row_index, 'tw_nodes'] = result.get('nodes', '')
                    df.at[row_index, 'tw_capacity_available'] = result.get('capacity_available', '')
                    df.at[row_index, 'tw_payment_cutoff_times'] = result.get('payment_cutoff_times', '')
                    df.at[row_index, 'tw_resource_pool_ids'] = result.get('resource_pool_ids', '')
                    df.at[row_index, 'tw_time_window_ids'] = result.get('time_window_ids', '')
                    df.at[row_index, 'tw_timestamp'] = result.get('timestamp')
        else:
            # Create new dataframe from original data
            df = original_df.copy()

            # Add result columns
            for col in result_columns:
                df[col] = None

            # Fill in results for current batch only
            for i, result in enumerate(results):
                row_index = start_row + i
                if row_index < len(df):
                    df.at[row_index, 'tw_delivery_success'] = result.get('delivery_success', False)
                    df.at[row_index, 'tw_timewindows_success'] = result.get('timewindows_success', False)
                    df.at[row_index, 'tw_delivery_arrangement_id'] = result.get('delivery_arrangement_id')
                    df.at[row_index, 'tw_solution_id'] = result.get('solution_id')
                    df.at[row_index, 'tw_delivery_id'] = result.get('delivery_id')
                    df.at[row_index, 'tw_delivery_error'] = result.get('delivery_error')
                    df.at[row_index, 'tw_timewindows_error'] = result.get('timewindows_error')
                    df.at[row_index, 'tw_delivery_response_time'] = result.get('delivery_response_time')
                    df.at[row_index, 'tw_timewindows_response_time'] = result.get('timewindows_response_time')
                    df.at[row_index, 'tw_has_time_slots'] = result.get('has_time_slots', False)
                    df.at[row_index, 'tw_available_dates'] = result.get('available_dates', '')
                    df.at[row_index, 'tw_time_windows'] = result.get('time_windows', '')
                    df.at[row_index, 'tw_tsp_names'] = result.get('tsp_names', '')
                    df.at[row_index, 'tw_tsp_ids'] = result.get('tsp_ids', '')
                    df.at[row_index, 'tw_nodes'] = result.get('nodes', '')
                    df.at[row_index, 'tw_capacity_available'] = result.get('capacity_available', '')
                    df.at[row_index, 'tw_payment_cutoff_times'] = result.get('payment_cutoff_times', '')
                    df.at[row_index, 'tw_resource_pool_ids'] = result.get('resource_pool_ids', '')
                    df.at[row_index, 'tw_time_window_ids'] = result.get('time_window_ids', '')
                    df.at[row_index, 'tw_timestamp'] = result.get('timestamp')

        # Save to Excel
        df.to_excel(self.excel_output, index=False)
        logger.info(f"Results saved to {self.excel_output} (updated {len(results)} rows)")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Time Windows API Batch Tester')
    parser.add_argument('--item-id', default='10534224', help='Item ID to test (default: 10534224 for parcel)')
    parser.add_argument('--start-row', type=int, default=0, help='Start row index')
    parser.add_argument('--end-row', type=int, help='End row index')
    parser.add_argument('--batch-size', type=int, default=100, help='Batch size')
    parser.add_argument('--concurrency', type=int, default=15, help='Concurrency level')
    parser.add_argument('--output-dir', help='Use existing test directory (e.g., results/timewindows_test_20250805_170740)')

    args = parser.parse_args()

    # Override concurrency if specified
    if args.concurrency:
        os.environ['CONCURRENCY'] = str(args.concurrency)

    tester = TimeWindowsTester(output_dir=args.output_dir)

    logger.info(f"Starting time-windows batch test")
    logger.info(f"Item ID: {args.item_id}")
    logger.info(f"Concurrency: {args.concurrency}")

    success = tester.run_batch_test(
        item_id=args.item_id,
        start_row=args.start_row,
        end_row=args.end_row,
        batch_size=args.batch_size
    )

    if success:
        logger.info("Time-windows batch test completed successfully!")
    else:
        logger.error("Time-windows batch test failed!")
        sys.exit(1)

if __name__ == '__main__':
    main()
