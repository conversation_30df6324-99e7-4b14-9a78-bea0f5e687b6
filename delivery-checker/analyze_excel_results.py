#!/usr/bin/env python3
"""
Analyze Excel results to understand why many rows are without results
"""

import pandas as pd
import os
import sys

def analyze_excel_file(excel_path):
    """Analyze a single Excel file"""
    if not os.path.exists(excel_path):
        print(f"❌ File not found: {excel_path}")
        return
    
    print(f"\n{'='*60}")
    print(f"📊 ANALYZING: {excel_path}")
    print(f"{'='*60}")
    
    try:
        # Load the Excel file
        df = pd.read_excel(excel_path)
        print(f"📋 Total rows: {len(df):,}")
        print(f"📋 Total columns: {len(df.columns)}")
        
        # Show all columns
        print(f"\n📋 All columns:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1:2d}. {col}")
        
        # Find result columns (those starting with 'tw_')
        result_cols = [col for col in df.columns if col.startswith('tw_')]
        print(f"\n🎯 Result columns ({len(result_cols)}):")
        for col in result_cols:
            print(f"  - {col}")
        
        if not result_cols:
            print("⚠️  No result columns found!")
            return
        
        # Analyze each result column
        print(f"\n📊 RESULT COLUMN ANALYSIS:")
        for col in result_cols:
            total_values = len(df)
            non_null_values = df[col].notna().sum()
            non_empty_values = (df[col].notna() & (df[col] != '') & (df[col] != 'None')).sum()
            
            print(f"\n  🔍 {col}:")
            print(f"    Total rows:     {total_values:,}")
            print(f"    Non-null:       {non_null_values:,} ({non_null_values/total_values*100:.1f}%)")
            print(f"    Non-empty:      {non_empty_values:,} ({non_empty_values/total_values*100:.1f}%)")
            print(f"    Empty/null:     {total_values-non_empty_values:,} ({(total_values-non_empty_values)/total_values*100:.1f}%)")
        
        # Show sample data
        print(f"\n📋 SAMPLE DATA (first 10 rows):")
        print("-" * 80)
        for i in range(min(10, len(df))):
            row = df.iloc[i]
            zipcode = row.get('zipcode', 'N/A')
            
            # Check if row has any results
            has_results = False
            result_summary = []
            for col in result_cols[:3]:  # Check first 3 result columns
                value = row.get(col)
                if pd.notna(value) and value != '' and value != 'None':
                    has_results = True
                    result_summary.append(f"{col}={value}")
            
            status = "✅ HAS RESULTS" if has_results else "❌ NO RESULTS"
            print(f"  Row {i:2d}: zipcode={zipcode} | {status}")
            if result_summary:
                print(f"         {' | '.join(result_summary)}")
        
        # Count rows with and without results
        rows_with_results = 0
        rows_without_results = 0
        
        for i in range(len(df)):
            row = df.iloc[i]
            has_results = any(
                pd.notna(row.get(col)) and row.get(col) != '' and row.get(col) != 'None' 
                for col in result_cols
            )
            if has_results:
                rows_with_results += 1
            else:
                rows_without_results += 1
        
        print(f"\n📊 OVERALL SUMMARY:")
        print(f"  ✅ Rows WITH results:    {rows_with_results:,} ({rows_with_results/len(df)*100:.1f}%)")
        print(f"  ❌ Rows WITHOUT results: {rows_without_results:,} ({rows_without_results/len(df)*100:.1f}%)")
        
        # Show some rows without results
        print(f"\n❌ SAMPLE ROWS WITHOUT RESULTS:")
        print("-" * 50)
        count = 0
        for i in range(len(df)):
            if count >= 5:  # Show only first 5
                break
            row = df.iloc[i]
            has_results = any(
                pd.notna(row.get(col)) and row.get(col) != '' and row.get(col) != 'None' 
                for col in result_cols
            )
            if not has_results:
                zipcode = row.get('zipcode', 'N/A')
                print(f"  Row {i:2d}: zipcode={zipcode}")
                count += 1
        
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")

def main():
    """Main function"""
    print("🔍 EXCEL RESULTS ANALYZER")
    print("=" * 60)
    
    # Files to analyze
    files_to_check = [
        'results/timewindows_test_20250805_175316/timewindows_test_results_20250805_175316.xlsx',  # Truck test
        'results/timewindows_test_20250805_170740/timewindows_test_results_20250805_170740.xlsx'   # Package test
    ]
    
    for excel_file in files_to_check:
        analyze_excel_file(excel_file)
    
    print(f"\n{'='*60}")
    print("🏁 ANALYSIS COMPLETE")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
