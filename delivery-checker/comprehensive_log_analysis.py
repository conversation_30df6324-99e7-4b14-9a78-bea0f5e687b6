#!/usr/bin/env python3
"""
Comprehensive analysis of all log files using command line tools for accuracy
"""

import subprocess
import os
import glob
import re

def get_item_id_from_log(log_file):
    """Extract item ID from log file using grep"""
    try:
        result = subprocess.run(['grep', 'Item ID:', log_file], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and result.stdout:
            match = re.search(r'Item ID:\s*(\d+)', result.stdout)
            if match:
                return match.group(1)
    except:
        pass
    return None

def analyze_log_with_commands(log_file):
    """Analyze log file using command line tools for accuracy"""
    result = {
        'file': os.path.basename(log_file),
        'size_mb': os.path.getsize(log_file) / (1024 * 1024),
        'total_lines': 0,
        'item_id': None,
        'item_type': 'Unknown',
        'total_tests': 0,
        'successful_tests': 0,
        'failed_tests': 0,
        'unique_zipcodes': 0,
        'time_slots_available': 0
    }
    
    try:
        # Get total lines
        wc_result = subprocess.run(['wc', '-l', log_file], 
                                 capture_output=True, text=True, timeout=10)
        if wc_result.returncode == 0:
            result['total_lines'] = int(wc_result.stdout.split()[0])
        
        # Get item ID
        result['item_id'] = get_item_id_from_log(log_file)
        if result['item_id'] == '10534224':
            result['item_type'] = '📦 Parcel'
        elif result['item_id'] == '70570836':
            result['item_type'] = '🚛 Truck'
        
        # Count successful tests
        success_result = subprocess.run(['grep', '-c', 'Row.*: .* - SUCCESS', log_file], 
                                      capture_output=True, text=True, timeout=15)
        if success_result.returncode == 0:
            result['successful_tests'] = int(success_result.stdout.strip())
        
        # Count failed tests
        failed_result = subprocess.run(['grep', '-c', 'Row.*: .* - FAILED', log_file], 
                                     capture_output=True, text=True, timeout=15)
        if failed_result.returncode == 0:
            result['failed_tests'] = int(failed_result.stdout.strip())
        
        result['total_tests'] = result['successful_tests'] + result['failed_tests']
        
        # Count unique zipcodes
        unique_cmd = f"grep 'Row.*: .* - ' {log_file} | sed 's/.*Row [0-9]*: \\([0-9]*\\) - .*/\\1/' | sort -u | wc -l"
        unique_result = subprocess.run(unique_cmd, shell=True, 
                                     capture_output=True, text=True, timeout=20)
        if unique_result.returncode == 0:
            result['unique_zipcodes'] = int(unique_result.stdout.strip())
        
        # Count time slots available
        slots_result = subprocess.run(['grep', '-c', 'Time slots available:', log_file], 
                                    capture_output=True, text=True, timeout=15)
        if slots_result.returncode == 0:
            result['time_slots_available'] = int(slots_result.stdout.strip())
        
    except Exception as e:
        print(f"  ❌ Error analyzing {log_file}: {e}")
    
    return result

def main():
    """Main function"""
    print("🔍 COMPREHENSIVE LOG FILE ANALYSIS USING COMMAND LINE TOOLS")
    print("=" * 80)
    
    # Find all log files
    log_files = glob.glob("results/timewindows*/timewindows_test_*.log")
    log_files.sort()
    
    print(f"📄 Found {len(log_files)} log files")
    
    # Analyze each log file
    parcel_results = []
    truck_results = []
    unknown_results = []
    
    print(f"\n📊 ANALYZING LOG FILES:")
    
    for log_file in log_files:
        print(f"\n📁 {os.path.basename(log_file)}")
        print(f"  📏 Size: {os.path.getsize(log_file) / (1024 * 1024):.1f} MB")
        
        result = analyze_log_with_commands(log_file)
        
        print(f"  📋 Total lines: {result['total_lines']:,}")
        print(f"  🏷️  Item ID: {result['item_id']} ({result['item_type']})")
        print(f"  📊 Total tests: {result['total_tests']:,}")
        print(f"  ✅ Successful: {result['successful_tests']:,}")
        print(f"  ❌ Failed: {result['failed_tests']:,}")
        print(f"  📍 Unique zipcodes: {result['unique_zipcodes']:,}")
        print(f"  ⏰ Time slots available: {result['time_slots_available']:,}")
        
        if result['total_tests'] > 0:
            success_rate = result['successful_tests'] / result['total_tests'] * 100
            print(f"  📈 Success rate: {success_rate:.1f}%")
        
        # Categorize results
        if result['item_id'] == '10534224':
            parcel_results.append(result)
        elif result['item_id'] == '70570836':
            truck_results.append(result)
        else:
            unknown_results.append(result)
    
    # Summary statistics
    print(f"\n{'='*80}")
    print(f"📊 COMPREHENSIVE SUMMARY:")
    
    def summarize_results(results, category_name):
        if not results:
            print(f"  {category_name}: No data found")
            return
        
        total_tests = sum(r['total_tests'] for r in results)
        total_successful = sum(r['successful_tests'] for r in results)
        total_failed = sum(r['failed_tests'] for r in results)
        total_unique_zipcodes = sum(r['unique_zipcodes'] for r in results)
        total_time_slots = sum(r['time_slots_available'] for r in results)
        total_size_mb = sum(r['size_mb'] for r in results)
        
        print(f"  {category_name}:")
        print(f"    📄 Log files: {len(results)}")
        print(f"    📏 Total size: {total_size_mb:.1f} MB")
        print(f"    📊 Total tests: {total_tests:,}")
        print(f"    ✅ Successful tests: {total_successful:,}")
        print(f"    ❌ Failed tests: {total_failed:,}")
        print(f"    📍 Total unique zipcodes: {total_unique_zipcodes:,}")
        print(f"    ⏰ Time slots available: {total_time_slots:,}")
        
        if total_tests > 0:
            success_rate = total_successful / total_tests * 100
            print(f"    📈 Overall success rate: {success_rate:.1f}%")
        
        # Show breakdown by file
        print(f"    📋 Breakdown by file:")
        for r in sorted(results, key=lambda x: x['total_tests'], reverse=True):
            if r['total_tests'] > 0:
                print(f"      {r['file']}: {r['total_tests']:,} tests, {r['unique_zipcodes']:,} unique zipcodes")
    
    summarize_results(parcel_results, "📦 PARCEL TESTS")
    summarize_results(truck_results, "🚛 TRUCK TESTS")
    summarize_results(unknown_results, "❓ UNKNOWN TESTS")
    
    # Grand totals
    all_results = parcel_results + truck_results + unknown_results
    if all_results:
        grand_total_tests = sum(r['total_tests'] for r in all_results)
        grand_total_successful = sum(r['successful_tests'] for r in all_results)
        grand_total_unique_zipcodes = sum(r['unique_zipcodes'] for r in all_results)
        
        print(f"\n🎯 GRAND TOTALS:")
        print(f"  📊 Total tests across all files: {grand_total_tests:,}")
        print(f"  ✅ Total successful tests: {grand_total_successful:,}")
        print(f"  📍 Total unique zipcodes tested: {grand_total_unique_zipcodes:,}")
        print(f"  📈 Overall success rate: {grand_total_successful/grand_total_tests*100:.1f}%")
    
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
