#!/usr/bin/env python3
"""
Count API responses in all JSON files using jq command line tool
"""

import subprocess
import glob
import os
import json

def count_responses_with_jq(json_file):
    """Count responses in a JSON file using jq"""
    try:
        # Use jq to count array length
        result = subprocess.run(['jq', 'length', json_file], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            return int(result.stdout.strip())
        else:
            print(f"  ❌ jq error for {json_file}: {result.stderr}")
            return 0
    except subprocess.TimeoutExpired:
        print(f"  ⏰ Timeout counting {json_file}")
        return 0
    except Exception as e:
        print(f"  ❌ Error counting {json_file}: {e}")
        return 0

def get_item_id_from_file(json_file):
    """Try to get item ID from the directory structure"""
    dir_path = os.path.dirname(json_file)
    timestamp = os.path.basename(dir_path).replace('timewindows_test_', '')
    
    # Try progress file first
    progress_file = os.path.join(dir_path, f"progress_{timestamp}.json")
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r') as f:
                progress_data = json.load(f)
                return str(progress_data.get('item_id', ''))
        except:
            pass
    
    # Try log file
    log_file = os.path.join(dir_path, f"timewindows_test_{timestamp}.log")
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r') as f:
                for line in f:
                    if 'Item ID:' in line:
                        parts = line.split('Item ID:')
                        if len(parts) > 1:
                            return parts[1].strip().split()[0]
        except:
            pass
    
    return "Unknown"

def main():
    """Main function"""
    print("🔍 COUNTING API RESPONSES USING JQ")
    print("=" * 60)
    
    # Find all API response files
    api_files = glob.glob("results/timewindows*/api*.json")
    api_files.sort()
    
    print(f"📄 Found {len(api_files)} API response files")
    
    total_responses = 0
    parcel_responses = 0
    truck_responses = 0
    
    print(f"\n📊 COUNTING RESPONSES:")
    
    for api_file in api_files:
        print(f"\n📁 {api_file}")
        
        # Get file size
        file_size = os.path.getsize(api_file)
        size_mb = file_size / (1024 * 1024)
        print(f"  📏 Size: {size_mb:.1f} MB")
        
        # Get item ID
        item_id = get_item_id_from_file(api_file)
        item_type = "📦 Parcel" if item_id == "10534224" else "🚛 Truck" if item_id == "70570836" else "❓ Unknown"
        print(f"  🏷️  Item ID: {item_id} ({item_type})")
        
        # Count responses using jq
        count = count_responses_with_jq(api_file)
        print(f"  📊 Responses: {count:,}")
        
        total_responses += count
        if item_id == "10534224":
            parcel_responses += count
        elif item_id == "70570836":
            truck_responses += count
    
    print(f"\n{'='*60}")
    print(f"📊 TOTAL SUMMARY:")
    print(f"  📦 Total Parcel responses: {parcel_responses:,}")
    print(f"  🚛 Total Truck responses: {truck_responses:,}")
    print(f"  📊 Grand Total responses: {total_responses:,}")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
