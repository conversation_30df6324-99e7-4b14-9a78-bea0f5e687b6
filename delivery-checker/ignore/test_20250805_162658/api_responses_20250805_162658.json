[{"zipcode": "572003", "timestamp": "2025-08-05T16:47:18.941952", "success": true, "status_code": 200, "response_time": 0.227278, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "572003"}, "deliveryArrangementsId": "20250805084718846925966", "itemLines": {"itemLine": [{"unitWeight": "23.40", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.1446", "id": "1", "itemNo": "00588793", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~4"}, {"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR40000606", "transportMethodType": "TRUCK", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.2055", "unitOfMeasure": "ORDERS", "shipNode": "CDC.045", "mergeNode": "LSC.2055"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T08:47:18"}, {"service": "CURBSIDE", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR90000897", "transportMethodType": "TRUCK", "deliveryId": "HD~~~4", "mergeNodeList": "LSC.2055", "unitOfMeasure": "ORDERS", "shipNode": "CDC.045", "mergeNode": "LSC.2055"}]}, "id": "HD~1~CURBSIDE", "earliestShipDate": "2025-08-05T08:47:18"}]}}]}, "checkInventory": "true"}, "delivery_arrangement_id": "20250805084718846925966", "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "591", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1123", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96887222", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1123", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 08:47:18 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "f0cb96d2-5b41-4a5d-8386-40422858164f", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}]