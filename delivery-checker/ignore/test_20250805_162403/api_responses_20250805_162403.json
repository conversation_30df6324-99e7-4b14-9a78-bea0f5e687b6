[{"zipcode": "638010", "timestamp": "2025-08-05T16:26:06.738167", "success": true, "status_code": 200, "response_time": 0.515378, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "638010"}, "deliveryArrangementsId": "20250805082606520138058", "itemLines": {"itemLine": [{"unitWeight": "0.076", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0005", "id": "1", "itemNo": "60561007", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~4"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~4", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T08:26:06"}]}}]}, "checkInventory": "true"}, "delivery_arrangement_id": "20250805082606520138058", "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1157", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96916410", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1157", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 08:26:06 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "3ea41099-8174-4b08-9ff5-b4551ccbf494", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "638005", "timestamp": "2025-08-05T16:26:06.979103", "success": true, "status_code": 200, "response_time": 0.395044, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "638005"}, "deliveryArrangementsId": "20250805082606800480441", "itemLines": {"itemLine": [{"unitWeight": "0.076", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0005", "id": "1", "itemNo": "60561007", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T08:26:06"}]}}]}, "checkInventory": "true"}, "delivery_arrangement_id": "20250805082606800480441", "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1139", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96916229", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1139", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 08:26:06 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "1a49500d-84b0-48fb-9a62-35c5d05fd914", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "638003", "timestamp": "2025-08-05T16:26:06.979966", "success": true, "status_code": 200, "response_time": 0.403912, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "638003"}, "deliveryArrangementsId": "20250805082606795664277", "itemLines": {"itemLine": [{"unitWeight": "0.076", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0005", "id": "1", "itemNo": "60561007", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T08:26:06"}]}}]}, "checkInventory": "true"}, "delivery_arrangement_id": "20250805082606795664277", "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1155", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96916408", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1155", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 08:26:06 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "980984c0-907e-4716-8f52-d5a3240a7837", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "638016", "timestamp": "2025-08-05T16:26:07.081367", "success": true, "status_code": 200, "response_time": 0.501125, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "638016"}, "deliveryArrangementsId": "20250805082606800399426", "itemLines": {"itemLine": [{"unitWeight": "0.076", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0005", "id": "1", "itemNo": "60561007", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T08:26:06"}]}}]}, "checkInventory": "true"}, "delivery_arrangement_id": "20250805082606800399426", "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1153", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96916406", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1153", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 08:26:06 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "7f7f75ef-0269-4c77-8a7a-19666e6d1b88", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "638012", "timestamp": "2025-08-05T16:26:07.081720", "success": true, "status_code": 200, "response_time": 0.509712, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "638012"}, "deliveryArrangementsId": "20250805082606797566943", "itemLines": {"itemLine": [{"unitWeight": "0.076", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0005", "id": "1", "itemNo": "60561007", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~4"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~4", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T08:26:06"}]}}]}, "checkInventory": "true"}, "delivery_arrangement_id": "20250805082606797566943", "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "550", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1140", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96916230", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1140", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 08:26:06 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "ace133cd-bec2-45dc-ba6f-8e43d4368053", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}]