[{"zipcode": "525385", "timestamp": "2025-08-05T15:30:53.615656", "success": true, "status_code": 200, "response_time": 0.372661, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "525385"}, "deliveryArrangementsId": "20250805073053440725703", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "543", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1193", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923421", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1193", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "464647ae-7e4b-4988-b148-308c4ab5f0d7", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "044288", "timestamp": "2025-08-05T15:30:53.646457", "success": true, "status_code": 200, "response_time": 0.43017, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "044288"}, "deliveryArrangementsId": "20250805073053434096498", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1194", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923422", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1194", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "c858a587-5909-495c-8052-9c571b7a9b94", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "525384", "timestamp": "2025-08-05T15:30:53.657436", "success": true, "status_code": 200, "response_time": 0.41216, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "525384"}, "deliveryArrangementsId": "20250805073053451954458", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1192", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923420", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1192", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "794fd724-4fce-44f4-b14e-18d27264b662", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "251569", "timestamp": "2025-08-05T15:30:53.660261", "success": true, "status_code": 200, "response_time": 0.414047, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "251569"}, "deliveryArrangementsId": "20250805073053455331479", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1190", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923418", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1190", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "74ad1a5f-4f19-436a-abc1-5334cd25ce03", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "252046", "timestamp": "2025-08-05T15:30:53.667165", "success": true, "status_code": 200, "response_time": 0.422844, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "252046"}, "deliveryArrangementsId": "20250805073053440199280", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "544", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1192", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924443", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1192", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "da8d426f-e211-49d3-9495-ce628dd2e5c3", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "044201", "timestamp": "2025-08-05T15:30:53.702393", "success": true, "status_code": 200, "response_time": 0.460776, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "044201"}, "deliveryArrangementsId": "20250805073053444889168", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1191", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923419", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1191", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "776bf888-972b-41fd-9d39-ee0a1ee6e3b3", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "044202", "timestamp": "2025-08-05T15:30:53.710096", "success": true, "status_code": 200, "response_time": 0.492721, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "044202"}, "deliveryArrangementsId": "20250805073053451564423", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "543", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1193", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924444", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1193", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "19309e6f-eeae-473b-9fa8-2d821e3bce14", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "044325", "timestamp": "2025-08-05T15:30:53.725449", "success": true, "status_code": 200, "response_time": 0.510804, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "044325"}, "deliveryArrangementsId": "20250805073053434893119", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1194", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924445", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1194", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "8e5600a7-2f65-476c-a917-16440dcbe5d7", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "252044", "timestamp": "2025-08-05T15:30:53.736940", "success": true, "status_code": 200, "response_time": 0.490246, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "252044"}, "deliveryArrangementsId": "20250805073053456788617", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1190", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924441", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1190", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "26d460ca-e4f2-4213-8205-f79d78a76190", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "335448", "timestamp": "2025-08-05T15:30:53.738782", "success": true, "status_code": 200, "response_time": 0.49407, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "335448"}, "deliveryArrangementsId": "20250805073053443556530", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1191", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924442", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1191", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "a0b5a31e-5e8c-418b-ab6d-4154f54c30c2", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "251580", "timestamp": "2025-08-05T15:30:54.065392", "success": true, "status_code": 200, "response_time": 0.416373, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "251580"}, "deliveryArrangementsId": "20250805073053841291521", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "544", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1187", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923415", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1187", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "41458f14-66a4-4144-b9a3-8a3bf2896383", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "123009", "timestamp": "2025-08-05T15:30:54.083011", "success": true, "status_code": 200, "response_time": 0.4139, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123009"}, "deliveryArrangementsId": "20250805073053851599765", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1186", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923414", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1186", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "9b5b5399-9fb0-4f88-bf04-bf6abf99f9b3", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "251571", "timestamp": "2025-08-05T15:30:54.086320", "success": true, "status_code": 200, "response_time": 0.46331, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "251571"}, "deliveryArrangementsId": "20250805073053810648248", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1188", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923416", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1188", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "9aa9c6dc-0c4a-4c3a-8497-5b3b498a3bbe", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "123008", "timestamp": "2025-08-05T15:30:54.089469", "success": true, "status_code": 200, "response_time": 0.424821, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123008"}, "deliveryArrangementsId": "20250805073053853940226", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1185", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923413", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1185", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "2247963c-eb05-4d4a-941a-7d29f1e8615a", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "123007", "timestamp": "2025-08-05T15:30:54.092065", "success": true, "status_code": 200, "response_time": 0.430819, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123007"}, "deliveryArrangementsId": "20250805073053861100475", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1189", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924440", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1189", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "1afdcbcb-2a37-4f73-b0f7-d6e017d15175", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "123010", "timestamp": "2025-08-05T15:30:54.113090", "success": true, "status_code": 200, "response_time": 0.409093, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123010"}, "deliveryArrangementsId": "20250805073053894291748", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:53"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1188", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924439", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1188", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "81f224c3-9864-4b0f-92ba-0450211ed7ff", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "123016", "timestamp": "2025-08-05T15:30:54.127735", "success": true, "status_code": 200, "response_time": 0.400641, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123016"}, "deliveryArrangementsId": "20250805073053915190472", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1184", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923412", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1184", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "790d16e9-4c1f-476d-8027-44dc69a2711b", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "123014", "timestamp": "2025-08-05T15:30:54.138786", "success": true, "status_code": 200, "response_time": 0.396569, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123014"}, "deliveryArrangementsId": "20250805073053924822458", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "544", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1185", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924436", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1185", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "63a9da5f-aee5-48cf-a8a3-596964406666", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "123011", "timestamp": "2025-08-05T15:30:54.174633", "success": true, "status_code": 200, "response_time": 0.462716, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123011"}, "deliveryArrangementsId": "20250805073053904649080", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "544", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1187", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924438", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1187", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "6000153d-00f5-4f6f-83ff-231069e93d2e", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "123015", "timestamp": "2025-08-05T15:30:54.186516", "success": true, "status_code": 200, "response_time": 0.44779, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123015"}, "deliveryArrangementsId": "20250805073053927266783", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1186", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924437", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1186", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:53 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "57978479-3121-4474-96e8-67909f7635c7", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "123026", "timestamp": "2025-08-05T15:30:54.604080", "success": true, "status_code": 200, "response_time": 0.40576, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123026"}, "deliveryArrangementsId": "20250805073054396000418", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1181", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924433", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1181", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "7c919508-1a6a-4460-a5b9-69bbad448cce", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "123034", "timestamp": "2025-08-05T15:30:54.629419", "success": true, "status_code": 200, "response_time": 0.427207, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123034"}, "deliveryArrangementsId": "20250805073054401402399", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1179", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924431", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1179", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "ac0dbe8e-eda8-440a-999e-2f43f60d1d6c", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "123035", "timestamp": "2025-08-05T15:30:54.643998", "success": true, "status_code": 200, "response_time": 0.442774, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123035"}, "deliveryArrangementsId": "20250805073054397158937", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1182", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923411", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1182", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "a2575901-2727-41a8-995e-2c089d32777c", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "112006", "timestamp": "2025-08-05T15:30:54.646953", "success": true, "status_code": 200, "response_time": 0.441546, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "112006"}, "deliveryArrangementsId": "20250805073054399398013", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1180", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923409", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1180", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "7a742045-8eca-447f-8512-41a0ddb104ce", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "123019", "timestamp": "2025-08-05T15:30:54.647862", "success": true, "status_code": 200, "response_time": 0.450399, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123019"}, "deliveryArrangementsId": "20250805073054397463103", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1180", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924432", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1180", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "694ae71f-e5ca-4487-bdc9-8faedd6d6691", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "123023", "timestamp": "2025-08-05T15:30:54.652532", "success": true, "status_code": 200, "response_time": 0.452611, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123023"}, "deliveryArrangementsId": "20250805073054395794249", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1183", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923412", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1183", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "2379ae67-1b07-4d5b-9b4a-c8a3b2eae249", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "123020", "timestamp": "2025-08-05T15:30:54.656301", "success": true, "status_code": 200, "response_time": 0.454621, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123020"}, "deliveryArrangementsId": "20250805073054395742028", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "543", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1183", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924435", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1183", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "c7f03cca-b137-4da4-a86f-9897f3c58287", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "123021", "timestamp": "2025-08-05T15:30:54.656508", "success": true, "status_code": 200, "response_time": 0.454423, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123021"}, "deliveryArrangementsId": "20250805073054396550064", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1184", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923413", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1184", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "41faadc4-a863-4ad7-ae25-ad9be5a86a99", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "123030", "timestamp": "2025-08-05T15:30:54.660157", "success": true, "status_code": 200, "response_time": 0.458527, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123030"}, "deliveryArrangementsId": "20250805073054397665753", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1181", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923410", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1181", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "e0399da2-8277-47a3-93a4-358c4697396d", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "123018", "timestamp": "2025-08-05T15:30:54.686359", "success": true, "status_code": 200, "response_time": 0.485889, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "123018"}, "deliveryArrangementsId": "20250805073054398527121", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1182", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924434", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1182", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "9145f3ae-fb4f-4854-9de1-7b145237217a", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "112005", "timestamp": "2025-08-05T15:30:55.042005", "success": true, "status_code": 200, "response_time": 0.435059, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "112005"}, "deliveryArrangementsId": "20250805073054797090165", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1178", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924430", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1178", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "fe550f41-5d69-4106-9bff-775ed83b4160", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "111008", "timestamp": "2025-08-05T15:30:55.053050", "success": true, "status_code": 200, "response_time": 0.390929, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "111008"}, "deliveryArrangementsId": "20250805073054852886507", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1178", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923407", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1178", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "767027e7-115e-4b2e-b134-e5b7352d6297", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "112004", "timestamp": "2025-08-05T15:30:55.058824", "success": true, "status_code": 200, "response_time": 0.412167, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "112004"}, "deliveryArrangementsId": "20250805073054840211241", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "543", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1179", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923408", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1179", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "1b6bac88-ca51-40c7-ba5f-37f09e0b7171", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "112001", "timestamp": "2025-08-05T15:30:55.091758", "success": true, "status_code": 200, "response_time": 0.459658, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "112001"}, "deliveryArrangementsId": "20250805073054833395339", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1177", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924429", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1177", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "4a13e32b-bf39-436a-ac95-990af4e8ff09", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "112008", "timestamp": "2025-08-05T15:30:55.094859", "success": true, "status_code": 200, "response_time": 0.438714, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "112008"}, "deliveryArrangementsId": "20250805073054844127444", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1176", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924428", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1176", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "cdc6d19e-2eb5-4b2f-879f-97cccc15b15a", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "111003", "timestamp": "2025-08-05T15:30:55.098102", "success": true, "status_code": 200, "response_time": 0.405762, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "111003"}, "deliveryArrangementsId": "20250805073054881724300", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1173", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924425", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1173", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "04a1fba0-ed35-49d3-b578-4875c33c4ab9", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "111007", "timestamp": "2025-08-05T15:30:55.103254", "success": true, "status_code": 200, "response_time": 0.443457, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "111007"}, "deliveryArrangementsId": "20250805073054853478439", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1175", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924427", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1175", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "2b03c459-9fe6-4c12-ab2b-9aa9037a871c", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "112002", "timestamp": "2025-08-05T15:30:55.107130", "success": true, "status_code": 200, "response_time": 0.455118, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "112002"}, "deliveryArrangementsId": "20250805073054852990567", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1174", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924426", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1174", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "47f80283-93af-4aec-9a04-37f340d302be", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "112003", "timestamp": "2025-08-05T15:30:55.117724", "success": true, "status_code": 200, "response_time": 0.46633, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "112003"}, "deliveryArrangementsId": "20250805073054851945704", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1177", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923406", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1177", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "6c04fd2d-dd65-4dd7-b49b-35232d63273c", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "112007", "timestamp": "2025-08-05T15:30:55.130509", "success": true, "status_code": 200, "response_time": 0.467778, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "112007"}, "deliveryArrangementsId": "20250805073054852058890", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:54"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1176", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923405", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1176", "RateLimit-Reset": "1", "Date": "Tue, 05 Aug 2025 07:30:54 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "3bc3d917-c54f-421b-95e1-5691c9ac1615", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "122634", "timestamp": "2025-08-05T15:30:55.530737", "success": true, "status_code": 200, "response_time": 0.383462, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "122634"}, "deliveryArrangementsId": "20250805073055342298580", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "543", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1170", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924422", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1170", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "04f9f9ee-9ad5-4a46-8a51-31e9cedacf2b", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "111015", "timestamp": "2025-08-05T15:30:55.545991", "success": true, "status_code": 200, "response_time": 0.401604, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "111015"}, "deliveryArrangementsId": "20250805073055339774780", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1171", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924423", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1171", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "17126092-37df-451c-bfd2-5eaf9439edc5", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "111016", "timestamp": "2025-08-05T15:30:55.547210", "success": true, "status_code": 200, "response_time": 0.401355, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "111016"}, "deliveryArrangementsId": "20250805073055341037696", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1174", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923403", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1174", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "1401edc9-ee99-41e2-9d01-0b82b514c466", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "124233", "timestamp": "2025-08-05T15:30:55.561736", "success": true, "status_code": 200, "response_time": 0.415889, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "124233"}, "deliveryArrangementsId": "20250805073055344942203", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "544", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1172", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923400", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1172", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "f096afbf-b93d-4da4-ba2d-555626e5189c", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "122629", "timestamp": "2025-08-05T15:30:55.567999", "success": true, "status_code": 200, "response_time": 0.420233, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "122629"}, "deliveryArrangementsId": "20250805073055344644297", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1168", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924420", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1168", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "528fe672-fcbd-4956-898b-89fc9b2110c0", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "111014", "timestamp": "2025-08-05T15:30:55.607150", "success": true, "status_code": 200, "response_time": 0.464395, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "111014"}, "deliveryArrangementsId": "20250805073055339580852", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1172", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924424", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1172", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "38e649fd-0ff1-4fb3-9b72-7bec0660cae0", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "122632", "timestamp": "2025-08-05T15:30:55.607628", "success": true, "status_code": 200, "response_time": 0.461206, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "122632"}, "deliveryArrangementsId": "20250805073055347160326", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1173", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923401", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1173", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "d40d3a95-ee10-46dd-a430-faa6b4cc4f6c", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "111002", "timestamp": "2025-08-05T15:30:55.616309", "success": true, "status_code": 200, "response_time": 0.472942, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "111002"}, "deliveryArrangementsId": "20250805073055338513810", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "544", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1176", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923404", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1176", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "12e8e856-fb69-400d-9a16-54fcadbbc273", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "122637", "timestamp": "2025-08-05T15:30:55.626024", "success": true, "status_code": 200, "response_time": 0.476801, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "122637"}, "deliveryArrangementsId": "20250805073055341757185", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1175", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923402", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1175", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "c30693cf-38be-4fa7-857a-8ede80c52876", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "122642", "timestamp": "2025-08-05T15:30:55.634420", "success": true, "status_code": 200, "response_time": 0.485959, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "122642"}, "deliveryArrangementsId": "20250805073055345797792", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1169", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924421", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1169", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "55f4fca6-9eb6-47cd-915a-f20d8ffd421e", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "122639", "timestamp": "2025-08-05T15:30:55.946590", "success": true, "status_code": 200, "response_time": 0.397983, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "122639"}, "deliveryArrangementsId": "20250805073055746425958", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1165", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924417", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1165", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "1794a031-e33d-4c14-8622-b2dc2f852ff2", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "122636", "timestamp": "2025-08-05T15:30:55.946901", "success": true, "status_code": 200, "response_time": 0.412822, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "122636"}, "deliveryArrangementsId": "20250805073055734421582", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1166", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924418", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1166", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "c6a9f6a4-5c1d-41f4-8991-a0b82e9f64f3", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "122638", "timestamp": "2025-08-05T15:30:56.049339", "success": true, "status_code": 200, "response_time": 0.499655, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "122638"}, "deliveryArrangementsId": "20250805073055744038435", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1171", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923399", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1171", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "54d2d984-1b81-4db2-afac-85359c7348ac", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "113118", "timestamp": "2025-08-05T15:30:56.053647", "success": true, "status_code": 200, "response_time": 0.479059, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "113118"}, "deliveryArrangementsId": "20250805073055765249894", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1169", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923397", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1169", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "60b61d80-a1d0-4d53-8272-9c1747d83dbf", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "122640", "timestamp": "2025-08-05T15:30:56.053923", "success": true, "status_code": 200, "response_time": 0.486688, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "122640"}, "deliveryArrangementsId": "20250805073055756585720", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "544", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1170", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923398", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1170", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "6fa77788-7b50-47fb-97a4-9b616b7abd54", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "118312", "timestamp": "2025-08-05T15:30:56.066128", "success": true, "status_code": 200, "response_time": 0.45552, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "118312"}, "deliveryArrangementsId": "20250805073055822819457", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1163", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924415", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1163", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "883756dd-0bf1-4280-8969-91f73710cc04", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "113112", "timestamp": "2025-08-05T15:30:56.087643", "success": true, "status_code": 200, "response_time": 0.476378, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "113112"}, "deliveryArrangementsId": "20250805073055836722526", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1162", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924414", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1162", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "8c7726da-551c-4693-a62a-b489d8f3a2c9", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "028129", "timestamp": "2025-08-05T15:30:56.476643", "success": true, "status_code": 200, "response_time": 0.833291, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "028129"}, "deliveryArrangementsId": "20250805073055847296119", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1167", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923395", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1167", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "65b8853e-8ba7-4519-98fc-3dd84a718fd9", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "028121", "timestamp": "2025-08-05T15:30:56.567235", "success": true, "status_code": 200, "response_time": 0.949008, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "028121"}, "deliveryArrangementsId": "20250805073055832722156", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1168", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923396", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1168", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "3c54d39b-59a3-4a6c-81c1-d648ec08deda", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "028119", "timestamp": "2025-08-05T15:30:56.569465", "success": true, "status_code": 200, "response_time": 0.941319, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "028119"}, "deliveryArrangementsId": "20250805073055844946626", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:55"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1161", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924413", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1161", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 07:30:55 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "615fbd69-aadd-4156-822d-3eda23eaecae", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "438010", "timestamp": "2025-08-05T15:30:57.102080", "success": true, "status_code": 200, "response_time": 0.514844, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "438010"}, "deliveryArrangementsId": "20250805073056834664965", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:56"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1161", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924409", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1161", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 07:30:56 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "6f6d61d9-34b7-4308-9b01-e726f24958a8", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "438024", "timestamp": "2025-08-05T15:30:57.110004", "success": true, "status_code": 200, "response_time": 0.524061, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "438024"}, "deliveryArrangementsId": "20250805073056831928805", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:56"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1153", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923373", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1153", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 07:30:56 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "19e65008-b209-45b5-9b4e-cccb2c0cc17d", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "436000", "timestamp": "2025-08-05T15:30:57.116765", "success": true, "status_code": 200, "response_time": 0.52827, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "436000"}, "deliveryArrangementsId": "20250805073056833216482", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:56"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1154", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923375", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1154", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 07:30:56 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "a6612801-2698-40c0-8b6e-a7ce9fa8f342", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "433020", "timestamp": "2025-08-05T15:30:57.120333", "success": true, "status_code": 200, "response_time": 0.528782, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "433020"}, "deliveryArrangementsId": "20250805073056834906945", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:56"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1160", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924408", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1160", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 07:30:56 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "5887d775-e2ce-4878-a133-855dee612052", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "435004", "timestamp": "2025-08-05T15:30:57.121137", "success": true, "status_code": 200, "response_time": 0.535115, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "435004"}, "deliveryArrangementsId": "20250805073056836466416", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:56"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1155", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923374", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1155", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 07:30:56 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "dd4d1693-4f3c-404b-bd6a-51c41805ffee", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "438016", "timestamp": "2025-08-05T15:30:57.125941", "success": true, "status_code": 200, "response_time": 0.534549, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "438016"}, "deliveryArrangementsId": "20250805073056814935778", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:56"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1157", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923377", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1157", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 07:30:56 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "0053a130-7568-434b-b1ea-376be1e183c9", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "438001", "timestamp": "2025-08-05T15:30:57.126570", "success": true, "status_code": 200, "response_time": 0.53609, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "438001"}, "deliveryArrangementsId": "20250805073056836264344", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:56"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1162", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924410", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1162", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 07:30:56 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "0b7033ad-87bb-485e-9b33-57b2856e4128", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "834019", "timestamp": "2025-08-05T15:30:57.357493", "success": true, "status_code": 200, "response_time": 0.228997, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "834019"}, "deliveryArrangementsId": "20250805073057347115917", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:57"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1155", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923368", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1155", "RateLimit-Reset": "3", "Date": "Tue, 05 Aug 2025 07:30:57 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "e6974204-868f-48fb-b194-da6057236807", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "834016", "timestamp": "2025-08-05T15:30:57.382186", "success": true, "status_code": 200, "response_time": 0.252379, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "834016"}, "deliveryArrangementsId": "20250805073057348066380", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:57"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1158", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923371", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1158", "RateLimit-Reset": "3", "Date": "Tue, 05 Aug 2025 07:30:57 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "c8fcfd68-45ce-406e-82a1-447745186db0", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "024078", "timestamp": "2025-08-05T15:30:57.504117", "success": true, "status_code": 200, "response_time": 0.919779, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "024078"}, "deliveryArrangementsId": "20250805073056815685693", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:56"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1163", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924411", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1163", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 07:30:56 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "f485bb60-5fd3-44f2-b77d-5471660d111a", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "028159", "timestamp": "2025-08-05T15:30:57.525019", "success": true, "status_code": 200, "response_time": 0.941485, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "028159"}, "deliveryArrangementsId": "20250805073056815855237", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:56"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1164", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924412", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1164", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 07:30:56 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "542eff5e-5134-4736-8689-3fa9fa949812", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "618408", "timestamp": "2025-08-05T15:30:57.618521", "success": true, "status_code": 200, "response_time": 0.495568, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "618408"}, "deliveryArrangementsId": "20250805073057345639559", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:57"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1164", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924408", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1164", "RateLimit-Reset": "3", "Date": "Tue, 05 Aug 2025 07:30:57 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "3d4d2ac4-db59-41ae-af49-9818855edd16", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "465454", "timestamp": "2025-08-05T15:30:57.620657", "success": true, "status_code": 200, "response_time": 0.49703, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "465454"}, "deliveryArrangementsId": "20250805073057344676727", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:57"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1156", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923369", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1156", "RateLimit-Reset": "3", "Date": "Tue, 05 Aug 2025 07:30:57 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "e19ef985-e9b3-4834-b2c3-7f2b2a1e9d47", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "465567", "timestamp": "2025-08-05T15:30:57.622544", "success": true, "status_code": 200, "response_time": 0.514696, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "465567"}, "deliveryArrangementsId": "20250805073057330308929", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:57"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1159", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923372", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1159", "RateLimit-Reset": "3", "Date": "Tue, 05 Aug 2025 07:30:57 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "3b73d212-53c5-436f-8a94-eaf1a17ee298", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "638857", "timestamp": "2025-08-05T15:30:57.622741", "success": true, "status_code": 200, "response_time": 0.494593, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "638857"}, "deliveryArrangementsId": "20250805073057348923390", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:57"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1163", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96924407", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1163", "RateLimit-Reset": "3", "Date": "Tue, 05 Aug 2025 07:30:57 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "68a5ebef-fa7e-433e-b3be-f219449610f2", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "636630", "timestamp": "2025-08-05T15:30:57.622931", "success": true, "status_code": 200, "response_time": 0.500775, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "636630"}, "deliveryArrangementsId": "20250805073057348122271", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:57"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1157", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923370", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1157", "RateLimit-Reset": "3", "Date": "Tue, 05 Aug 2025 07:30:57 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "326fc170-fbee-4bd0-ba57-dbf2448dd0c4", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "529400", "timestamp": "2025-08-05T15:30:57.888833", "success": true, "status_code": 200, "response_time": 0.503887, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "529400"}, "deliveryArrangementsId": "20250805073057669174706", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:57"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1151", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923525", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1151", "RateLimit-Reset": "3", "Date": "Tue, 05 Aug 2025 07:30:57 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "94a6ff10-bf94-4191-ad44-b7318f451555", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "513210", "timestamp": "2025-08-05T15:30:57.892614", "success": true, "status_code": 200, "response_time": 0.531883, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "513210"}, "deliveryArrangementsId": "20250805073057670975477", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T07:30:57"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1152", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923526", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1152", "RateLimit-Reset": "3", "Date": "Tue, 05 Aug 2025 07:30:57 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "7c6e1616-8f92-4a8a-be9e-5ec852e7ec94", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "161625", "timestamp": "2025-08-05T15:30:58.028788", "success": true, "status_code": 200, "response_time": 0.515239, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "161625"}, "deliveryArrangementsId": "20250805073057812902982", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:57"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1154", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923367", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1154", "RateLimit-Reset": "3", "Date": "Tue, 05 Aug 2025 07:30:57 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "cb6171f9-ff01-4d58-b4d6-a4c42bbef295", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "154109", "timestamp": "2025-08-05T15:30:58.563328", "success": true, "status_code": 200, "response_time": 0.521502, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "154109"}, "deliveryArrangementsId": "20250805073058326336258", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1158", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923524", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1158", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "a09c79ff-8600-4c18-a90b-3c70c25886f9", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "154111", "timestamp": "2025-08-05T15:30:58.663820", "success": true, "status_code": 200, "response_time": 0.620866, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "154111"}, "deliveryArrangementsId": "20250805073058351124766", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1156", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923363", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1156", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "70ab4a72-6726-4bc2-9937-d2880ae9275e", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "154108", "timestamp": "2025-08-05T15:30:58.664204", "success": true, "status_code": 200, "response_time": 0.623789, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "154108"}, "deliveryArrangementsId": "20250805073058356199757", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1154", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923520", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1154", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "9ab66101-362e-4680-805d-7ed3d39ab655", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "154299", "timestamp": "2025-08-05T15:30:58.664506", "success": true, "status_code": 200, "response_time": 0.623541, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "154299"}, "deliveryArrangementsId": "20250805073058339351386", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1157", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923523", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1157", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "5d218b76-9fff-4bf3-9023-306f50bab8b7", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "154110", "timestamp": "2025-08-05T15:30:58.669334", "success": true, "status_code": 200, "response_time": 0.625236, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "154110"}, "deliveryArrangementsId": "20250805073058338502208", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "545", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1158", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923365", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1158", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "bc299b6f-5396-4a3e-afae-a5c0e26f7ea1", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "161562", "timestamp": "2025-08-05T15:30:58.669769", "success": true, "status_code": 200, "response_time": 0.632804, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "161562"}, "deliveryArrangementsId": "20250805073058345834862", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1157", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923364", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1157", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "fb365832-bbc7-4949-b2c6-06b77779dac6", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "164300", "timestamp": "2025-08-05T15:30:58.671447", "success": true, "status_code": 200, "response_time": 0.630238, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "164300"}, "deliveryArrangementsId": "20250805073058347406044", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1155", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923521", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1155", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "a3d1e0f3-a857-4111-b0f7-a5d351d2693c", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "158100", "timestamp": "2025-08-05T15:30:59.070687", "success": true, "status_code": 200, "response_time": 0.493718, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "158100"}, "deliveryArrangementsId": "20250805073058855144605", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1152", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923518", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1152", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "905bdaa3-587f-4b05-b683-dc6816fe7d6b", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "154113", "timestamp": "2025-08-05T15:30:59.074217", "success": true, "status_code": 200, "response_time": 0.499802, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "154113"}, "deliveryArrangementsId": "20250805073058857044069", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1151", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923517", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1151", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "2b51bfa5-6dbb-4f4c-9077-f6b7f54389f4", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "158106", "timestamp": "2025-08-05T15:30:59.080643", "success": true, "status_code": 200, "response_time": 0.41336, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "158106"}, "deliveryArrangementsId": "20250805073058860227938", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1150", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923516", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1150", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "60b01acc-02c9-4ea4-9ca2-d77f95abce13", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": "158112", "timestamp": "2025-08-05T15:30:59.083980", "success": true, "status_code": 200, "response_time": 0.41227, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "158112"}, "deliveryArrangementsId": "20250805073058868654491", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1150", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923357", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1150", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "0024e629-3c29-4455-aaa5-b9b0ae1974b3", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "158105", "timestamp": "2025-08-05T15:30:59.189569", "success": true, "status_code": 200, "response_time": 0.52369, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "158105"}, "deliveryArrangementsId": "20250805073058873758839", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:59"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1149", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923356", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1149", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "437b822f-45b8-44e5-8afb-7deb31c5c48d", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "151774", "timestamp": "2025-08-05T15:30:59.189859", "success": true, "status_code": 200, "response_time": 0.514319, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "151774"}, "deliveryArrangementsId": "20250805073058868319847", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:59"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1151", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923358", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1151", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "414a66d3-390d-4fe8-be9c-caba78b65261", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": "158101", "timestamp": "2025-08-05T15:30:59.193074", "success": true, "status_code": 200, "response_time": 0.59556, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "158101"}, "deliveryArrangementsId": "20250805073058858675490", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1314", "unitOfMeasure": "ORDERS", "shipNode": "CDC.085", "mergeNode": "LSC.1314"}]}, "id": "HD~2~STANDARD", "earliestShipDate": "2025-08-05T07:30:58"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1152", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96923359", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1152", "RateLimit-Reset": "2", "Date": "Tue, 05 Aug 2025 07:30:58 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "5aefa079-f621-43dc-8336-5d24988c132d", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}]