[{"zipcode": 409911, "timestamp": "2025-08-05T14:35:00.236389", "success": true, "status_code": 200, "response_time": 0.372451, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "409911"}, "deliveryArrangementsId": "20250805063500083113079", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:00"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1194", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96954719", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1194", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 06:35:00 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "888772ac-efe5-4f43-86b0-0a9ebf876ca4", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": 404700, "timestamp": "2025-08-05T14:35:00.286682", "success": true, "status_code": 200, "response_time": 0.42869, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "404700"}, "deliveryArrangementsId": "20250805063500073932802", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:00"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1191", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96956348", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1191", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 06:35:00 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "cec975a1-f00e-4a95-9127-528216040fca", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": 409108, "timestamp": "2025-08-05T14:35:00.298555", "success": true, "status_code": 200, "response_time": 0.437105, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "409108"}, "deliveryArrangementsId": "20250805063500085227263", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:00"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1196", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96954721", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1196", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 06:35:00 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "9da60fdc-fd72-4156-8596-e5e24255de7c", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": 405900, "timestamp": "2025-08-05T14:35:00.356327", "success": true, "status_code": 200, "response_time": 0.497254, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "405900"}, "deliveryArrangementsId": "20250805063500073200787", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:00"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1197", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96954722", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1197", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 06:35:00 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "0379183c-2b85-480f-ab08-f52fce57dea3", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": 404600, "timestamp": "2025-08-05T14:35:00.357031", "success": true, "status_code": 200, "response_time": 0.499162, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "404600"}, "deliveryArrangementsId": "20250805063500083979794", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:00"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1195", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96954720", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1195", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 06:35:00 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "ff935663-7d07-4ebe-8911-ed200689ffb8", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": 404500, "timestamp": "2025-08-05T14:35:00.357175", "success": true, "status_code": 200, "response_time": 0.49633, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "404500"}, "deliveryArrangementsId": "20250805063500088346400", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:00"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1189", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96956346", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1189", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 06:35:00 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "ee113944-87e4-41fe-a0da-8866808d9603", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": 409600, "timestamp": "2025-08-05T14:35:00.360116", "success": true, "status_code": 200, "response_time": 0.50403, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "409600"}, "deliveryArrangementsId": "20250805063500093968185", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:00"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "548", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1188", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96956345", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1188", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 06:35:00 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "38ead5b2-137e-4483-9336-9d9fa5cff249", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": 404101, "timestamp": "2025-08-05T14:35:00.361599", "success": true, "status_code": 200, "response_time": 0.508219, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "404101"}, "deliveryArrangementsId": "20250805063500092937576", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:00"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1193", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96954718", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1193", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 06:35:00 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "a0b5189c-3913-4546-be9a-9fa2caf2e88d", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": 405899, "timestamp": "2025-08-05T14:35:01.350740", "success": true, "status_code": 200, "response_time": 1.494939, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "405899"}, "deliveryArrangementsId": "20250805063500091918697", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:00"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1190", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96956347", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1190", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 06:35:00 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "a363f3ba-bc21-4b09-9f66-04a601666d44", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": 409809, "timestamp": "2025-08-05T14:35:01.359052", "success": true, "status_code": 200, "response_time": 1.508847, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "409809"}, "deliveryArrangementsId": "20250805063500074469371", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:00"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1192", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96956349", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1192", "RateLimit-Reset": "5", "Date": "Tue, 05 Aug 2025 06:35:00 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "3938ad96-a77a-4d45-89df-d29f6e025844", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": 666315, "timestamp": "2025-08-05T14:35:01.718864", "success": true, "status_code": 200, "response_time": 0.35004, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "666315"}, "deliveryArrangementsId": "20250805063501573173929", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:01"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1186", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96956343", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1186", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 06:35:01 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "0b376447-bee0-4a07-b33c-5b4fa5eae320", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": 666310, "timestamp": "2025-08-05T14:35:01.763459", "success": true, "status_code": 200, "response_time": 0.393926, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "666310"}, "deliveryArrangementsId": "20250805063501581444503", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:01"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1185", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96956342", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1185", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 06:35:01 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "5d3ccff7-8bad-4c38-aaf5-0315387a2496", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": 666305, "timestamp": "2025-08-05T14:35:01.774581", "success": true, "status_code": 200, "response_time": 0.404125, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "666305"}, "deliveryArrangementsId": "20250805063501585659372", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:01"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1189", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96954715", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1189", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 06:35:01 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "8eb7cfca-7032-4a8f-ba3b-929f4f82ccd2", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": 666313, "timestamp": "2025-08-05T14:35:01.806937", "success": true, "status_code": 200, "response_time": 0.43547, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "666313"}, "deliveryArrangementsId": "20250805063501578912793", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:01"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1188", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96954714", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1188", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 06:35:01 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "50381544-04ad-4041-aa60-60e95704b97e", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": 666302, "timestamp": "2025-08-05T14:35:01.810228", "success": true, "status_code": 200, "response_time": 0.435068, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "666302"}, "deliveryArrangementsId": "20250805063501581390750", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:01"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1187", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96954713", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1187", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 06:35:01 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "aed4c7bc-2950-41e5-9a24-bbeb267e9b2c", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": 666319, "timestamp": "2025-08-05T14:35:01.813454", "success": true, "status_code": 200, "response_time": 0.437914, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "666319"}, "deliveryArrangementsId": "20250805063501585821674", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:01"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1183", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96956340", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1183", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 06:35:01 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "c24880e3-abb2-4d61-83ec-caa748dfaa8c", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": 666308, "timestamp": "2025-08-05T14:35:01.818866", "success": true, "status_code": 200, "response_time": 0.444708, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "666308"}, "deliveryArrangementsId": "20250805063501591801168", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:01"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1182", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96956339", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1182", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 06:35:01 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "ac542287-33a7-4c48-b1fb-ad7e3d074247", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": 666307, "timestamp": "2025-08-05T14:35:01.839520", "success": true, "status_code": 200, "response_time": 0.468671, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "666307"}, "deliveryArrangementsId": "20250805063501586734476", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:01"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1184", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96956341", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1184", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 06:35:01 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "d0fa9950-a714-43b8-9720-6a8410b7da73", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a5o8443; path=/; Httponly; Secure"}}, {"zipcode": 666312, "timestamp": "2025-08-05T14:35:02.790464", "success": true, "status_code": 200, "response_time": 1.418256, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "666312"}, "deliveryArrangementsId": "20250805063501581470874", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~2"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~2", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:01"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "547", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1186", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96954712", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1186", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 06:35:01 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "c178b2af-ac44-4770-869d-9ebf74835877", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}, {"zipcode": 666306, "timestamp": "2025-08-05T14:35:02.826562", "success": true, "status_code": 200, "response_time": 1.453184, "response_data": {"shipToAddress": {"country": "CN", "zipCode": "666306"}, "deliveryArrangementsId": "20250805063501596368881", "itemLines": {"itemLine": [{"unitWeight": "0.051", "itemType": "ART", "requiredQty": "1.0", "unitVolume": "0.0003", "id": "1", "itemNo": "10534224", "volumeUnitOfMeasure": "CUMETER", "weightUnitOfMeasure": "KG"}]}, "businessUnit": {"code": "1228", "type": "STO"}, "checkCapacity": "true", "channelReferences": {"pageContext": "", "sellingChannelName": "DeliveryChecker"}, "serviceTypes": {"serviceType": [{"possibleDeliveryLines": {"possibleDeliveryLine": [{"deliveryAssociations": {"deliveryAssociation": [{"itemQty": "1.0", "itemLineId": "1"}]}, "id": "HD~~~3"}]}, "id": "HOME_DELIVERY", "possibleSolutions": {"possibleSolution": [{"service": "STANDARD", "deliveryLines": {"deliveryLine": [{"serviceItemId": "SGR50000597", "transportMethodType": "PARCEL", "deliveryId": "HD~~~3", "mergeNodeList": "LSC.1313", "unitOfMeasure": "ORDERS", "shipNode": "CDC.037", "mergeNode": "LSC.1313"}]}, "id": "HD~1~STANDARD", "earliestShipDate": "2025-08-05T06:35:01"}]}}]}, "checkInventory": "true"}, "error": null, "request_headers": {"User-Agent": "python-requests/2.32.3", "Accept-Encoding": "gzip, deflate, zstd", "Accept": "*/*", "Connection": "keep-alive", "Content-Type": "application/json", "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Content-Length": "434"}, "response_headers": {"Content-Type": "application/json; charset=UTF-8", "Content-Length": "546", "Connection": "keep-alive", "X-RateLimit-Limit-5": "1200", "X-RateLimit-Remaining-5": "1185", "X-RateLimit-Limit-month": "100000000", "X-RateLimit-Remaining-month": "96954711", "RateLimit-Limit": "1200", "RateLimit-Remaining": "1185", "RateLimit-Reset": "4", "Date": "Tue, 05 Aug 2025 06:35:01 GMT", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Content-Encoding": "gzip", "X-FRAME-OPTIONS": "SAMEORIGIN", "X-UA-Compatible": "IE=EmulateIE8", "X-XSS-Protection": "1; mode=block", "Access-Control-Allow-Origin": "*", "Unique-Rq-Id": "5198e827-eca8-4ac0-82f7-34b4b184c6a3", "Set-Cookie": "BIGipServer~CHNc-DC9-ac-inf01-apims~as3-prod-api-ingka~private-api.ingka.prodcn.ikea.com_pool-8443=rd11o00000000000000000000ffff0a5237a7o8443; path=/; Httponly; Secure"}}]