#!/usr/bin/env python3
"""
Check progress of all time-windows tests
"""

import os
import json
import argparse
from pathlib import Path
from datetime import datetime

def get_all_test_progress():
    """Get progress information for all tests"""
    results_dir = Path("results")
    if not results_dir.exists():
        print("❌ Results directory not found")
        return
    
    test_dirs = [d for d in results_dir.iterdir() if d.is_dir() and d.name.startswith("timewindows_test_")]
    if not test_dirs:
        print("❌ No test directories found")
        return
    
    print("📊 All Time-Windows Test Progress:")
    print("=" * 80)
    
    # Sort by creation time, newest first
    test_dirs.sort(key=lambda d: d.stat().st_mtime, reverse=True)
    
    for test_dir in test_dirs:
        progress_files = list(test_dir.glob("progress_*.json"))
        if not progress_files:
            print(f"📁 {test_dir.name}: (no progress file)")
            continue
        
        try:
            with open(progress_files[0], 'r') as f:
                progress = json.load(f)
            
            completed = progress.get('completed_rows', 0)
            total = progress.get('total_rows', 0)
            item_id = progress.get('item_id', 'Unknown')
            concurrency = progress.get('concurrency', 'Unknown')
            completion_pct = progress.get('completion_percentage', 0)
            estimated_time = progress.get('estimated_remaining_time', 'Unknown')
            last_update = progress.get('last_update_time', 'Unknown')
            current_row = progress.get('current_row', completed)
            
            # Parse last update time
            try:
                if last_update != 'Unknown':
                    last_update_dt = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                    time_ago = datetime.now() - last_update_dt.replace(tzinfo=None)
                    if time_ago.total_seconds() < 3600:
                        time_ago_str = f"{time_ago.total_seconds()/60:.0f}m ago"
                    else:
                        time_ago_str = f"{time_ago.total_seconds()/3600:.1f}h ago"
                else:
                    time_ago_str = "Unknown"
            except:
                time_ago_str = "Unknown"
            
            print(f"📁 {test_dir.name}")
            print(f"   🏷️  Item ID: {item_id}")
            print(f"   📊 Progress: {completed}/{total} rows ({completion_pct:.1f}%)")
            print(f"   🚀 Current row: {current_row}")
            print(f"   🔄 Concurrency: {concurrency}")
            if estimated_time != 'Unknown':
                print(f"   ⏱️  Est. remaining: {estimated_time}")
            print(f"   🕐 Last update: {time_ago_str}")
            
            # Show command to continue
            if completed < total:
                print(f"   💡 To continue: python time_windows_test.py --item-id {item_id} --start-row {current_row} --concurrency {concurrency}")
            else:
                print(f"   ✅ COMPLETED")
            print()
            
        except Exception as e:
            print(f"📁 {test_dir.name}: (error reading progress: {e})")

def find_best_test_to_continue(item_id=None):
    """Find the test with most progress for given item_id"""
    results_dir = Path("results")
    if not results_dir.exists():
        return None
    
    test_dirs = [d for d in results_dir.iterdir() if d.is_dir() and d.name.startswith("timewindows_test_")]
    best_test = None
    best_progress = 0
    
    for test_dir in test_dirs:
        progress_files = list(test_dir.glob("progress_*.json"))
        if not progress_files:
            continue
        
        try:
            with open(progress_files[0], 'r') as f:
                progress = json.load(f)
            
            test_item_id = progress.get('item_id', 'Unknown')
            completed = progress.get('completed_rows', 0)
            total = progress.get('total_rows', 0)
            
            # Skip if item_id doesn't match (when specified)
            if item_id and str(test_item_id) != str(item_id):
                continue
            
            # Skip if already completed
            if completed >= total:
                continue
            
            # Check if this has more progress
            if completed > best_progress:
                best_progress = completed
                best_test = {
                    'dir': test_dir.name,
                    'progress': progress
                }
                
        except:
            continue
    
    return best_test

def main():
    parser = argparse.ArgumentParser(description='Check time-windows test progress')
    parser.add_argument('--item-id', help='Show best test to continue for specific item ID')
    parser.add_argument('--best', action='store_true', help='Show best test to continue')
    
    args = parser.parse_args()
    
    if args.best or args.item_id:
        best_test = find_best_test_to_continue(args.item_id)
        if best_test:
            progress = best_test['progress']
            item_id = progress.get('item_id')
            current_row = progress.get('current_row', progress.get('completed_rows', 0))
            concurrency = progress.get('concurrency', 15)
            completed = progress.get('completed_rows', 0)
            total = progress.get('total_rows', 0)
            
            print(f"🎯 Best test to continue:")
            print(f"   📁 Directory: {best_test['dir']}")
            print(f"   🏷️  Item ID: {item_id}")
            print(f"   📊 Progress: {completed}/{total} rows ({completed/total*100:.1f}%)")
            print(f"   🚀 Continue from row: {current_row}")
            print()
            print(f"💡 Command to continue:")
            print(f"   python time_windows_test.py --item-id {item_id} --start-row {current_row} --concurrency {concurrency}")
        else:
            item_filter = f" for item {args.item_id}" if args.item_id else ""
            print(f"❌ No incomplete tests found{item_filter}")
    else:
        get_all_test_progress()

if __name__ == "__main__":
    main()
