#!/usr/bin/env python3
"""
Check the status of zipcode 272002 for truck delivery
"""

import pandas as pd
import os

def check_zipcode_272002():
    """Check zipcode 272002 status"""
    
    zipcode = "272002"
    print(f"🔍 CHECKING ZIPCODE {zipcode}")
    print("=" * 50)
    
    # Check in quick filter results
    try:
        df = pd.read_excel('quick_filter_results.xlsx')
        rows = df[df['ZipCode'].astype(str).str.zfill(6) == zipcode]
        
        if not rows.empty:
            row = rows.iloc[0]
            parcel_status = row.get('Parcel_Quick_Status', 'N/A')
            truck_status = row.get('Truck_Quick_Status', 'N/A')
            province = row.get('Province-EN', 'Unknown')
            city = row.get('City-EN', 'Unknown')
            district = row.get('District-EN', 'Unknown')
            
            print(f"📍 LOCATION: {province} > {city} > {district}")
            print(f"📦 Parcel Status: {parcel_status}")
            print(f"🚛 Truck Status: {truck_status}")
            
            if truck_status == 'SUCCESS':
                print(f"✅ TRUCK DELIVERY: WORKS - Time windows available")
            else:
                print(f"❌ TRUCK DELIVERY: Does not work - Never succeeded")
        else:
            print(f"❌ Zipcode {zipcode} not found in results")
    
    except Exception as e:
        print(f"❌ Error reading quick filter results: {e}")
    
    # Double-check by searching logs directly
    print(f"\n🔍 CHECKING LOGS FOR {zipcode}:")
    
    results_dir = "results"
    if os.path.exists(results_dir):
        # Search for this zipcode in truck test logs
        truck_tests_found = []
        
        test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
        
        for test_dir in test_dirs:
            full_path = os.path.join(results_dir, test_dir)
            log_files = [f for f in os.listdir(full_path) if f.endswith('.log')]
            
            if not log_files:
                continue
            
            log_path = os.path.join(full_path, log_files[0])
            
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                
                # Check if this is a truck test and contains our zipcode
                is_truck = "Item ID: 70570836" in log_content
                has_zipcode = zipcode in log_content
                
                if is_truck and has_zipcode:
                    # Look for the specific result
                    lines = log_content.split('\n')
                    for line in lines:
                        if zipcode in line and (' - SUCCESS' in line or ' - FAILED' in line):
                            result = 'SUCCESS' if ' - SUCCESS' in line else 'FAILED'
                            truck_tests_found.append({
                                'test_dir': test_dir,
                                'result': result,
                                'line': line.strip()
                            })
                            break
            
            except Exception as e:
                continue
        
        if truck_tests_found:
            print(f"📊 FOUND {len(truck_tests_found)} TRUCK TEST RESULTS:")
            for i, test in enumerate(truck_tests_found, 1):
                print(f"  {i}. {test['test_dir']}: {test['result']}")
                print(f"     Log line: {test['line']}")
            
            # Check if any succeeded
            has_success = any(test['result'] == 'SUCCESS' for test in truck_tests_found)
            if has_success:
                print(f"\n✅ CONCLUSION: Zipcode {zipcode} WORKS with truck delivery")
            else:
                print(f"\n❌ CONCLUSION: Zipcode {zipcode} does NOT work with truck delivery")
        else:
            print(f"❌ No truck test results found for zipcode {zipcode}")

def main():
    """Main function"""
    check_zipcode_272002()

if __name__ == "__main__":
    main()
