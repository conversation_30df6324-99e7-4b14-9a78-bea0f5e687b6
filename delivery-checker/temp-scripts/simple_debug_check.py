#!/usr/bin/env python3
"""
Simple debug check for the comprehensive results
"""

import pandas as pd

def main():
    """Main function"""
    
    print("🔍 SIMPLE DEBUG CHECK")
    print("=" * 40)
    
    # Load the files
    parcel_df = pd.read_excel('parcel_final_comprehensive_results.xlsx')
    truck_df = pd.read_excel('truck_final_comprehensive_results.xlsx')
    
    print("📊 STATUS COUNTS:")
    print("📦 PARCEL:")
    print(parcel_df['Parcel_Final_Status'].value_counts())
    print()
    print("🚛 TRUCK:")  
    print(truck_df['Truck_Final_Status'].value_counts())
    
    print()
    print("🔍 SAMPLE UNTESTED WITH TESTS:")
    untested_with_tests = parcel_df[(parcel_df['Parcel_Final_Status'] == 'UNTESTED') & (parcel_df['Parcel_Total_Tests'] > 0)]
    print(f"📦 Parcel untested but with tests: {len(untested_with_tests)}")
    
    if len(untested_with_tests) > 0:
        sample = untested_with_tests.head(3)
        for idx, row in sample.iterrows():
            zipcode = str(row['ZipCode']).zfill(6)
            tests = row['Parcel_Total_Tests']
            print(f"  {zipcode}: {tests} tests")

if __name__ == "__main__":
    main()
