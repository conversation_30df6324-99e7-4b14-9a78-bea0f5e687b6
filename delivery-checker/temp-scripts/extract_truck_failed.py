#!/usr/bin/env python3
"""
Extract the 16 truck locations that are marked as NOT_TESTED_OR_FAILED
"""

import pandas as pd

def extract_truck_failed():
    """Extract truck locations that never succeeded"""
    
    print("🔍 EXTRACTING TRUCK NOT_TESTED_OR_FAILED LOCATIONS")
    print("=" * 60)
    
    try:
        # Load the quick filter results
        df = pd.read_excel('quick_filter_results.xlsx')
        
        # Filter for truck NOT_TESTED_OR_FAILED
        truck_failed = df[df['Truck_Quick_Status'] == 'NOT_TESTED_OR_FAILED']
        
        print(f"📊 Found {len(truck_failed)} truck locations that never succeeded")
        
        if len(truck_failed) > 0:
            print(f"\n📋 TRUCK NOT_TESTED_OR_FAILED LOCATIONS:")
            print("=" * 80)
            
            for idx, row in truck_failed.iterrows():
                zipcode = str(row['ZipCode']).zfill(6)
                province = row.get('Province-EN', 'Unknown')
                city = row.get('City-EN', 'Unknown') 
                district = row.get('District-EN', 'Unknown')
                subdistrict = row.get('SubDistrict-EN', 'Unknown')
                
                print(f"{idx+1:2d}. {zipcode}: {province} > {city} > {district} > {subdistrict}")
            
            # Save to separate file
            truck_failed_file = 'truck_never_succeeded_locations.xlsx'
            truck_failed.to_excel(truck_failed_file, index=False)
            print(f"\n💾 Saved to: {truck_failed_file}")
            
            # Also create a simple zipcode list
            zipcode_list = [str(row['ZipCode']).zfill(6) for _, row in truck_failed.iterrows()]
            zipcode_df = pd.DataFrame({'ZipCode': zipcode_list})
            zipcode_file = 'truck_never_succeeded_zipcodes.xlsx'
            zipcode_df.to_excel(zipcode_file, index=False)
            print(f"💾 Zipcode list saved to: {zipcode_file}")
            
            print(f"\n📋 ZIPCODE LIST:")
            for i, zipcode in enumerate(zipcode_list, 1):
                print(f"  {i:2d}. {zipcode}")
        
        else:
            print("✅ No truck locations found that never succeeded!")
    
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main function"""
    extract_truck_failed()

if __name__ == "__main__":
    main()
