#!/usr/bin/env python3
"""
Check the status of the stopped execution and determine where to resume
"""

import os
import json
import pandas as pd

def check_execution_status():
    """Check the status of the stopped execution"""
    
    print("🔍 CHECKING EXECUTION STATUS")
    print("=" * 50)
    
    # Check the test directory
    test_dir = "results/timewindows_test_20250805_233749"
    
    if not os.path.exists(test_dir):
        print(f"❌ Test directory not found: {test_dir}")
        return
    
    print(f"📁 Test directory: {test_dir}")
    
    # Check log file
    log_file = f"{test_dir}/timewindows_test_20250805_233749.log"
    if os.path.exists(log_file):
        print(f"📄 Log file size: {os.path.getsize(log_file) / (1024*1024):.1f} MB")
        
        # Find the last processed row
        last_row = None
        total_lines = 0
        success_count = 0
        failed_count = 0
        
        with open(log_file, 'r') as f:
            for line in f:
                total_lines += 1
                if "Row " in line and (" - SUCCESS" in line or " - FAILED" in line):
                    # Extract row number
                    try:
                        row_part = line.split("Row ")[1].split(":")[0]
                        row_num = int(row_part)
                        last_row = row_num
                        
                        if " - SUCCESS" in line:
                            success_count += 1
                        else:
                            failed_count += 1
                    except:
                        pass
        
        print(f"📊 Log analysis:")
        print(f"  Total log lines: {total_lines:,}")
        print(f"  Last processed row: {last_row}")
        print(f"  Successful tests: {success_count:,}")
        print(f"  Failed tests: {failed_count:,}")
        print(f"  Total processed: {success_count + failed_count:,}")
    
    # Check progress file
    progress_file = f"{test_dir}/progress_20250805_233749.json"
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r') as f:
                progress = json.load(f)
            
            print(f"📊 Progress file:")
            print(f"  Item ID: {progress.get('item_id')}")
            print(f"  Start row: {progress.get('start_row')}")
            print(f"  End row: {progress.get('end_row')}")
            print(f"  Last processed row: {progress.get('last_processed_row')}")
            print(f"  Total processed: {progress.get('total_processed', 0)}")
            
        except Exception as e:
            print(f"⚠️  Could not read progress file: {e}")
    
    # Check Excel results file
    excel_file = f"{test_dir}/timewindows_test_results_20250805_233749.xlsx"
    if os.path.exists(excel_file):
        try:
            df = pd.read_excel(excel_file)
            
            # Count non-empty results
            filled_rows = 0
            for col in ['tw_delivery_success', 'tw_timewindows_success']:
                if col in df.columns:
                    filled_rows = df[col].notna().sum()
                    break
            
            print(f"📊 Excel results:")
            print(f"  Total rows in Excel: {len(df):,}")
            print(f"  Rows with results: {filled_rows:,}")
            
        except Exception as e:
            print(f"⚠️  Could not read Excel file: {e}")
    
    # Check API responses file
    api_file = f"{test_dir}/api_responses_20250805_233749.json"
    if os.path.exists(api_file):
        file_size = os.path.getsize(api_file)
        print(f"📊 API responses file: {file_size / (1024*1024):.1f} MB")
        
        # Try to count responses
        try:
            with open(api_file, 'r') as f:
                api_data = json.load(f)
            
            if isinstance(api_data, list):
                print(f"  API responses count: {len(api_data):,}")
            else:
                print(f"  API responses: Single response object")
                
        except Exception as e:
            print(f"⚠️  Could not parse API responses: {e}")
    
    # Determine resume point
    print(f"\n🎯 RESUME RECOMMENDATIONS:")
    
    if last_row is not None:
        next_row = last_row + 1
        print(f"  ✅ Resume from row: {next_row}")
        print(f"  📝 Command to resume:")
        print(f"     INPUT_EXCEL_FILE=parcel_failed_only_fixed.xlsx python time_windows_test.py --item-id 10534224 --start-row {next_row} --end-row 13667 --concurrency 20")
    else:
        print(f"  ⚠️  Could not determine last processed row")
        print(f"  📝 Restart from beginning:")
        print(f"     INPUT_EXCEL_FILE=parcel_failed_only_fixed.xlsx python time_windows_test.py --item-id 10534224 --start-row 0 --end-row 13667 --concurrency 20")

def main():
    """Main function"""
    check_execution_status()
    print("=" * 50)

if __name__ == "__main__":
    main()
