#!/usr/bin/env python3
"""
Hourly monitoring system to check progress and auto-start remaining tests
"""

import os
import json
import time
import subprocess
import pandas as pd
from datetime import datetime

def check_process_status():
    """Check if the current test processes are still running"""
    try:
        # Check for python processes running time_windows_test.py
        result = subprocess.run(['pgrep', '-f', 'time_windows_test.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            return len([pid for pid in pids if pid.strip()]), pids
        else:
            return 0, []
    except Exception as e:
        print(f"Error checking processes: {e}")
        return 0, []

def get_test_progress():
    """Get current progress of all tests"""
    
    test_status = {
        "parcel_failed": {"completed": 0, "total": 13668, "status": "unknown"},
        "truck_untested": {"completed": 0, "total": 18071, "status": "unknown"},
        "parcel_untested": {"completed": 0, "total": 2298, "status": "not_started"}
    }
    
    # Check Parcel Failed progress
    parcel_dir = "results/timewindows_test_20250806_000942"
    if os.path.exists(parcel_dir):
        log_file = f"{parcel_dir}/timewindows_test_20250806_000942.log"
        if os.path.exists(log_file):
            last_row = get_last_processed_row(log_file)
            if last_row is not None:
                test_status["parcel_failed"]["completed"] = last_row + 1
                if last_row + 1 >= 13668:
                    test_status["parcel_failed"]["status"] = "completed"
                else:
                    test_status["parcel_failed"]["status"] = "in_progress"
    
    # Check Truck Untested progress
    truck_dir = "results/timewindows_test_20250806_002307"
    if os.path.exists(truck_dir):
        log_file = f"{truck_dir}/timewindows_test_20250806_002307.log"
        if os.path.exists(log_file):
            last_row = get_last_processed_row(log_file)
            if last_row is not None:
                test_status["truck_untested"]["completed"] = last_row + 1
                if last_row + 1 >= 18071:
                    test_status["truck_untested"]["status"] = "completed"
                else:
                    test_status["truck_untested"]["status"] = "in_progress"
    
    # Check if Parcel Untested has started
    parcel_untested_dirs = [d for d in os.listdir("results") if "timewindows_test_" in d]
    for dir_name in parcel_untested_dirs:
        if dir_name not in ["timewindows_test_20250806_000942", "timewindows_test_20250806_002307"]:
            # Check if this is a parcel untested test by looking at the log
            log_file = f"results/{dir_name}/{dir_name}.log"
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    content = f.read()
                    if "parcel_untested_only_fixed.xlsx" in content:
                        last_row = get_last_processed_row(log_file)
                        if last_row is not None:
                            test_status["parcel_untested"]["completed"] = last_row + 1
                            if last_row + 1 >= 2298:
                                test_status["parcel_untested"]["status"] = "completed"
                            else:
                                test_status["parcel_untested"]["status"] = "in_progress"
                        break
    
    return test_status

def get_last_processed_row(log_file):
    """Get the last processed row from a log file"""
    last_row = None
    try:
        with open(log_file, 'r') as f:
            for line in f:
                if "Row " in line and (" - SUCCESS" in line or " - FAILED" in line):
                    try:
                        row_part = line.split("Row ")[1].split(":")[0]
                        row_num = int(row_part)
                        last_row = row_num
                    except:
                        pass
    except Exception as e:
        print(f"Error reading log file {log_file}: {e}")
    
    return last_row

def start_parcel_untested():
    """Start the Parcel Untested test"""
    try:
        print(f"🚀 Starting Parcel Untested test...")
        
        # Change to the correct directory and start the test
        cmd = [
            'bash', '-c',
            'cd /Users/<USER>/Work/etc/delivery-checker && INPUT_EXCEL_FILE=parcel_untested_only_fixed.xlsx python time_windows_test.py --item-id 10534224 --start-row 0 --end-row 2297 --concurrency 15'
        ]
        
        # Start the process in the background
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print(f"✅ Parcel Untested test started with PID: {process.pid}")
        return True
        
    except Exception as e:
        print(f"❌ Error starting Parcel Untested test: {e}")
        return False

def print_status_report(test_status, running_processes):
    """Print a comprehensive status report"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    print(f"\n{'='*80}")
    print(f"🕐 HOURLY STATUS REPORT - {timestamp}")
    print(f"{'='*80}")
    
    print(f"\n🔄 RUNNING PROCESSES:")
    print(f"  Active test processes: {running_processes}")
    
    print(f"\n📊 TEST PROGRESS:")
    
    total_completed = 0
    total_zipcodes = 0
    
    for test_name, status in test_status.items():
        completed = status["completed"]
        total = status["total"]
        test_status_str = status["status"]
        
        total_completed += completed
        total_zipcodes += total
        
        progress_pct = (completed / total) * 100 if total > 0 else 0
        
        # Format test name
        display_name = test_name.replace("_", " ").title()
        
        # Status emoji
        if test_status_str == "completed":
            emoji = "✅"
        elif test_status_str == "in_progress":
            emoji = "🔄"
        else:
            emoji = "❌"
        
        print(f"  {emoji} {display_name}:")
        print(f"    Progress: {completed:,} / {total:,} ({progress_pct:.1f}%)")
        print(f"    Status: {test_status_str.replace('_', ' ').title()}")
        
        if test_status_str == "in_progress":
            remaining = total - completed
            print(f"    Remaining: {remaining:,} zipcodes")
    
    # Overall progress
    overall_progress = (total_completed / total_zipcodes) * 100
    print(f"\n🎯 OVERALL PROGRESS:")
    print(f"  Total: {total_completed:,} / {total_zipcodes:,} ({overall_progress:.1f}%)")
    
    # Determine next action
    parcel_failed_done = test_status["parcel_failed"]["status"] == "completed"
    truck_untested_done = test_status["truck_untested"]["status"] == "completed"
    parcel_untested_started = test_status["parcel_untested"]["status"] != "not_started"
    
    print(f"\n🎯 NEXT ACTIONS:")
    if not parcel_untested_started and (parcel_failed_done or truck_untested_done):
        print(f"  🚀 Ready to start Parcel Untested test")
    elif not parcel_untested_started:
        print(f"  ⏳ Waiting for current tests to complete before starting Parcel Untested")
    elif test_status["parcel_untested"]["status"] == "in_progress":
        print(f"  🔄 Parcel Untested test is running")
    elif all(status["status"] == "completed" for status in test_status.values()):
        print(f"  🎉 ALL TESTS COMPLETED!")
    else:
        print(f"  🔄 Tests in progress...")
    
    print(f"{'='*80}")

def main():
    """Main monitoring loop"""
    
    print(f"🕐 STARTING HOURLY MONITORING SYSTEM")
    print(f"📋 Will check progress every hour and auto-start remaining tests")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    while True:
        try:
            # Check current status
            running_processes, pids = check_process_status()
            test_status = get_test_progress()
            
            # Print status report
            print_status_report(test_status, running_processes)
            
            # Check if we should start Parcel Untested
            parcel_failed_done = test_status["parcel_failed"]["status"] == "completed"
            truck_untested_done = test_status["truck_untested"]["status"] == "completed"
            parcel_untested_not_started = test_status["parcel_untested"]["status"] == "not_started"
            
            if parcel_untested_not_started and (parcel_failed_done or truck_untested_done):
                print(f"\n🚀 CONDITIONS MET: Starting Parcel Untested test...")
                if start_parcel_untested():
                    print(f"✅ Parcel Untested test started successfully!")
                else:
                    print(f"❌ Failed to start Parcel Untested test")
            
            # Check if all tests are completed
            all_completed = all(status["status"] == "completed" for status in test_status.values())
            if all_completed:
                print(f"\n🎉 ALL TESTS COMPLETED! Monitoring system will exit.")
                break
            
            # Wait for 1 hour (3600 seconds)
            print(f"\n⏰ Next check in 1 hour...")
            time.sleep(3600)
            
        except KeyboardInterrupt:
            print(f"\n⏹️  Monitoring stopped by user")
            break
        except Exception as e:
            print(f"\n❌ Error in monitoring loop: {e}")
            print(f"⏰ Retrying in 1 hour...")
            time.sleep(3600)

if __name__ == "__main__":
    main()
