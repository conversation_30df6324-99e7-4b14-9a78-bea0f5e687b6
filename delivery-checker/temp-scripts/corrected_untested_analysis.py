#!/usr/bin/env python3
"""
Corrected analysis to find truly untested zipcodes
"""

import os
import pandas as pd
import re
from collections import defaultdict

def get_all_tested_zipcodes_from_logs():
    """Get all zipcodes that have been tested by scanning log files directly"""
    
    print("🔍 SCANNING ALL LOG FILES FOR TESTED ZIPCODES")
    print("=" * 60)
    
    results_dir = "results"
    parcel_tested = set()
    truck_tested = set()
    
    if not os.path.exists(results_dir):
        print(f"❌ Results directory not found: {results_dir}")
        return parcel_tested, truck_tested
    
    # Get all test directories
    test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
    test_dirs.sort()
    
    print(f"📁 Found {len(test_dirs)} test directories")
    
    for test_dir in test_dirs:
        full_path = os.path.join(results_dir, test_dir)
        
        # Find log file
        log_files = [f for f in os.listdir(full_path) if f.endswith('.log')]
        if not log_files:
            continue
        
        log_path = os.path.join(full_path, log_files[0])
        
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            # Determine test type from item ID in log
            is_parcel = "Item ID: 10534224" in log_content
            is_truck = "Item ID: 70570836" in log_content
            
            if not (is_parcel or is_truck):
                print(f"  ⚠️  Skipping {test_dir}: Unknown item ID")
                continue
            
            delivery_type = "Parcel" if is_parcel else "Truck"
            current_tested = parcel_tested if is_parcel else truck_tested
            
            # Extract all tested zipcodes from log using regex
            # Pattern: "Row X: ZIPCODE - SUCCESS" or "Row X: ZIPCODE - FAILED"
            zipcode_pattern = r'Row \d+: (\d{6}) - (SUCCESS|FAILED)'
            matches = re.findall(zipcode_pattern, log_content)
            
            test_count = 0
            for zipcode, result in matches:
                formatted_zipcode = str(zipcode).zfill(6)
                current_tested.add(formatted_zipcode)
                test_count += 1
            
            print(f"  📊 {delivery_type} test {test_dir}: {test_count} zipcodes tested")
        
        except Exception as e:
            print(f"  ⚠️  Error processing {test_dir}: {e}")
            continue
    
    print(f"\n📊 TESTED ZIPCODES SUMMARY (from logs):")
    print(f"  📦 Parcel tested: {len(parcel_tested):,} unique zipcodes")
    print(f"  🚛 Truck tested: {len(truck_tested):,} unique zipcodes")
    
    return parcel_tested, truck_tested

def verify_specific_zipcode(zipcode, parcel_tested, truck_tested):
    """Verify if a specific zipcode was actually tested"""
    
    print(f"\n🔍 VERIFYING ZIPCODE {zipcode}:")
    
    # Check in our sets
    parcel_in_set = zipcode in parcel_tested
    truck_in_set = zipcode in truck_tested
    
    print(f"  📦 Parcel tested (from analysis): {parcel_in_set}")
    print(f"  🚛 Truck tested (from analysis): {truck_in_set}")
    
    # Double-check by searching logs directly
    results_dir = "results"
    parcel_found_in_logs = []
    truck_found_in_logs = []
    
    test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
    
    for test_dir in test_dirs:
        full_path = os.path.join(results_dir, test_dir)
        log_files = [f for f in os.listdir(full_path) if f.endswith('.log')]
        
        if not log_files:
            continue
        
        log_path = os.path.join(full_path, log_files[0])
        
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            if zipcode in log_content:
                is_parcel = "Item ID: 10534224" in log_content
                is_truck = "Item ID: 70570836" in log_content
                
                if is_parcel:
                    parcel_found_in_logs.append(test_dir)
                elif is_truck:
                    truck_found_in_logs.append(test_dir)
        
        except Exception as e:
            continue
    
    print(f"  📦 Parcel found in logs: {len(parcel_found_in_logs)} tests")
    for test in parcel_found_in_logs[:5]:  # Show first 5
        print(f"    - {test}")
    
    print(f"  🚛 Truck found in logs: {len(truck_found_in_logs)} tests")
    for test in truck_found_in_logs[:5]:  # Show first 5
        print(f"    - {test}")

def find_truly_untested_samples():
    """Find truly untested zipcode samples with verification"""
    
    print("\n📋 FINDING TRULY UNTESTED ZIPCODE SAMPLES")
    print("=" * 60)
    
    # Load masterfile
    masterfile_path = "/Users/<USER>/Work/etc/delivery-checker/masterfile-0805.xlsx"
    
    try:
        df = pd.read_excel(masterfile_path)
        print(f"✅ Loaded masterfile: {len(df):,} rows")
        
        # Get all unique zipcodes from masterfile
        all_zipcodes = set(str(zipcode).zfill(6) for zipcode in df['ZipCode'].unique())
        print(f"📊 Total unique zipcodes in masterfile: {len(all_zipcodes):,}")
        
    except Exception as e:
        print(f"❌ Error loading masterfile: {e}")
        return
    
    # Get tested zipcodes from logs
    parcel_tested, truck_tested = get_all_tested_zipcodes_from_logs()
    
    # Verify the problematic zipcode 666315
    verify_specific_zipcode("666315", parcel_tested, truck_tested)
    
    # Find truly untested zipcodes
    parcel_untested = all_zipcodes - parcel_tested
    truck_untested = all_zipcodes - truck_tested
    
    print(f"\n🎯 CORRECTED UNTESTED ZIPCODE ANALYSIS:")
    print(f"  📦 Parcel untested: {len(parcel_untested):,} zipcodes")
    print(f"  🚛 Truck untested: {len(truck_untested):,} zipcodes")
    
    # Show samples of truly untested
    if parcel_untested:
        print(f"\n📋 PARCEL TRULY UNTESTED SAMPLES (first 20):")
        parcel_samples = sorted(list(parcel_untested))[:20]
        for i, zipcode in enumerate(parcel_samples, 1):
            print(f"  {i:2d}. {zipcode}")
    else:
        print(f"\n📦 ✅ ALL PARCEL ZIPCODES HAVE BEEN TESTED!")
    
    if truck_untested:
        print(f"\n📋 TRUCK TRULY UNTESTED SAMPLES (first 20):")
        truck_samples = sorted(list(truck_untested))[:20]
        for i, zipcode in enumerate(truck_samples, 1):
            print(f"  {i:2d}. {zipcode}")
        
        # Verify a few truck samples
        print(f"\n🔍 VERIFYING TRUCK UNTESTED SAMPLES:")
        for zipcode in truck_samples[:3]:
            verify_specific_zipcode(zipcode, parcel_tested, truck_tested)
    else:
        print(f"\n🚛 ✅ ALL TRUCK ZIPCODES HAVE BEEN TESTED!")
    
    # Coverage analysis
    print(f"\n📊 CORRECTED COVERAGE ANALYSIS:")
    print(f"  📦 Parcel Coverage: {(len(parcel_tested)/len(all_zipcodes))*100:.1f}% ({len(parcel_tested):,}/{len(all_zipcodes):,})")
    print(f"  🚛 Truck Coverage: {(len(truck_tested)/len(all_zipcodes))*100:.1f}% ({len(truck_tested):,}/{len(all_zipcodes):,})")

def main():
    """Main function"""
    
    print("🔍 CORRECTED UNTESTED ZIPCODE ANALYSIS")
    print("=" * 70)
    print("This script will:")
    print("1. Scan ALL log files directly for tested zipcodes")
    print("2. Verify specific zipcodes that were incorrectly classified")
    print("3. Provide truly untested samples")
    print("4. Generate corrected coverage analysis")
    print("=" * 70)
    
    find_truly_untested_samples()
    
    print(f"\n🎉 CORRECTED ANALYSIS COMPLETE!")
    print("=" * 70)

if __name__ == "__main__":
    main()
