#!/usr/bin/env python3
"""
Check the structure of Excel result files to understand the data format
"""

import pandas as pd
import os

def check_excel_structure():
    """Check the structure of Excel result files"""
    
    print("🔍 CHECKING EXCEL FILE STRUCTURE")
    print("=" * 50)
    
    # Find a recent Excel result file
    results_dir = "results"
    test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
    test_dirs.sort(reverse=True)  # Get most recent first
    
    for test_dir in test_dirs[:3]:  # Check first 3
        full_path = os.path.join(results_dir, test_dir)
        
        # Find Excel results file
        excel_files = [f for f in os.listdir(full_path) if f.endswith('.xlsx') and 'results' in f]
        if not excel_files:
            continue
        
        excel_path = os.path.join(full_path, excel_files[0])
        
        print(f"\n📊 CHECKING: {test_dir}")
        print(f"📁 File: {excel_files[0]}")
        
        try:
            # Read just the first few rows to check structure
            df = pd.read_excel(excel_path, nrows=5)
            
            print(f"📋 Columns ({len(df.columns)}):")
            for col in df.columns:
                print(f"  - {col}")
            
            print(f"\n📊 Sample data (first row):")
            if len(df) > 0:
                first_row = df.iloc[0]
                for col in df.columns:
                    value = first_row[col]
                    print(f"  {col}: {value}")
            
            # Check if this has the expected columns
            expected_cols = ['tw_delivery_success', 'tw_timewindows_success', 'tw_available_dates', 'tw_error_details']
            missing_cols = [col for col in expected_cols if col not in df.columns]
            
            if missing_cols:
                print(f"❌ Missing expected columns: {missing_cols}")
            else:
                print(f"✅ Has all expected columns")
            
            break  # Found a good file, stop checking
            
        except Exception as e:
            print(f"❌ Error reading {excel_path}: {e}")
            continue
    
    # Also check what columns are actually in our comprehensive files
    print(f"\n🔍 CHECKING COMPREHENSIVE RESULT FILES:")
    
    try:
        parcel_df = pd.read_excel('parcel_final_comprehensive_results.xlsx', nrows=1)
        print(f"\n📦 PARCEL COMPREHENSIVE COLUMNS:")
        for col in parcel_df.columns:
            if 'Parcel' in col:
                print(f"  - {col}")
    except Exception as e:
        print(f"❌ Error reading parcel comprehensive: {e}")

def main():
    """Main function"""
    check_excel_structure()

if __name__ == "__main__":
    main()
