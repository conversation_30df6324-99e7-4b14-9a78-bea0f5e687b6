#!/usr/bin/env python3
"""
Check the status of all remaining zipcodes across all test categories
"""

import os
import json
import pandas as pd

def check_all_remaining_zipcodes():
    """Check the status of all remaining zipcodes"""
    
    print("🔍 CHECKING ALL REMAINING ZIPCODE STATUS")
    print("=" * 70)
    
    # Define all test categories and their files
    test_categories = {
        "📦 Parcel Failed": {
            "file": "parcel_failed_only_fixed.xlsx",
            "total_rows": 13668,
            "current_test_dir": "results/timewindows_test_20250806_000942",
            "item_id": "10534224"
        },
        "📦 Parcel Untested": {
            "file": "parcel_untested_only_fixed.xlsx", 
            "total_rows": 2298,
            "current_test_dir": None,
            "item_id": "10534224"
        },
        "🚛 Truck Failed": {
            "file": "truck_failed_only_fixed.xlsx",
            "total_rows": 4,
            "current_test_dir": "completed",  # Already completed
            "item_id": "70570836"
        },
        "🚛 Truck Untested": {
            "file": "truck_untested_only_fixed.xlsx",
            "total_rows": 18071,
            "current_test_dir": "results/timewindows_test_20250806_002307",
            "item_id": "70570836"
        }
    }
    
    total_remaining = 0
    total_completed = 0
    total_in_progress = 0
    
    for category, info in test_categories.items():
        print(f"\n{category}:")
        print(f"  📁 File: {info['file']}")
        print(f"  📊 Total rows: {info['total_rows']:,}")
        
        if info['current_test_dir'] == "completed":
            print(f"  ✅ Status: COMPLETED")
            total_completed += info['total_rows']
            continue
        elif info['current_test_dir'] is None:
            print(f"  ❌ Status: NOT STARTED")
            total_remaining += info['total_rows']
            continue
        
        # Check current test progress
        test_dir = info['current_test_dir']
        if os.path.exists(test_dir):
            # Check log file for progress
            log_file = f"{test_dir}/{os.path.basename(test_dir)}.log"
            if os.path.exists(log_file):
                last_row = None
                success_count = 0
                failed_count = 0
                
                with open(log_file, 'r') as f:
                    for line in f:
                        if "Row " in line and (" - SUCCESS" in line or " - FAILED" in line):
                            try:
                                row_part = line.split("Row ")[1].split(":")[0]
                                row_num = int(row_part)
                                last_row = row_num
                                
                                if " - SUCCESS" in line:
                                    success_count += 1
                                else:
                                    failed_count += 1
                            except:
                                pass
                
                if last_row is not None:
                    completed_rows = last_row + 1
                    remaining_rows = info['total_rows'] - completed_rows
                    progress_pct = (completed_rows / info['total_rows']) * 100
                    
                    print(f"  🔄 Status: IN PROGRESS")
                    print(f"  📊 Completed: {completed_rows:,} / {info['total_rows']:,} ({progress_pct:.1f}%)")
                    print(f"  📊 Remaining: {remaining_rows:,}")
                    print(f"  ✅ Success: {success_count:,}")
                    print(f"  ❌ Failed: {failed_count:,}")
                    
                    total_completed += completed_rows
                    total_remaining += remaining_rows
                    total_in_progress += 1
                else:
                    print(f"  ⚠️  Status: STARTED but no progress found")
                    total_remaining += info['total_rows']
            else:
                print(f"  ⚠️  Status: Directory exists but no log file")
                total_remaining += info['total_rows']
        else:
            print(f"  ❌ Status: NOT STARTED")
            total_remaining += info['total_rows']
    
    # Summary
    print(f"\n🎯 OVERALL SUMMARY:")
    print(f"=" * 70)
    print(f"  ✅ Total Completed: {total_completed:,} zipcodes")
    print(f"  🔄 Total In Progress: {total_in_progress} test(s)")
    print(f"  ❌ Total Remaining: {total_remaining:,} zipcodes")
    print(f"  📊 Grand Total: {total_completed + total_remaining:,} zipcodes")
    
    completion_pct = (total_completed / (total_completed + total_remaining)) * 100
    print(f"  📈 Overall Progress: {completion_pct:.1f}% complete")
    
    # Breakdown by delivery type
    print(f"\n📦 PARCEL DELIVERY STATUS:")
    parcel_total = test_categories["📦 Parcel Failed"]["total_rows"] + test_categories["📦 Parcel Untested"]["total_rows"]
    print(f"  📊 Total Parcel Zipcodes: {parcel_total:,}")
    
    print(f"\n🚛 TRUCK DELIVERY STATUS:")
    truck_total = test_categories["🚛 Truck Failed"]["total_rows"] + test_categories["🚛 Truck Untested"]["total_rows"]
    print(f"  📊 Total Truck Zipcodes: {truck_total:,}")
    
    # What's not invoked yet
    print(f"\n❌ NOT YET INVOKED:")
    not_invoked = []
    for category, info in test_categories.items():
        if info['current_test_dir'] is None:
            not_invoked.append(f"  • {category}: {info['total_rows']:,} zipcodes")
    
    if not_invoked:
        for item in not_invoked:
            print(item)
    else:
        print(f"  🎉 All zipcode categories have been invoked!")
    
    print(f"=" * 70)

def main():
    """Main function"""
    check_all_remaining_zipcodes()

if __name__ == "__main__":
    main()
