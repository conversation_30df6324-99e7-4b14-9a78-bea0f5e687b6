#!/usr/bin/env python3
"""
Generate final comprehensive result files with correct aggregation logic:
- SUCCESS: If time window test succeeded at least once
- FAILED: If failed with error code (no network issues) 
- UNTESTED: If failed due to network issues or never executed
"""

import os
import json
import pandas as pd
import re
from collections import defaultdict

def load_masterfile():
    """Load the masterfile as template"""
    
    print("📋 LOADING MASTERFILE TEMPLATE")
    print("=" * 60)
    
    masterfile_path = "/Users/<USER>/Work/etc/delivery-checker/masterfile-0805.xlsx"
    
    try:
        df = pd.read_excel(masterfile_path)
        print(f"✅ Loaded masterfile: {len(df):,} rows, {len(df.columns)} columns")
        print(f"📊 Unique zipcodes: {df['ZipCode'].nunique():,}")
        return df
    except Exception as e:
        print(f"❌ Error loading masterfile: {e}")
        return None

def analyze_all_test_results_properly():
    """Properly analyze all test results with correct aggregation logic"""
    
    print("\n🔍 ANALYZING ALL TEST RESULTS WITH PROPER LOGIC")
    print("=" * 60)
    
    results_dir = "results"
    parcel_results = defaultdict(lambda: {
        'status': 'UNTESTED', 
        'tests': [], 
        'best_success': None,
        'latest_test': None,
        'total_tests': 0
    })
    truck_results = defaultdict(lambda: {
        'status': 'UNTESTED', 
        'tests': [], 
        'best_success': None,
        'latest_test': None,
        'total_tests': 0
    })
    
    if not os.path.exists(results_dir):
        print(f"❌ Results directory not found: {results_dir}")
        return parcel_results, truck_results
    
    # Get all test directories
    test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
    test_dirs.sort()
    
    print(f"📁 Found {len(test_dirs)} test directories to analyze")
    
    for test_dir in test_dirs:
        full_path = os.path.join(results_dir, test_dir)
        
        # Find Excel results file (most reliable source)
        excel_files = [f for f in os.listdir(full_path) if f.endswith('.xlsx') and 'results' in f]
        if not excel_files:
            continue
        
        excel_path = os.path.join(full_path, excel_files[0])
        
        # Determine test type from log
        log_files = [f for f in os.listdir(full_path) if f.endswith('.log')]
        if not log_files:
            continue
        
        log_path = os.path.join(full_path, log_files[0])
        
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                first_chunk = f.read(2000)
            
            is_parcel = "Item ID: 10534224" in first_chunk
            is_truck = "Item ID: 70570836" in first_chunk
            
            if not (is_parcel or is_truck):
                print(f"  ⚠️  Skipping {test_dir}: Unknown item ID")
                continue
            
            delivery_type = "Parcel" if is_parcel else "Truck"
            current_results = parcel_results if is_parcel else truck_results
            
            print(f"  📊 Processing {delivery_type} test: {test_dir}")
            
            # Read Excel results
            df = pd.read_excel(excel_path)
            processed_count = 0
            
            for _, row in df.iterrows():
                zipcode = str(row['ZipCode']).zfill(6)
                
                # Extract result information
                delivery_success = row.get('tw_delivery_success', False)
                timewindows_success = row.get('tw_timewindows_success', False)
                available_dates = row.get('tw_available_dates', '')
                error_details = str(row.get('tw_error_details', ''))
                api_response = row.get('tw_api_response', '')
                response_time = row.get('tw_delivery_response_time', 0)
                
                # Convert boolean values properly
                if delivery_success == 'True' or delivery_success == True or delivery_success == 1:
                    delivery_success = True
                else:
                    delivery_success = False
                
                if timewindows_success == 'True' or timewindows_success == True or timewindows_success == 1:
                    timewindows_success = True
                else:
                    timewindows_success = False
                
                # Create test result record
                test_result = {
                    'test_dir': test_dir,
                    'delivery_success': delivery_success,
                    'timewindows_success': timewindows_success,
                    'available_dates': str(available_dates),
                    'error_details': error_details,
                    'api_response': str(api_response),
                    'response_time': float(response_time) if response_time else 0
                }
                
                # Classify this specific test result
                if timewindows_success:
                    # SUCCESS: Time windows available
                    test_status = 'SUCCESS'
                elif delivery_success:
                    # Check if it's a real error or network issue
                    if any(network_error in error_details.lower() for network_error in 
                           ['timeout', 'connection', 'network', 'unreachable', 'dns', 'timed out']):
                        test_status = 'UNTESTED'  # Network issue
                    else:
                        test_status = 'FAILED'  # Real business error (like no capacity)
                else:
                    # No delivery success - check if it's network or business error
                    if any(network_error in error_details.lower() for network_error in 
                           ['timeout', 'connection', 'network', 'unreachable', 'dns', 'timed out']):
                        test_status = 'UNTESTED'  # Network issue
                    elif error_details and error_details.strip() and error_details != 'nan':
                        test_status = 'FAILED'  # Real business error
                    else:
                        test_status = 'UNTESTED'  # No clear result
                
                test_result['test_status'] = test_status
                
                # Add to zipcode history
                current_results[zipcode]['tests'].append(test_result)
                current_results[zipcode]['total_tests'] += 1
                current_results[zipcode]['latest_test'] = test_result
                
                # Update overall status (SUCCESS takes highest precedence)
                if test_status == 'SUCCESS':
                    current_results[zipcode]['status'] = 'SUCCESS'
                    if current_results[zipcode]['best_success'] is None:
                        current_results[zipcode]['best_success'] = test_result
                elif current_results[zipcode]['status'] != 'SUCCESS' and test_status == 'FAILED':
                    current_results[zipcode]['status'] = 'FAILED'
                elif current_results[zipcode]['status'] == 'UNTESTED':
                    current_results[zipcode]['status'] = test_status
                
                processed_count += 1
            
            print(f"    ✅ Processed {processed_count:,} zipcode results")
        
        except Exception as e:
            print(f"    ⚠️  Error processing {test_dir}: {e}")
            continue
    
    # Convert defaultdict to regular dict and count results
    parcel_results = dict(parcel_results)
    truck_results = dict(truck_results)
    
    print(f"\n📊 AGGREGATION SUMMARY:")
    print(f"  📦 Parcel: {len(parcel_results):,} zipcodes analyzed")
    print(f"  🚛 Truck: {len(truck_results):,} zipcodes analyzed")
    
    return parcel_results, truck_results

def create_final_comprehensive_file(masterfile_df, aggregated_results, delivery_type):
    """Create final comprehensive result file with proper aggregation"""
    
    print(f"\n📝 CREATING FINAL {delivery_type.upper()} COMPREHENSIVE FILE")
    print("=" * 60)
    
    # Create a copy of masterfile
    result_df = masterfile_df.copy()
    
    # Add result columns
    result_df[f'{delivery_type}_Final_Status'] = 'UNTESTED'
    result_df[f'{delivery_type}_Available_Dates'] = ''
    result_df[f'{delivery_type}_Error_Details'] = ''
    result_df[f'{delivery_type}_Response_Time_ms'] = 0.0
    result_df[f'{delivery_type}_Total_Tests'] = 0
    result_df[f'{delivery_type}_Last_Test_Dir'] = ''
    result_df[f'{delivery_type}_Success_Test_Dir'] = ''
    
    # Count results
    success_count = 0
    failed_count = 0
    untested_count = 0
    
    for idx, row in result_df.iterrows():
        zipcode = str(row['ZipCode']).zfill(6)
        
        if zipcode in aggregated_results:
            zipcode_data = aggregated_results[zipcode]
            
            # Set the aggregated status
            result_df.at[idx, f'{delivery_type}_Final_Status'] = zipcode_data['status']
            result_df.at[idx, f'{delivery_type}_Total_Tests'] = zipcode_data['total_tests']
            
            # Use best success result if available, otherwise latest test
            best_result = zipcode_data['best_success'] or zipcode_data['latest_test']
            
            if best_result:
                result_df.at[idx, f'{delivery_type}_Available_Dates'] = best_result.get('available_dates', '')
                result_df.at[idx, f'{delivery_type}_Error_Details'] = best_result.get('error_details', '')
                result_df.at[idx, f'{delivery_type}_Response_Time_ms'] = best_result.get('response_time', 0)
                result_df.at[idx, f'{delivery_type}_Last_Test_Dir'] = zipcode_data['latest_test'].get('test_dir', '')
                
                if zipcode_data['best_success']:
                    result_df.at[idx, f'{delivery_type}_Success_Test_Dir'] = zipcode_data['best_success'].get('test_dir', '')
            
            # Count by status
            if zipcode_data['status'] == 'SUCCESS':
                success_count += 1
            elif zipcode_data['status'] == 'FAILED':
                failed_count += 1
            else:
                untested_count += 1
        else:
            untested_count += 1
    
    # Save the result file
    output_file = f'{delivery_type.lower()}_final_comprehensive_results.xlsx'
    
    # Create Excel writer with formatting
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        result_df.to_excel(writer, sheet_name='Results', index=False)
        
        # Get the workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Results']
        
        # Apply conditional formatting for status column
        from openpyxl.styles import PatternFill
        from openpyxl.formatting.rule import CellIsRule
        
        # Find the status column
        status_col = None
        for col_idx, col in enumerate(result_df.columns, 1):
            if col.endswith('_Final_Status'):
                status_col = col_idx
                break
        
        if status_col:
            # Green for SUCCESS
            green_fill = PatternFill(start_color='90EE90', end_color='90EE90', fill_type='solid')
            worksheet.conditional_formatting.add(
                f'{chr(64+status_col)}2:{chr(64+status_col)}{len(result_df)+1}',
                CellIsRule(operator='equal', formula=['"SUCCESS"'], fill=green_fill)
            )
            
            # Red for FAILED
            red_fill = PatternFill(start_color='FFB6C1', end_color='FFB6C1', fill_type='solid')
            worksheet.conditional_formatting.add(
                f'{chr(64+status_col)}2:{chr(64+status_col)}{len(result_df)+1}',
                CellIsRule(operator='equal', formula=['"FAILED"'], fill=red_fill)
            )
            
            # Gray for UNTESTED
            gray_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
            worksheet.conditional_formatting.add(
                f'{chr(64+status_col)}2:{chr(64+status_col)}{len(result_df)+1}',
                CellIsRule(operator='equal', formula=['"UNTESTED"'], fill=gray_fill)
            )
    
    print(f"✅ Created {output_file}")
    print(f"📊 Final Results Summary:")
    print(f"  ✅ SUCCESS: {success_count:,} ({success_count/len(result_df)*100:.1f}%)")
    print(f"  ❌ FAILED: {failed_count:,} ({failed_count/len(result_df)*100:.1f}%)")
    print(f"  ⚪ UNTESTED: {untested_count:,} ({untested_count/len(result_df)*100:.1f}%)")
    
    return output_file, {
        'success': success_count,
        'failed': failed_count,
        'untested': untested_count,
        'total': len(result_df)
    }

def main():
    """Main function"""
    
    print("🎯 GENERATING FINAL COMPREHENSIVE RESULT FILES")
    print("=" * 70)
    print("This script will:")
    print("1. Load masterfile as template")
    print("2. Properly analyze ALL test execution histories")
    print("3. Apply correct classification logic:")
    print("   - SUCCESS: Time window test succeeded at least once")
    print("   - FAILED: Failed with business error (no network issues)")
    print("   - UNTESTED: Failed due to network issues or never executed")
    print("4. Create final comprehensive result files")
    print("=" * 70)
    
    # Step 1: Load masterfile
    masterfile_df = load_masterfile()
    if masterfile_df is None:
        return
    
    # Step 2: Analyze all test results properly
    parcel_results, truck_results = analyze_all_test_results_properly()
    
    # Step 3: Create final comprehensive result files
    parcel_file, parcel_stats = create_final_comprehensive_file(masterfile_df, parcel_results, 'Parcel')
    truck_file, truck_stats = create_final_comprehensive_file(masterfile_df, truck_results, 'Truck')
    
    # Step 4: Summary
    print(f"\n🎉 FINAL COMPREHENSIVE RESULT FILES CREATED!")
    print("=" * 70)
    print(f"📦 PARCEL RESULTS: {parcel_file}")
    print(f"  ✅ Success: {parcel_stats['success']:,}")
    print(f"  ❌ Failed: {parcel_stats['failed']:,}")
    print(f"  ⚪ Untested: {parcel_stats['untested']:,}")
    
    print(f"\n🚛 TRUCK RESULTS: {truck_file}")
    print(f"  ✅ Success: {truck_stats['success']:,}")
    print(f"  ❌ Failed: {truck_stats['failed']:,}")
    print(f"  ⚪ Untested: {truck_stats['untested']:,}")
    
    print(f"\n💡 FINAL CLASSIFICATION LOGIC APPLIED:")
    print(f"  ✅ SUCCESS: Time windows available (at least once)")
    print(f"  ❌ FAILED: Business error (no capacity, invalid zipcode, etc.)")
    print(f"  ⚪ UNTESTED: Network issues or never executed")
    print("=" * 70)

if __name__ == "__main__":
    main()
