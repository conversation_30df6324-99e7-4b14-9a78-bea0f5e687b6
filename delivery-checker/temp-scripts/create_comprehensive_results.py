#!/usr/bin/env python3
"""
Create comprehensive result files using masterfile as template
with time window information for both truck and parcel deliveries
"""

import os
import json
import pandas as pd
import re
from collections import defaultdict
from datetime import datetime

def load_masterfile():
    """Load the masterfile as template"""
    
    print("📋 LOADING MASTERFILE TEMPLATE")
    print("=" * 60)
    
    masterfile_path = "/Users/<USER>/Work/etc/delivery-checker/masterfile-0805.xlsx"
    
    try:
        df = pd.read_excel(masterfile_path)
        print(f"✅ Loaded masterfile: {len(df):,} rows, {len(df.columns)} columns")
        print(f"📊 Unique zipcodes: {df['ZipCode'].nunique():,}")
        return df
    except Exception as e:
        print(f"❌ Error loading masterfile: {e}")
        return None

def extract_test_results_from_logs():
    """Extract all test results from historical logs"""
    
    print("\n🔍 EXTRACTING TEST RESULTS FROM ALL LOGS")
    print("=" * 60)
    
    results_dir = "results"
    parcel_results = {}  # zipcode -> result_data
    truck_results = {}   # zipcode -> result_data
    
    if not os.path.exists(results_dir):
        print(f"❌ Results directory not found: {results_dir}")
        return parcel_results, truck_results
    
    # Get all test directories
    test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
    test_dirs.sort()
    
    print(f"📁 Found {len(test_dirs)} test directories to process")
    
    for test_dir in test_dirs:
        full_path = os.path.join(results_dir, test_dir)
        
        # Find log file
        log_files = [f for f in os.listdir(full_path) if f.endswith('.log')]
        if not log_files:
            continue
        
        log_path = os.path.join(full_path, log_files[0])
        
        # Determine test type
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                first_chunk = f.read(2000)
            
            is_parcel = "Item ID: 10534224" in first_chunk
            is_truck = "Item ID: 70570836" in first_chunk
            
            if not (is_parcel or is_truck):
                continue
            
            delivery_type = "Parcel" if is_parcel else "Truck"
            print(f"  📊 Processing {delivery_type} test: {test_dir}")
            
            # Extract results from log
            with open(log_path, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            # Parse log entries
            log_lines = log_content.split('\n')
            current_results = parcel_results if is_parcel else truck_results
            
            for line in log_lines:
                # Look for result lines: "Row X: ZIPCODE - SUCCESS/FAILED"
                if " - SUCCESS" in line or " - FAILED" in line:
                    try:
                        # Extract zipcode and result
                        if "Row " in line and ": " in line:
                            parts = line.split("Row ")[1].split(": ")
                            if len(parts) >= 2:
                                zipcode_part = parts[1].split(" - ")[0]
                                zipcode = str(zipcode_part).zfill(6)
                                
                                result_status = "SUCCESS" if " - SUCCESS" in line else "FAILED"
                                
                                # Initialize result data
                                if zipcode not in current_results:
                                    current_results[zipcode] = {
                                        'status': result_status,
                                        'available_dates': [],
                                        'error_details': '',
                                        'test_timestamp': test_dir.split('_')[-1] if '_' in test_dir else '',
                                        'response_time': 0
                                    }
                                
                                # Update with latest result (SUCCESS takes precedence)
                                if result_status == "SUCCESS" or current_results[zipcode]['status'] != "SUCCESS":
                                    current_results[zipcode]['status'] = result_status
                                    current_results[zipcode]['test_timestamp'] = test_dir.split('_')[-1] if '_' in test_dir else ''
                    
                    except Exception as e:
                        continue
                
                # Look for time slots information
                elif "Time slots available:" in line:
                    try:
                        # Extract zipcode from previous context and dates
                        dates_part = line.split("Time slots available: ")[1].strip()
                        # Find the most recent zipcode processed
                        for zipcode in current_results:
                            if current_results[zipcode]['test_timestamp'] == test_dir.split('_')[-1]:
                                if dates_part not in current_results[zipcode]['available_dates']:
                                    current_results[zipcode]['available_dates'].append(dates_part)
                                break
                    except:
                        continue
        
        except Exception as e:
            print(f"  ⚠️  Error processing {test_dir}: {e}")
            continue
    
    print(f"✅ Extracted results:")
    print(f"  📦 Parcel: {len(parcel_results):,} zipcodes")
    print(f"  🚛 Truck: {len(truck_results):,} zipcodes")
    
    return parcel_results, truck_results

def extract_detailed_results_from_excel():
    """Extract detailed results from Excel files"""
    
    print("\n📊 EXTRACTING DETAILED RESULTS FROM EXCEL FILES")
    print("=" * 60)
    
    results_dir = "results"
    parcel_detailed = {}
    truck_detailed = {}
    
    test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
    
    for test_dir in test_dirs:
        full_path = os.path.join(results_dir, test_dir)
        
        # Find Excel results file
        excel_files = [f for f in os.listdir(full_path) if f.endswith('.xlsx') and 'results' in f]
        if not excel_files:
            continue
        
        excel_path = os.path.join(full_path, excel_files[0])
        
        try:
            df = pd.read_excel(excel_path)
            
            # Determine test type from log
            log_files = [f for f in os.listdir(full_path) if f.endswith('.log')]
            if log_files:
                log_path = os.path.join(full_path, log_files[0])
                with open(log_path, 'r', encoding='utf-8') as f:
                    first_chunk = f.read(1000)
                
                is_parcel = "Item ID: 10534224" in first_chunk
                is_truck = "Item ID: 70570836" in first_chunk
                
                if is_parcel:
                    delivery_type = "Parcel"
                    current_detailed = parcel_detailed
                elif is_truck:
                    delivery_type = "Truck"
                    current_detailed = truck_detailed
                else:
                    continue
                
                print(f"  📊 Processing {delivery_type} Excel: {excel_files[0]} ({len(df)} rows)")
                
                # Process each row
                for _, row in df.iterrows():
                    zipcode = str(row['ZipCode']).zfill(6)
                    
                    # Extract detailed information
                    result_data = {
                        'delivery_success': row.get('tw_delivery_success', 0),
                        'timewindows_success': row.get('tw_timewindows_success', 0),
                        'available_dates': row.get('tw_available_dates', ''),
                        'response_time': row.get('tw_delivery_response_time', 0),
                        'error_details': row.get('tw_error_details', ''),
                        'api_response': row.get('tw_api_response', ''),
                        'test_timestamp': test_dir.split('_')[-1] if '_' in test_dir else ''
                    }
                    
                    # Update with latest/best result
                    if zipcode not in current_detailed or result_data['timewindows_success'] == 1:
                        current_detailed[zipcode] = result_data
        
        except Exception as e:
            print(f"  ⚠️  Error processing Excel {excel_path}: {e}")
            continue
    
    print(f"✅ Extracted detailed results:")
    print(f"  📦 Parcel: {len(parcel_detailed):,} zipcodes")
    print(f"  🚛 Truck: {len(truck_detailed):,} zipcodes")
    
    return parcel_detailed, truck_detailed

def create_comprehensive_result_file(masterfile_df, detailed_results, delivery_type):
    """Create comprehensive result file with masterfile as template"""
    
    print(f"\n📝 CREATING {delivery_type.upper()} COMPREHENSIVE RESULT FILE")
    print("=" * 60)
    
    # Create a copy of masterfile
    result_df = masterfile_df.copy()
    
    # Add time window result columns
    result_df[f'{delivery_type}_TW_Delivery_Success'] = 0
    result_df[f'{delivery_type}_TW_TimeWindows_Success'] = 0
    result_df[f'{delivery_type}_TW_Available_Dates'] = ''
    result_df[f'{delivery_type}_TW_Response_Time_ms'] = 0
    result_df[f'{delivery_type}_TW_Error_Details'] = ''
    result_df[f'{delivery_type}_TW_Test_Status'] = 'NOT_TESTED'
    result_df[f'{delivery_type}_TW_Test_Timestamp'] = ''
    
    # Fill in the results
    success_count = 0
    failed_count = 0
    not_tested_count = 0
    
    for idx, row in result_df.iterrows():
        zipcode = str(row['ZipCode']).zfill(6)
        
        if zipcode in detailed_results:
            result_data = detailed_results[zipcode]
            
            result_df.at[idx, f'{delivery_type}_TW_Delivery_Success'] = result_data.get('delivery_success', 0)
            result_df.at[idx, f'{delivery_type}_TW_TimeWindows_Success'] = result_data.get('timewindows_success', 0)
            result_df.at[idx, f'{delivery_type}_TW_Available_Dates'] = result_data.get('available_dates', '')
            result_df.at[idx, f'{delivery_type}_TW_Response_Time_ms'] = result_data.get('response_time', 0)
            result_df.at[idx, f'{delivery_type}_TW_Error_Details'] = result_data.get('error_details', '')
            result_df.at[idx, f'{delivery_type}_TW_Test_Timestamp'] = result_data.get('test_timestamp', '')
            
            # Determine overall status
            if result_data.get('timewindows_success', 0) == 1:
                result_df.at[idx, f'{delivery_type}_TW_Test_Status'] = 'SUCCESS'
                success_count += 1
            elif result_data.get('delivery_success', 0) == 1:
                result_df.at[idx, f'{delivery_type}_TW_Test_Status'] = 'DELIVERY_SUCCESS_NO_TIMEWINDOWS'
                failed_count += 1
            else:
                result_df.at[idx, f'{delivery_type}_TW_Test_Status'] = 'FAILED'
                failed_count += 1
        else:
            result_df.at[idx, f'{delivery_type}_TW_Test_Status'] = 'NOT_TESTED'
            not_tested_count += 1
    
    # Save the result file
    output_file = f'{delivery_type.lower()}_comprehensive_timewindows_results.xlsx'
    
    # Create Excel writer with formatting
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        result_df.to_excel(writer, sheet_name='Results', index=False)
        
        # Get the workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Results']
        
        # Apply conditional formatting for status column
        from openpyxl.styles import PatternFill
        from openpyxl.formatting.rule import CellIsRule
        
        # Find the status column
        status_col = None
        for col_idx, col in enumerate(result_df.columns, 1):
            if col.endswith('_TW_Test_Status'):
                status_col = col_idx
                break
        
        if status_col:
            # Green for SUCCESS
            green_fill = PatternFill(start_color='90EE90', end_color='90EE90', fill_type='solid')
            worksheet.conditional_formatting.add(
                f'{chr(64+status_col)}2:{chr(64+status_col)}{len(result_df)+1}',
                CellIsRule(operator='equal', formula=['"SUCCESS"'], fill=green_fill)
            )
            
            # Yellow for DELIVERY_SUCCESS_NO_TIMEWINDOWS
            yellow_fill = PatternFill(start_color='FFFF99', end_color='FFFF99', fill_type='solid')
            worksheet.conditional_formatting.add(
                f'{chr(64+status_col)}2:{chr(64+status_col)}{len(result_df)+1}',
                CellIsRule(operator='equal', formula=['"DELIVERY_SUCCESS_NO_TIMEWINDOWS"'], fill=yellow_fill)
            )
            
            # Red for FAILED
            red_fill = PatternFill(start_color='FFB6C1', end_color='FFB6C1', fill_type='solid')
            worksheet.conditional_formatting.add(
                f'{chr(64+status_col)}2:{chr(64+status_col)}{len(result_df)+1}',
                CellIsRule(operator='equal', formula=['"FAILED"'], fill=red_fill)
            )
            
            # Gray for NOT_TESTED
            gray_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
            worksheet.conditional_formatting.add(
                f'{chr(64+status_col)}2:{chr(64+status_col)}{len(result_df)+1}',
                CellIsRule(operator='equal', formula=['"NOT_TESTED"'], fill=gray_fill)
            )
    
    print(f"✅ Created {output_file}")
    print(f"📊 Results Summary:")
    print(f"  ✅ SUCCESS: {success_count:,} ({success_count/len(result_df)*100:.1f}%)")
    print(f"  🟡 DELIVERY_SUCCESS_NO_TIMEWINDOWS: {failed_count:,} ({failed_count/len(result_df)*100:.1f}%)")
    print(f"  ⚪ NOT_TESTED: {not_tested_count:,} ({not_tested_count/len(result_df)*100:.1f}%)")
    
    return output_file, {
        'success': success_count,
        'failed': failed_count,
        'not_tested': not_tested_count,
        'total': len(result_df)
    }

def main():
    """Main function"""
    
    print("🎯 CREATING COMPREHENSIVE RESULT FILES")
    print("=" * 70)
    print("This script will:")
    print("1. Load masterfile as template")
    print("2. Extract all test results from logs and Excel files")
    print("3. Create comprehensive result files for Parcel and Truck")
    print("4. Include time window info and highlight errors")
    print("=" * 70)
    
    # Step 1: Load masterfile
    masterfile_df = load_masterfile()
    if masterfile_df is None:
        return
    
    # Step 2: Extract detailed results
    parcel_detailed, truck_detailed = extract_detailed_results_from_excel()
    
    # Step 3: Create comprehensive result files
    parcel_file, parcel_stats = create_comprehensive_result_file(masterfile_df, parcel_detailed, 'Parcel')
    truck_file, truck_stats = create_comprehensive_result_file(masterfile_df, truck_detailed, 'Truck')
    
    # Step 4: Summary
    print(f"\n🎉 COMPREHENSIVE RESULT FILES CREATED!")
    print("=" * 70)
    print(f"📦 PARCEL RESULTS: {parcel_file}")
    print(f"  ✅ Success: {parcel_stats['success']:,}")
    print(f"  🟡 Partial: {parcel_stats['failed']:,}")
    print(f"  ⚪ Not Tested: {parcel_stats['not_tested']:,}")
    
    print(f"\n🚛 TRUCK RESULTS: {truck_file}")
    print(f"  ✅ Success: {truck_stats['success']:,}")
    print(f"  🟡 Partial: {truck_stats['failed']:,}")
    print(f"  ⚪ Not Tested: {truck_stats['not_tested']:,}")
    
    print(f"\n💡 FEATURES:")
    print(f"  📋 Based on masterfile template with all geographic details")
    print(f"  🎨 Color-coded status (Green=Success, Yellow=Partial, Red=Failed, Gray=Not Tested)")
    print(f"  📊 Includes available dates, response times, and error details")
    print(f"  🔍 Comprehensive coverage of all test results")
    print("=" * 70)

if __name__ == "__main__":
    main()
