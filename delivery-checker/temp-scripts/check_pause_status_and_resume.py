#!/usr/bin/env python3
"""
Check the status after pausing and prepare resume commands
"""

import os
import json
import pandas as pd

def check_pause_status():
    """Check the current status after pausing"""
    
    print("🛑 PAUSE STATUS CHECK")
    print("=" * 60)
    
    # Check Parcel Failed status
    parcel_dir = "results/timewindows_test_20250806_000942"
    if os.path.exists(parcel_dir):
        progress_file = f"{parcel_dir}/progress_20250806_000942.json"
        if os.path.exists(progress_file):
            with open(progress_file, 'r') as f:
                progress = json.load(f)
            
            print(f"📦 PARCEL FAILED STATUS:")
            print(f"  📊 Completed: {progress['completed_rows']:,} / {progress['total_rows']:,}")
            print(f"  📈 Progress: {progress['completion_percentage']:.1f}%")
            print(f"  ✅ Success: {progress['successful_delivery']:,}")
            print(f"  ❌ Failed: {progress['failed_delivery']:,}")
            print(f"  ⏱️  Last Update: {progress['last_update_time']}")
            
            parcel_remaining = progress['total_rows'] - progress['completed_rows']
            parcel_next_row = progress['completed_rows']
            
            print(f"  🎯 Remaining: {parcel_remaining:,} zipcodes")
            print(f"  🔄 Resume from row: {parcel_next_row}")
        else:
            print(f"📦 PARCEL FAILED: No progress file found")
            parcel_remaining = 13668
            parcel_next_row = 0
    else:
        print(f"📦 PARCEL FAILED: Directory not found")
        parcel_remaining = 13668
        parcel_next_row = 0
    
    # Check Truck Untested status
    truck_dir = "results/timewindows_test_20250806_002307"
    if os.path.exists(truck_dir):
        log_file = f"{truck_dir}/timewindows_test_20250806_002307.log"
        if os.path.exists(log_file):
            last_row = None
            with open(log_file, 'r') as f:
                for line in f:
                    if "Row " in line and (" - SUCCESS" in line or " - FAILED" in line):
                        try:
                            row_part = line.split("Row ")[1].split(":")[0]
                            row_num = int(row_part)
                            last_row = row_num
                        except:
                            pass
            
            if last_row is not None:
                truck_completed = last_row + 1
                truck_remaining = 18071 - truck_completed
                truck_next_row = last_row + 1
                
                print(f"\n🚛 TRUCK UNTESTED STATUS:")
                print(f"  📊 Completed: {truck_completed:,} / 18,071")
                print(f"  📈 Progress: {(truck_completed/18071)*100:.1f}%")
                print(f"  🎯 Remaining: {truck_remaining:,} zipcodes")
                print(f"  🔄 Resume from row: {truck_next_row}")
            else:
                print(f"\n🚛 TRUCK UNTESTED: No progress found in log")
                truck_remaining = 18071
                truck_next_row = 0
        else:
            print(f"\n🚛 TRUCK UNTESTED: No log file found")
            truck_remaining = 18071
            truck_next_row = 0
    else:
        print(f"\n🚛 TRUCK UNTESTED: Directory not found")
        truck_remaining = 18071
        truck_next_row = 0
    
    # Parcel Untested status
    parcel_untested_remaining = 2298
    
    print(f"\n📦 PARCEL UNTESTED STATUS:")
    print(f"  📊 Status: Not Started")
    print(f"  🎯 Remaining: {parcel_untested_remaining:,} zipcodes")
    
    # Summary
    total_remaining = parcel_remaining + truck_remaining + parcel_untested_remaining
    print(f"\n🎯 OVERALL SUMMARY:")
    print(f"  📦 Parcel Failed Remaining: {parcel_remaining:,}")
    print(f"  🚛 Truck Untested Remaining: {truck_remaining:,}")
    print(f"  📦 Parcel Untested Remaining: {parcel_untested_remaining:,}")
    print(f"  📊 Total Remaining: {total_remaining:,} zipcodes")
    
    # Resume commands
    print(f"\n🚀 RESUME COMMANDS:")
    print(f"=" * 60)
    
    if parcel_remaining > 0:
        print(f"1️⃣  RESUME PARCEL FAILED:")
        print(f"   INPUT_EXCEL_FILE=parcel_failed_only_fixed.xlsx python time_windows_test.py --item-id 10534224 --start-row {parcel_next_row} --end-row 13667 --concurrency 15 --output-dir results/timewindows_test_20250806_000942")
    else:
        print(f"1️⃣  PARCEL FAILED: ✅ COMPLETED")
    
    if truck_remaining > 0:
        print(f"\n2️⃣  RESUME TRUCK UNTESTED:")
        print(f"   INPUT_EXCEL_FILE=truck_untested_only_fixed.xlsx python time_windows_test.py --item-id 70570836 --start-row {truck_next_row} --end-row 18070 --concurrency 15 --output-dir results/timewindows_test_20250806_002307")
    else:
        print(f"\n2️⃣  TRUCK UNTESTED: ✅ COMPLETED")
    
    print(f"\n3️⃣  START PARCEL UNTESTED:")
    print(f"   INPUT_EXCEL_FILE=parcel_untested_only_fixed.xlsx python time_windows_test.py --item-id 10534224 --start-row 0 --end-row 2297 --concurrency 15")
    
    print(f"\n💡 RECOMMENDATION:")
    print(f"   Run tests 1️⃣  and 2️⃣  in parallel, then start 3️⃣  when either completes")
    
    print(f"=" * 60)
    
    return {
        'parcel_failed': {'remaining': parcel_remaining, 'next_row': parcel_next_row},
        'truck_untested': {'remaining': truck_remaining, 'next_row': truck_next_row},
        'parcel_untested': {'remaining': parcel_untested_remaining, 'next_row': 0}
    }

def main():
    """Main function"""
    status = check_pause_status()
    return status

if __name__ == "__main__":
    main()
