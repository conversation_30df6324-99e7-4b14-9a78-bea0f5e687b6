#!/usr/bin/env python3
"""
Create a single test file for zipcode 215622
"""

import pandas as pd

def create_single_test_file():
    """Create Excel file with single zipcode for testing"""
    
    print("📝 CREATING SINGLE TEST FILE FOR ZIPCODE 215622")
    print("=" * 50)
    
    # Create DataFrame with single zipcode
    df = pd.DataFrame({
        'ZipCode': ['215622']
    })
    
    # Save to Excel file
    output_file = 'single_test_215622.xlsx'
    df.to_excel(output_file, index=False)
    
    print(f"✅ Created test file: {output_file}")
    print(f"📊 Contains zipcode: 215622")
    print(f"🎯 Ready for parcel item testing (Item ID: 10534224)")
    
    return output_file

def main():
    """Main function"""
    create_single_test_file()

if __name__ == "__main__":
    main()
