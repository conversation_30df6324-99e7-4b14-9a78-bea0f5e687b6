#!/usr/bin/env python3
"""
Thoroughly investigate the classification issue to understand why tested zipcodes 
are being marked as UNTESTED
"""

import pandas as pd
import os

def investigate_specific_zipcode(zipcode):
    """Investigate a specific zipcode that's marked as UNTESTED but has tests"""
    
    print(f"\n🔍 INVESTIGATING ZIPCODE {zipcode}")
    print("=" * 50)
    
    # Check in the comprehensive result file
    parcel_df = pd.read_excel('parcel_final_comprehensive_results.xlsx')
    parcel_row = parcel_df[parcel_df['ZipCode'].astype(str).str.zfill(6) == zipcode]
    
    if not parcel_row.empty:
        row = parcel_row.iloc[0]
        print(f"📦 COMPREHENSIVE FILE STATUS:")
        print(f"  Status: {row['Parcel_Final_Status']}")
        print(f"  Total Tests: {row['Parcel_Total_Tests']}")
        print(f"  Available Dates: {row['Parcel_Available_Dates']}")
        print(f"  Error Details: {row['Parcel_Error_Details']}")
        print(f"  Last Test Dir: {row['Parcel_Last_Test_Dir']}")
    
    # Now check the actual Excel test results for this zipcode
    print(f"\n🔍 CHECKING ACTUAL TEST RESULTS:")
    
    results_dir = "results"
    test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
    
    found_tests = []
    
    for test_dir in test_dirs:
        full_path = os.path.join(results_dir, test_dir)
        
        # Find Excel results file
        excel_files = [f for f in os.listdir(full_path) if f.endswith('.xlsx') and 'results' in f]
        if not excel_files:
            continue
        
        excel_path = os.path.join(full_path, excel_files[0])
        
        # Check if it's a parcel test
        log_files = [f for f in os.listdir(full_path) if f.endswith('.log')]
        if not log_files:
            continue
        
        log_path = os.path.join(full_path, log_files[0])
        
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                first_chunk = f.read(2000)
            
            is_parcel = "Item ID: 10534224" in first_chunk
            if not is_parcel:
                continue
            
            # Read Excel results
            df = pd.read_excel(excel_path)
            zipcode_rows = df[df['ZipCode'].astype(str).str.zfill(6) == zipcode]
            
            if not zipcode_rows.empty:
                for idx, test_row in zipcode_rows.iterrows():
                    delivery_success = test_row.get('tw_delivery_success', False)
                    timewindows_success = test_row.get('tw_timewindows_success', False)
                    available_dates = test_row.get('tw_available_dates', '')
                    error_details = str(test_row.get('tw_error_details', ''))
                    
                    found_tests.append({
                        'test_dir': test_dir,
                        'delivery_success': delivery_success,
                        'timewindows_success': timewindows_success,
                        'available_dates': available_dates,
                        'error_details': error_details
                    })
        
        except Exception as e:
            continue
    
    print(f"📊 FOUND {len(found_tests)} TEST RESULTS:")
    for i, test in enumerate(found_tests, 1):
        print(f"  {i}. {test['test_dir']}:")
        print(f"     Delivery Success: {test['delivery_success']}")
        print(f"     TimeWindows Success: {test['timewindows_success']}")
        print(f"     Available Dates: {test['available_dates']}")
        print(f"     Error Details: {test['error_details'][:100]}...")
        
        # Apply my classification logic
        if test['timewindows_success'] in [True, 1, '1', 'True']:
            classification = "SUCCESS"
        elif test['delivery_success'] in [True, 1, '1', 'True']:
            if any(network_error in str(test['error_details']).lower() for network_error in 
                   ['timeout', 'connection', 'network', 'unreachable', 'dns', 'timed out']):
                classification = "UNTESTED"
            else:
                classification = "FAILED"
        else:
            classification = "UNTESTED"
        
        print(f"     My Classification: {classification}")
        print()
    
    # Determine what the final status should be
    has_success = any(test['timewindows_success'] in [True, 1, '1', 'True'] for test in found_tests)
    has_business_failure = any(
        test['delivery_success'] in [True, 1, '1', 'True'] and 
        not any(network_error in str(test['error_details']).lower() for network_error in 
                ['timeout', 'connection', 'network', 'unreachable', 'dns', 'timed out'])
        for test in found_tests
    )
    
    if has_success:
        correct_status = "SUCCESS"
    elif has_business_failure:
        correct_status = "FAILED"
    else:
        correct_status = "UNTESTED"
    
    print(f"🎯 CORRECT STATUS SHOULD BE: {correct_status}")
    print(f"❌ CURRENT STATUS IS: {row['Parcel_Final_Status'] if not parcel_row.empty else 'NOT_FOUND'}")

def main():
    """Main function"""
    
    print("🔍 INVESTIGATING CLASSIFICATION ISSUE")
    print("=" * 60)
    print("This will investigate why tested zipcodes are marked as UNTESTED")
    print("=" * 60)
    
    # Get some sample zipcodes that are marked as UNTESTED but have tests
    parcel_df = pd.read_excel('parcel_final_comprehensive_results.xlsx')
    untested_with_tests = parcel_df[
        (parcel_df['Parcel_Final_Status'] == 'UNTESTED') & 
        (parcel_df['Parcel_Total_Tests'] > 0)
    ]
    
    print(f"📊 Found {len(untested_with_tests)} zipcodes marked UNTESTED but with tests")
    
    # Investigate a few specific cases
    sample_zipcodes = ['405899', '404700', '404600']
    
    for zipcode in sample_zipcodes:
        investigate_specific_zipcode(zipcode)

if __name__ == "__main__":
    main()
