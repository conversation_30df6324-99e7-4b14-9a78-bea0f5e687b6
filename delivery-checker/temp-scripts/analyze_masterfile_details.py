#!/usr/bin/env python3
"""
Analyze masterfile details - total rows vs distinct zipcodes
"""

import pandas as pd
from collections import Counter

def analyze_masterfile_details():
    """Analyze the masterfile for total rows vs distinct zipcodes"""
    
    print("📋 ANALYZING MASTERFILE DETAILS")
    print("=" * 60)
    
    masterfile_path = "/Users/<USER>/Work/etc/delivery-checker/masterfile-0805.xlsx"
    
    try:
        # Read the masterfile
        df = pd.read_excel(masterfile_path)
        
        print(f"📊 MASTERFILE STATISTICS:")
        print(f"  📁 File: {masterfile_path}")
        print(f"  📋 Total rows: {len(df):,}")
        print(f"  📊 Columns: {list(df.columns)}")
        
        # Analyze ZipCode column
        if 'ZipCode' in df.columns:
            # Get all zipcodes (raw)
            raw_zipcodes = df['ZipCode'].tolist()
            
            # Format zipcodes as 6-character strings
            formatted_zipcodes = [str(zipcode).zfill(6) for zipcode in raw_zipcodes]
            
            # Count unique zipcodes
            unique_zipcodes = set(formatted_zipcodes)
            
            print(f"\n🔍 ZIPCODE ANALYSIS:")
            print(f"  📋 Total zipcode entries: {len(raw_zipcodes):,}")
            print(f"  🎯 Unique zipcodes: {len(unique_zipcodes):,}")
            print(f"  🔄 Duplicate entries: {len(raw_zipcodes) - len(unique_zipcodes):,}")
            print(f"  📈 Duplication rate: {((len(raw_zipcodes) - len(unique_zipcodes))/len(raw_zipcodes))*100:.1f}%")
            
            # Find most duplicated zipcodes
            zipcode_counts = Counter(formatted_zipcodes)
            most_common = zipcode_counts.most_common(10)
            
            print(f"\n🔝 TOP 10 MOST DUPLICATED ZIPCODES:")
            for i, (zipcode, count) in enumerate(most_common, 1):
                if count > 1:
                    print(f"  {i:2d}. {zipcode}: {count:,} occurrences")
                else:
                    break
            
            # Sample of raw data
            print(f"\n📋 SAMPLE DATA (first 10 rows):")
            for i in range(min(10, len(df))):
                row = df.iloc[i]
                zipcode_raw = row['ZipCode']
                zipcode_formatted = str(zipcode_raw).zfill(6)
                other_cols = {col: row[col] for col in df.columns if col != 'ZipCode'}
                print(f"  Row {i+1}: {zipcode_raw} → {zipcode_formatted} | {other_cols}")
            
            # Check if there are other identifying columns
            print(f"\n🔍 OTHER COLUMNS ANALYSIS:")
            for col in df.columns:
                if col != 'ZipCode':
                    unique_values = df[col].nunique()
                    total_values = len(df[col].dropna())
                    print(f"  📊 {col}: {unique_values:,} unique values out of {total_values:,} non-null entries")
                    
                    # Show sample values
                    sample_values = df[col].dropna().unique()[:5]
                    print(f"      Sample values: {list(sample_values)}")
            
            # Check for patterns in duplicates
            print(f"\n🔍 DUPLICATE PATTERN ANALYSIS:")
            duplicated_zipcodes = [zipcode for zipcode, count in zipcode_counts.items() if count > 1]
            
            if duplicated_zipcodes:
                print(f"  🔄 {len(duplicated_zipcodes):,} zipcodes appear multiple times")
                
                # Analyze a few duplicate cases
                sample_duplicate = duplicated_zipcodes[0]
                duplicate_rows = df[df['ZipCode'].astype(str).str.zfill(6) == sample_duplicate]
                
                print(f"\n📋 EXAMPLE DUPLICATE ANALYSIS (ZipCode: {sample_duplicate}):")
                print(f"  📊 Appears {len(duplicate_rows)} times:")
                for i, (idx, row) in enumerate(duplicate_rows.iterrows()):
                    print(f"    {i+1}. Row {idx+1}: {dict(row)}")
                
                # Check if duplicates have different values in other columns
                for col in df.columns:
                    if col != 'ZipCode':
                        unique_vals_for_duplicate = duplicate_rows[col].nunique()
                        if unique_vals_for_duplicate > 1:
                            print(f"  ⚠️  Column '{col}' has different values for same zipcode!")
                            print(f"      Values: {list(duplicate_rows[col].unique())}")
            else:
                print(f"  ✅ No duplicates found")
        
        else:
            print(f"❌ ZipCode column not found in masterfile")
            print(f"Available columns: {list(df.columns)}")
    
    except Exception as e:
        print(f"❌ Error reading masterfile: {e}")

def main():
    """Main function"""
    analyze_masterfile_details()

if __name__ == "__main__":
    main()
