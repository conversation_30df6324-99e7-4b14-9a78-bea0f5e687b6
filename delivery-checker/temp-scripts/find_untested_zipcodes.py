#!/usr/bin/env python3
"""
Find samples of untested zipcodes by comparing masterfile against all test results
"""

import os
import pandas as pd
from collections import defaultdict

def get_all_tested_zipcodes():
    """Get all zipcodes that have been tested (regardless of result)"""
    
    print("🔍 SCANNING ALL TEST RESULTS FOR TESTED ZIPCODES")
    print("=" * 60)
    
    results_dir = "results"
    parcel_tested = set()
    truck_tested = set()
    
    if not os.path.exists(results_dir):
        print(f"❌ Results directory not found: {results_dir}")
        return parcel_tested, truck_tested
    
    # Get all test directories
    test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
    test_dirs.sort()
    
    print(f"📁 Found {len(test_dirs)} test directories")
    
    for test_dir in test_dirs:
        full_path = os.path.join(results_dir, test_dir)
        
        # Find Excel results file
        excel_files = [f for f in os.listdir(full_path) if f.endswith('.xlsx') and 'results' in f]
        if not excel_files:
            continue
        
        excel_path = os.path.join(full_path, excel_files[0])
        
        # Determine test type from log
        log_files = [f for f in os.listdir(full_path) if f.endswith('.log')]
        if not log_files:
            continue
        
        log_path = os.path.join(full_path, log_files[0])
        
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                first_chunk = f.read(2000)
            
            is_parcel = "Item ID: 10534224" in first_chunk
            is_truck = "Item ID: 70570836" in first_chunk
            
            if not (is_parcel or is_truck):
                continue
            
            delivery_type = "Parcel" if is_parcel else "Truck"
            current_tested = parcel_tested if is_parcel else truck_tested
            
            # Read Excel results
            df = pd.read_excel(excel_path)
            
            for _, row in df.iterrows():
                zipcode = str(row['ZipCode']).zfill(6)
                current_tested.add(zipcode)
            
            print(f"  📊 {delivery_type} test {test_dir}: {len(df)} zipcodes")
        
        except Exception as e:
            print(f"  ⚠️  Error processing {test_dir}: {e}")
            continue
    
    print(f"\n📊 TESTED ZIPCODES SUMMARY:")
    print(f"  📦 Parcel tested: {len(parcel_tested):,} unique zipcodes")
    print(f"  🚛 Truck tested: {len(truck_tested):,} unique zipcodes")
    
    return parcel_tested, truck_tested

def find_untested_samples():
    """Find samples of untested zipcodes"""
    
    print("\n📋 FINDING UNTESTED ZIPCODE SAMPLES")
    print("=" * 60)
    
    # Load masterfile
    masterfile_path = "/Users/<USER>/Work/etc/delivery-checker/masterfile-0805.xlsx"
    
    try:
        df = pd.read_excel(masterfile_path)
        print(f"✅ Loaded masterfile: {len(df):,} rows")
        
        # Get all unique zipcodes from masterfile
        all_zipcodes = set(str(zipcode).zfill(6) for zipcode in df['ZipCode'].unique())
        print(f"📊 Total unique zipcodes in masterfile: {len(all_zipcodes):,}")
        
    except Exception as e:
        print(f"❌ Error loading masterfile: {e}")
        return
    
    # Get tested zipcodes
    parcel_tested, truck_tested = get_all_tested_zipcodes()
    
    # Find untested zipcodes
    parcel_untested = all_zipcodes - parcel_tested
    truck_untested = all_zipcodes - truck_tested
    
    print(f"\n🎯 UNTESTED ZIPCODE ANALYSIS:")
    print(f"  📦 Parcel untested: {len(parcel_untested):,} zipcodes")
    print(f"  🚛 Truck untested: {len(truck_untested):,} zipcodes")
    
    # Show samples
    print(f"\n📋 PARCEL UNTESTED SAMPLES (first 50):")
    parcel_samples = sorted(list(parcel_untested))[:50]
    for i, zipcode in enumerate(parcel_samples, 1):
        print(f"  {i:2d}. {zipcode}")
    
    print(f"\n📋 TRUCK UNTESTED SAMPLES (first 50):")
    truck_samples = sorted(list(truck_untested))[:50]
    for i, zipcode in enumerate(truck_samples, 1):
        print(f"  {i:2d}. {zipcode}")
    
    # Find geographic info for samples
    print(f"\n🗺️  GEOGRAPHIC INFO FOR UNTESTED SAMPLES:")
    
    # Parcel untested with geographic info
    if parcel_untested:
        print(f"\n📦 PARCEL UNTESTED WITH LOCATION INFO (first 20):")
        parcel_sample_df = df[df['ZipCode'].astype(str).str.zfill(6).isin(list(parcel_untested)[:20])]
        for idx, row in parcel_sample_df.iterrows():
            zipcode = str(row['ZipCode']).zfill(6)
            province = row.get('Province-EN', 'Unknown')
            city = row.get('City-EN', 'Unknown')
            district = row.get('District-EN', 'Unknown')
            print(f"  {zipcode}: {province} > {city} > {district}")
    
    # Truck untested with geographic info
    if truck_untested:
        print(f"\n🚛 TRUCK UNTESTED WITH LOCATION INFO (first 20):")
        truck_sample_df = df[df['ZipCode'].astype(str).str.zfill(6).isin(list(truck_untested)[:20])]
        for idx, row in truck_sample_df.iterrows():
            zipcode = str(row['ZipCode']).zfill(6)
            province = row.get('Province-EN', 'Unknown')
            city = row.get('City-EN', 'Unknown')
            district = row.get('District-EN', 'Unknown')
            print(f"  {zipcode}: {province} > {city} > {district}")
    
    # Save untested zipcodes to files
    if parcel_untested:
        parcel_untested_df = pd.DataFrame({'ZipCode': sorted(list(parcel_untested))})
        parcel_untested_df.to_excel('parcel_untested_samples.xlsx', index=False)
        print(f"\n💾 Saved parcel untested zipcodes to: parcel_untested_samples.xlsx")
    
    if truck_untested:
        truck_untested_df = pd.DataFrame({'ZipCode': sorted(list(truck_untested))})
        truck_untested_df.to_excel('truck_untested_samples.xlsx', index=False)
        print(f"💾 Saved truck untested zipcodes to: truck_untested_samples.xlsx")
    
    # Coverage analysis
    print(f"\n📊 COVERAGE ANALYSIS:")
    print(f"  📦 Parcel Coverage: {(len(parcel_tested)/len(all_zipcodes))*100:.1f}% ({len(parcel_tested):,}/{len(all_zipcodes):,})")
    print(f"  🚛 Truck Coverage: {(len(truck_tested)/len(all_zipcodes))*100:.1f}% ({len(truck_tested):,}/{len(all_zipcodes):,})")
    
    # Both untested
    both_untested = parcel_untested & truck_untested
    print(f"  🚫 Both untested: {len(both_untested):,} zipcodes")
    
    if both_untested:
        print(f"\n🚫 ZIPCODES UNTESTED FOR BOTH PARCEL AND TRUCK (first 20):")
        both_samples = sorted(list(both_untested))[:20]
        for i, zipcode in enumerate(both_samples, 1):
            print(f"  {i:2d}. {zipcode}")

def main():
    """Main function"""
    
    print("🔍 FINDING UNTESTED ZIPCODE SAMPLES")
    print("=" * 70)
    print("This script will:")
    print("1. Scan all test results to find tested zipcodes")
    print("2. Compare against masterfile to find untested ones")
    print("3. Provide samples with geographic information")
    print("4. Generate coverage analysis")
    print("=" * 70)
    
    find_untested_samples()
    
    print(f"\n🎉 ANALYSIS COMPLETE!")
    print("=" * 70)

if __name__ == "__main__":
    main()
