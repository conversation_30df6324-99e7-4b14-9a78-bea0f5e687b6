#!/usr/bin/env python3
"""
Comprehensive zipcode analysis across all historical test results
"""

import os
import json
import pandas as pd
import re
from collections import defaultdict

def extract_all_zipcodes_from_masterfile():
    """Extract all zipcodes from the masterfile"""
    
    print("📋 EXTRACTING ZIPCODES FROM MASTERFILE")
    print("=" * 60)
    
    masterfile_path = "/Users/<USER>/Work/etc/delivery-checker/masterfile-0805.xlsx"
    
    if not os.path.exists(masterfile_path):
        print(f"❌ Masterfile not found: {masterfile_path}")
        return set()
    
    try:
        df = pd.read_excel(masterfile_path)
        
        # Ensure zipcodes are properly formatted as 6-character strings
        zipcodes = set()
        for zipcode in df['ZipCode']:
            formatted_zipcode = str(zipcode).zfill(6)
            zipcodes.add(formatted_zipcode)
        
        print(f"✅ Extracted {len(zipcodes):,} unique zipcodes from masterfile")
        print(f"📋 Sample zipcodes: {list(sorted(zipcodes))[:10]}")
        
        return zipcodes
        
    except Exception as e:
        print(f"❌ Error reading masterfile: {e}")
        return set()

def analyze_log_file(log_path, delivery_type):
    """Analyze a single log file for successful zipcodes"""
    
    successful_zipcodes = set()
    
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for SUCCESS patterns in the log
        # Pattern: "Row X: ZIPCODE - SUCCESS"
        success_pattern = r'Row \d+: (\d{6}) - SUCCESS'
        matches = re.findall(success_pattern, content)
        
        for zipcode in matches:
            # Ensure 6-character format
            formatted_zipcode = str(zipcode).zfill(6)
            successful_zipcodes.add(formatted_zipcode)
        
        if successful_zipcodes:
            print(f"  📊 {delivery_type}: Found {len(successful_zipcodes)} successful zipcodes in {os.path.basename(log_path)}")
        
        return successful_zipcodes
        
    except Exception as e:
        print(f"  ⚠️  Error reading {log_path}: {e}")
        return set()

def scan_all_historical_logs():
    """Scan all historical logs for successful zipcodes"""
    
    print("\n🔍 SCANNING ALL HISTORICAL LOGS")
    print("=" * 60)
    
    results_dir = "results"
    if not os.path.exists(results_dir):
        print(f"❌ Results directory not found: {results_dir}")
        return {}, {}
    
    parcel_successful = set()
    truck_successful = set()
    
    # Get all test directories
    test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
    test_dirs.sort()
    
    print(f"📁 Found {len(test_dirs)} test directories to analyze")
    
    for test_dir in test_dirs:
        full_path = os.path.join(results_dir, test_dir)
        
        # Find log file
        log_files = [f for f in os.listdir(full_path) if f.endswith('.log')]
        
        if not log_files:
            continue
        
        log_path = os.path.join(full_path, log_files[0])
        
        # Determine test type by checking log content
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                first_chunk = f.read(2000)  # Read first 2000 chars
            
            delivery_type = "Unknown"
            if "Item ID: 10534224" in first_chunk:
                delivery_type = "📦 Parcel"
                successful_zipcodes = analyze_log_file(log_path, delivery_type)
                parcel_successful.update(successful_zipcodes)
            elif "Item ID: 70570836" in first_chunk:
                delivery_type = "🚛 Truck"
                successful_zipcodes = analyze_log_file(log_path, delivery_type)
                truck_successful.update(successful_zipcodes)
            
        except Exception as e:
            print(f"  ⚠️  Error analyzing {test_dir}: {e}")
            continue
    
    print(f"\n📊 HISTORICAL ANALYSIS SUMMARY:")
    print(f"  📦 Parcel: {len(parcel_successful):,} unique successful zipcodes")
    print(f"  🚛 Truck: {len(truck_successful):,} unique successful zipcodes")
    
    return parcel_successful, truck_successful

def generate_comprehensive_report(all_zipcodes, parcel_successful, truck_successful):
    """Generate comprehensive report of zipcode status"""
    
    print(f"\n📋 GENERATING COMPREHENSIVE REPORT")
    print("=" * 60)
    
    # Calculate never succeeded zipcodes
    parcel_never_succeeded = all_zipcodes - parcel_successful
    truck_never_succeeded = all_zipcodes - truck_successful
    
    # Calculate overlap and unique failures
    both_succeeded = parcel_successful & truck_successful
    parcel_only_succeeded = parcel_successful - truck_successful
    truck_only_succeeded = truck_successful - parcel_successful
    
    both_never_succeeded = parcel_never_succeeded & truck_never_succeeded
    parcel_only_failed = parcel_never_succeeded - truck_never_succeeded
    truck_only_failed = truck_never_succeeded - parcel_never_succeeded
    
    # Generate report
    report = {
        'summary': {
            'total_zipcodes': len(all_zipcodes),
            'parcel_successful': len(parcel_successful),
            'truck_successful': len(truck_successful),
            'parcel_never_succeeded': len(parcel_never_succeeded),
            'truck_never_succeeded': len(truck_never_succeeded),
            'both_succeeded': len(both_succeeded),
            'both_never_succeeded': len(both_never_succeeded)
        },
        'parcel_never_succeeded': sorted(list(parcel_never_succeeded)),
        'truck_never_succeeded': sorted(list(truck_never_succeeded)),
        'parcel_successful': sorted(list(parcel_successful)),
        'truck_successful': sorted(list(truck_successful))
    }
    
    # Print detailed report
    print(f"\n🎯 COMPREHENSIVE ZIPCODE ANALYSIS REPORT")
    print("=" * 70)
    
    print(f"\n📊 OVERALL STATISTICS:")
    print(f"  📋 Total zipcodes in masterfile: {len(all_zipcodes):,}")
    print(f"  ✅ Parcel successful (at least once): {len(parcel_successful):,} ({len(parcel_successful)/len(all_zipcodes)*100:.1f}%)")
    print(f"  ✅ Truck successful (at least once): {len(truck_successful):,} ({len(truck_successful)/len(all_zipcodes)*100:.1f}%)")
    
    print(f"\n❌ NEVER SUCCEEDED:")
    print(f"  📦 Parcel never succeeded: {len(parcel_never_succeeded):,} ({len(parcel_never_succeeded)/len(all_zipcodes)*100:.1f}%)")
    print(f"  🚛 Truck never succeeded: {len(truck_never_succeeded):,} ({len(truck_never_succeeded)/len(all_zipcodes)*100:.1f}%)")
    print(f"  🚫 Both never succeeded: {len(both_never_succeeded):,} ({len(both_never_succeeded)/len(all_zipcodes)*100:.1f}%)")
    
    print(f"\n🔍 DETAILED BREAKDOWN:")
    print(f"  🎯 Both parcel & truck succeeded: {len(both_succeeded):,}")
    print(f"  📦 Only parcel succeeded: {len(parcel_only_succeeded):,}")
    print(f"  🚛 Only truck succeeded: {len(truck_only_succeeded):,}")
    print(f"  📦 Only parcel failed: {len(parcel_only_failed):,}")
    print(f"  🚛 Only truck failed: {len(truck_only_failed):,}")
    
    # Show sample zipcodes that never succeeded
    print(f"\n📋 SAMPLE ZIPCODES THAT NEVER SUCCEEDED:")
    if parcel_never_succeeded:
        sample_parcel = sorted(list(parcel_never_succeeded))[:20]
        print(f"  📦 Parcel (first 20): {sample_parcel}")
    
    if truck_never_succeeded:
        sample_truck = sorted(list(truck_never_succeeded))[:20]
        print(f"  🚛 Truck (first 20): {sample_truck}")
    
    if both_never_succeeded:
        sample_both = sorted(list(both_never_succeeded))[:20]
        print(f"  🚫 Both (first 20): {sample_both}")
    
    return report

def save_detailed_results(report):
    """Save detailed results to files"""
    
    print(f"\n💾 SAVING DETAILED RESULTS")
    print("=" * 60)
    
    # Save parcel never succeeded
    if report['parcel_never_succeeded']:
        parcel_df = pd.DataFrame({
            'ZipCode': report['parcel_never_succeeded']
        })
        parcel_file = 'parcel_never_succeeded_zipcodes.xlsx'
        parcel_df.to_excel(parcel_file, index=False)
        print(f"  📦 Saved {len(report['parcel_never_succeeded']):,} parcel never-succeeded zipcodes to: {parcel_file}")
    
    # Save truck never succeeded
    if report['truck_never_succeeded']:
        truck_df = pd.DataFrame({
            'ZipCode': report['truck_never_succeeded']
        })
        truck_file = 'truck_never_succeeded_zipcodes.xlsx'
        truck_df.to_excel(truck_file, index=False)
        print(f"  🚛 Saved {len(report['truck_never_succeeded']):,} truck never-succeeded zipcodes to: {truck_file}")
    
    # Save summary report
    summary_file = 'zipcode_analysis_summary.json'
    with open(summary_file, 'w') as f:
        json.dump(report, f, indent=2)
    print(f"  📋 Saved complete analysis to: {summary_file}")
    
    print(f"\n✅ All results saved successfully!")

def main():
    """Main function"""
    
    print("🔍 COMPREHENSIVE ZIPCODE ANALYSIS")
    print("=" * 70)
    print("This script will:")
    print("1. Extract all zipcodes from masterfile-0805.xlsx")
    print("2. Scan all historical logs for successful zipcodes")
    print("3. Identify zipcodes that never succeeded")
    print("4. Generate comprehensive report")
    print("=" * 70)
    
    # Step 1: Extract all zipcodes from masterfile
    all_zipcodes = extract_all_zipcodes_from_masterfile()
    if not all_zipcodes:
        print("❌ Failed to extract zipcodes from masterfile")
        return
    
    # Step 2: Scan all historical logs
    parcel_successful, truck_successful = scan_all_historical_logs()
    
    # Step 3: Generate comprehensive report
    report = generate_comprehensive_report(all_zipcodes, parcel_successful, truck_successful)
    
    # Step 4: Save detailed results
    save_detailed_results(report)
    
    print(f"\n🎉 ANALYSIS COMPLETE!")
    print("=" * 70)

if __name__ == "__main__":
    main()
