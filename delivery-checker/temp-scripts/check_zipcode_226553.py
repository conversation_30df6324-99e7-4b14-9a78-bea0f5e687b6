#!/usr/bin/env python3
"""
Check the status of zipcode 226553 in the final comprehensive result files
"""

import pandas as pd

def check_zipcode_status(zipcode):
    """Check the status of a specific zipcode in both result files"""
    
    print(f"🔍 CHECKING ZIPCODE {zipcode} STATUS")
    print("=" * 50)
    
    # Check parcel results
    try:
        parcel_df = pd.read_excel('parcel_final_comprehensive_results.xlsx')
        parcel_rows = parcel_df[parcel_df['ZipCode'].astype(str).str.zfill(6) == zipcode]
        
        if not parcel_rows.empty:
            parcel_row = parcel_rows.iloc[0]
            print(f"📦 PARCEL DELIVERY:")
            print(f"  🎯 Status: {parcel_row.get('Parcel_Final_Status', 'N/A')}")
            print(f"  📅 Available Dates: {parcel_row.get('Parcel_Available_Dates', 'N/A')}")
            print(f"  ❌ Error Details: {parcel_row.get('Parcel_Error_Details', 'N/A')}")
            print(f"  🔢 Total Tests: {parcel_row.get('Parcel_Total_Tests', 'N/A')}")
            print(f"  📁 Success Test: {parcel_row.get('Parcel_Success_Test_Dir', 'N/A')}")
            print(f"  📁 Last Test: {parcel_row.get('Parcel_Last_Test_Dir', 'N/A')}")
        else:
            print(f"📦 PARCEL: Zipcode {zipcode} not found in parcel results")
    
    except Exception as e:
        print(f"❌ Error reading parcel results: {e}")
    
    # Check truck results
    try:
        truck_df = pd.read_excel('truck_final_comprehensive_results.xlsx')
        truck_rows = truck_df[truck_df['ZipCode'].astype(str).str.zfill(6) == zipcode]
        
        if not truck_rows.empty:
            truck_row = truck_rows.iloc[0]
            print(f"\n🚛 TRUCK DELIVERY:")
            print(f"  🎯 Status: {truck_row.get('Truck_Final_Status', 'N/A')}")
            print(f"  📅 Available Dates: {truck_row.get('Truck_Available_Dates', 'N/A')}")
            print(f"  ❌ Error Details: {truck_row.get('Truck_Error_Details', 'N/A')}")
            print(f"  🔢 Total Tests: {truck_row.get('Truck_Total_Tests', 'N/A')}")
            print(f"  📁 Success Test: {truck_row.get('Truck_Success_Test_Dir', 'N/A')}")
            print(f"  📁 Last Test: {truck_row.get('Truck_Last_Test_Dir', 'N/A')}")
        else:
            print(f"\n🚛 TRUCK: Zipcode {zipcode} not found in truck results")
    
    except Exception as e:
        print(f"❌ Error reading truck results: {e}")
    
    # Summary
    print(f"\n📊 SUMMARY FOR ZIPCODE {zipcode}:")
    print("=" * 50)
    
    try:
        parcel_status = parcel_rows.iloc[0].get('Parcel_Final_Status', 'NOT_FOUND') if not parcel_rows.empty else 'NOT_FOUND'
        truck_status = truck_rows.iloc[0].get('Truck_Final_Status', 'NOT_FOUND') if not truck_rows.empty else 'NOT_FOUND'
        
        print(f"📦 Parcel: {parcel_status}")
        print(f"🚛 Truck: {truck_status}")
        
        if parcel_status == 'SUCCESS' and truck_status == 'SUCCESS':
            print("🎉 BOTH PARCEL AND TRUCK SUCCEEDED!")
        elif parcel_status == 'SUCCESS':
            print("✅ PARCEL SUCCEEDED, TRUCK DID NOT")
        elif truck_status == 'SUCCESS':
            print("✅ TRUCK SUCCEEDED, PARCEL DID NOT")
        else:
            print("❌ NEITHER PARCEL NOR TRUCK SUCCEEDED")
    
    except Exception as e:
        print(f"❌ Error in summary: {e}")

def main():
    """Main function"""
    check_zipcode_status("226553")

if __name__ == "__main__":
    main()
