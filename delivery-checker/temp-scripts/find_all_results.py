#!/usr/bin/env python3
"""
Find and summarize all test results locations
"""

import os
import json
import pandas as pd
from datetime import datetime

def find_all_results():
    """Find all test results directories and files"""
    
    print("📁 FINDING ALL TEST RESULTS")
    print("=" * 70)
    
    results_dir = "results"
    if not os.path.exists(results_dir):
        print(f"❌ Results directory not found: {results_dir}")
        return
    
    # Get all test directories
    test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
    test_dirs.sort()
    
    print(f"📊 Found {len(test_dirs)} test result directories:")
    print()
    
    total_results = 0
    
    for i, test_dir in enumerate(test_dirs, 1):
        full_path = os.path.join(results_dir, test_dir)
        print(f"{i}️⃣  📁 {test_dir}")
        print(f"   📍 Location: {full_path}")
        
        # Check what files are in this directory
        files = os.listdir(full_path)
        
        # Determine test type from log file
        log_files = [f for f in files if f.endswith('.log')]
        test_type = "Unknown"
        item_id = "Unknown"
        
        if log_files:
            log_file = os.path.join(full_path, log_files[0])
            try:
                with open(log_file, 'r') as f:
                    first_lines = f.read(2000)  # Read first 2000 chars
                    
                    if "parcel_failed_only_fixed.xlsx" in first_lines:
                        test_type = "📦 Parcel Failed"
                        item_id = "10534224"
                    elif "parcel_untested_only_fixed.xlsx" in first_lines:
                        test_type = "📦 Parcel Untested"
                        item_id = "10534224"
                    elif "truck_untested_only_fixed.xlsx" in first_lines:
                        test_type = "🚛 Truck Untested"
                        item_id = "70570836"
                    elif "truck_failed_only_fixed.xlsx" in first_lines:
                        test_type = "🚛 Truck Failed"
                        item_id = "70570836"
                    
                    # Extract item ID from log
                    if "Item ID: " in first_lines:
                        item_id = first_lines.split("Item ID: ")[1].split()[0]
            except:
                pass
        
        print(f"   🏷️  Test Type: {test_type}")
        print(f"   🆔 Item ID: {item_id}")
        
        # Check key files
        key_files = {
            "📄 Log File": [f for f in files if f.endswith('.log')],
            "📊 Excel Results": [f for f in files if f.endswith('.xlsx')],
            "📋 Progress File": [f for f in files if f.endswith('.json') and 'progress' in f],
            "🔗 API Responses": [f for f in files if f.endswith('.json') and 'api_responses' in f]
        }
        
        for file_type, file_list in key_files.items():
            if file_list:
                for file_name in file_list:
                    file_path = os.path.join(full_path, file_name)
                    file_size = os.path.getsize(file_path)
                    
                    if file_size > 1024*1024:  # > 1MB
                        size_str = f"{file_size/(1024*1024):.1f} MB"
                    elif file_size > 1024:  # > 1KB
                        size_str = f"{file_size/1024:.1f} KB"
                    else:
                        size_str = f"{file_size} bytes"
                    
                    print(f"   {file_type}: {file_name} ({size_str})")
                    
                    # Count results from Excel files
                    if file_name.endswith('.xlsx') and 'results' in file_name:
                        try:
                            df = pd.read_excel(file_path)
                            # Count non-empty results
                            result_count = 0
                            for col in ['tw_delivery_success', 'tw_timewindows_success']:
                                if col in df.columns:
                                    result_count = df[col].notna().sum()
                                    break
                            
                            if result_count > 0:
                                print(f"     📈 Results: {result_count:,} zipcodes tested")
                                total_results += result_count
                        except Exception as e:
                            print(f"     ⚠️  Could not read Excel: {e}")
            else:
                print(f"   {file_type}: ❌ Not found")
        
        print()
    
    # Summary
    print(f"🎯 RESULTS SUMMARY:")
    print(f"=" * 70)
    print(f"📁 Total test directories: {len(test_dirs)}")
    print(f"📊 Total zipcodes tested: {total_results:,}")
    
    # Provide direct paths for easy access
    print(f"\n📍 DIRECT ACCESS PATHS:")
    print(f"=" * 70)
    
    for test_dir in test_dirs:
        full_path = os.path.join(results_dir, test_dir)
        
        # Find Excel results file
        files = os.listdir(full_path)
        excel_files = [f for f in files if f.endswith('.xlsx') and 'results' in f]
        
        if excel_files:
            excel_path = os.path.join(full_path, excel_files[0])
            print(f"📊 {test_dir}:")
            print(f"   Excel Results: {excel_path}")
        
        # Find log file
        log_files = [f for f in files if f.endswith('.log')]
        if log_files:
            log_path = os.path.join(full_path, log_files[0])
            print(f"   Log File: {log_path}")
        
        print()
    
    # Current working directory context
    current_dir = os.getcwd()
    print(f"💡 NOTE: All paths are relative to: {current_dir}")
    print(f"=" * 70)

def main():
    """Main function"""
    find_all_results()

if __name__ == "__main__":
    main()
