#!/usr/bin/env python3
"""
Debug the comprehensive results to understand why so many zipcodes show as untested
"""

import pandas as pd
import os

def debug_comprehensive_results():
    """Debug the comprehensive result files"""
    
    print("🔍 DEBUGGING COMPREHENSIVE RESULT FILES")
    print("=" * 60)
    
    # Load the final result files
    try:
        parcel_df = pd.read_excel('parcel_final_comprehensive_results.xlsx')
        truck_df = pd.read_excel('truck_final_comprehensive_results.xlsx')
        
        print(f"✅ Loaded result files:")
        print(f"  📦 Parcel: {len(parcel_df):,} rows")
        print(f"  🚛 Truck: {len(truck_df):,} rows")
        
    except Exception as e:
        print(f"❌ Error loading result files: {e}")
        return
    
    # Check status distribution
    print(f"\n📊 STATUS DISTRIBUTION:")
    
    parcel_status = parcel_df['Parcel_Final_Status'].value_counts()
    print(f"\n📦 PARCEL STATUS:")
    for status, count in parcel_status.items():
        print(f"  {status}: {count:,}")
    
    truck_status = truck_df['Truck_Final_Status'].value_counts()
    print(f"\n🚛 TRUCK STATUS:")
    for status, count in truck_status.items():
        print(f"  {status}: {count:,}")
    
    # Check unique zipcodes
    parcel_unique = parcel_df['ZipCode'].nunique()
    truck_unique = truck_df['ZipCode'].nunique()
    
    print(f"\n📊 UNIQUE ZIPCODES:")
    print(f"  📦 Parcel file: {parcel_unique:,} unique zipcodes")
    print(f"  🚛 Truck file: {truck_unique:,} unique zipcodes")
    
    # Check some specific examples
    print(f"\n🔍 SAMPLE UNTESTED ZIPCODES:")
    
    parcel_untested = parcel_df[parcel_df['Parcel_Final_Status'] == 'UNTESTED']
    if not parcel_untested.empty:
        print(f"\n📦 PARCEL UNTESTED SAMPLES (first 10):")
        for idx, row in parcel_untested.head(10).iterrows():
            zipcode = str(row['ZipCode']).zfill(6)
            total_tests = row.get('Parcel_Total_Tests', 0)
            print(f"  {zipcode}: {total_tests} tests")
    
    truck_untested = truck_df[truck_df['Truck_Final_Status'] == 'UNTESTED']
    if not truck_untested.empty:
        print(f"\n🚛 TRUCK UNTESTED SAMPLES (first 10):")
        for idx, row in truck_untested.head(10).iterrows():
            zipcode = str(row['ZipCode']).zfill(6)
            total_tests = row.get('Truck_Total_Tests', 0)
            print(f"  {zipcode}: {total_tests} tests")
    
    # Check if the issue is with the aggregation logic
    print(f"\n🔍 CHECKING AGGREGATION LOGIC:")
    
    # Look at zipcodes that have tests but still show as untested
    parcel_untested_with_tests = parcel_df[(parcel_df['Parcel_Final_Status'] == 'UNTESTED') & (parcel_df['Parcel_Total_Tests'] > 0)]
    print(f"\n📦 PARCEL: {len(parcel_untested_with_tests)} zipcodes marked UNTESTED but have tests")
    
    if not parcel_untested_with_tests.empty:
        print("Sample cases:")
        for idx, row in parcel_untested_with_tests.head(5).iterrows():
            zipcode = str(row['ZipCode']).zfill(6)
            tests = row.get('Parcel_Total_Tests', 0)
            error = row.get('Parcel_Error_Details', '')
            print(f"  {zipcode}: {tests} tests, error: {error[:100]}...")
    
    truck_untested_with_tests = truck_df[(truck_df['Truck_Final_Status'] == 'UNTESTED') & (truck_df['Truck_Total_Tests'] > 0)]
    print(f"\n🚛 TRUCK: {len(truck_untested_with_tests)} zipcodes marked UNTESTED but have tests")
    
    if not truck_untested_with_tests.empty:
        print("Sample cases:")
        for idx, row in truck_untested_with_tests.head(5).iterrows():
            zipcode = str(row['ZipCode']).zfill(6)
            tests = row.get('Truck_Total_Tests', 0)
            error = row.get('Truck_Error_Details', '')
            print(f"  {zipcode}: {tests} tests, error: {error[:100]}...")
    
    # Cross-check with our earlier corrected analysis
    print(f"\n🔍 CROSS-CHECK WITH LOG ANALYSIS:")
    
    # Let's manually check a few zipcodes that show as untested
    sample_zipcodes = ['100005', '100006', '226553']
    
    for zipcode in sample_zipcodes:
        print(f"\n🔍 Checking {zipcode}:")
        
        # Check in parcel file
        parcel_row = parcel_df[parcel_df['ZipCode'].astype(str).str.zfill(6) == zipcode]
        if not parcel_row.empty:
            status = parcel_row.iloc[0]['Parcel_Final_Status']
            tests = parcel_row.iloc[0]['Parcel_Total_Tests']
            print(f"  📦 Parcel: {status} ({tests} tests)")
        
        # Check in truck file
        truck_row = truck_df[truck_df['ZipCode'].astype(str).str.zfill(6) == zipcode]
        if not truck_row.empty:
            status = truck_row.iloc[0]['Truck_Final_Status']
            tests = truck_row.iloc[0]['Truck_Total_Tests']
            print(f"  🚛 Truck: {status} ({tests} tests)")
        
        # Check in actual logs
        print(f"  🔍 Checking logs...")
        os.system(f'ag -c "{zipcode}" results/ | head -5')

def main():
    """Main function"""
    debug_comprehensive_results()

if __name__ == "__main__":
    main()
