#!/usr/bin/env python3
"""
Check what the current running test is doing vs our comprehensive analysis
"""

import os
import json
import pandas as pd

def check_current_test_discrepancy():
    """Check why we're still running parcel tests when analysis shows 99.996% complete"""
    
    print("🔍 CHECKING CURRENT TEST VS COMPREHENSIVE ANALYSIS")
    print("=" * 70)
    
    # Check the current running test details
    current_test_dir = "results/timewindows_test_20250806_000942"
    
    if os.path.exists(current_test_dir):
        progress_file = f"{current_test_dir}/progress_20250806_000942.json"
        
        if os.path.exists(progress_file):
            with open(progress_file, 'r') as f:
                progress = json.load(f)
            
            print(f"📊 CURRENT RUNNING TEST STATUS:")
            print(f"  📁 Directory: {current_test_dir}")
            print(f"  📈 Progress: {progress['completed_rows']:,} / {progress['total_rows']:,}")
            print(f"  📊 Completion: {progress['completion_percentage']:.1f}%")
            print(f"  ✅ Successful: {progress['successful_delivery']:,}")
            print(f"  ❌ Failed: {progress['failed_delivery']:,}")
            print(f"  ⏱️  Last Update: {progress['last_update_time']}")
            
            # Check what input file this test is using
            log_file = f"{current_test_dir}/timewindows_test_20250806_000942.log"
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    log_content = f.read(2000)  # First 2000 chars
                
                print(f"\n📋 TEST CONFIGURATION:")
                if "parcel_failed_only_fixed.xlsx" in log_content:
                    print(f"  📦 Input File: parcel_failed_only_fixed.xlsx")
                    print(f"  🎯 Test Type: Parcel Failed zipcodes")
                    print(f"  🆔 Item ID: 10534224")
                
                # Extract total rows from log
                if "Loaded input Excel file with" in log_content:
                    total_line = [line for line in log_content.split('\n') if "Loaded input Excel file with" in line]
                    if total_line:
                        print(f"  📊 {total_line[0].split(' - INFO - ')[1]}")
    
    # Check our comprehensive analysis results
    analysis_file = "zipcode_analysis_summary.json"
    if os.path.exists(analysis_file):
        with open(analysis_file, 'r') as f:
            analysis = json.load(f)
        
        print(f"\n🔍 COMPREHENSIVE ANALYSIS RESULTS:")
        print(f"  📋 Total zipcodes in masterfile: {analysis['summary']['total_zipcodes']:,}")
        print(f"  ✅ Parcel successful: {analysis['summary']['parcel_successful']:,}")
        print(f"  ❌ Parcel never succeeded: {analysis['summary']['parcel_never_succeeded']:,}")
        print(f"  📈 Parcel success rate: {(analysis['summary']['parcel_successful']/analysis['summary']['total_zipcodes'])*100:.3f}%")
        
        if analysis['parcel_never_succeeded']:
            print(f"  🚫 Never succeeded zipcodes: {analysis['parcel_never_succeeded']}")
    
    # Check the parcel_failed_only_fixed.xlsx file
    parcel_failed_file = "parcel_failed_only_fixed.xlsx"
    if os.path.exists(parcel_failed_file):
        try:
            df = pd.read_excel(parcel_failed_file)
            print(f"\n📁 PARCEL FAILED INPUT FILE:")
            print(f"  📊 Total zipcodes in file: {len(df):,}")
            print(f"  📋 Sample zipcodes: {df['ZipCode'].head(10).tolist()}")
            
            # Check if this includes zipcodes that we already know succeeded
            if os.path.exists(analysis_file):
                successful_zipcodes = set(analysis['parcel_successful'])
                file_zipcodes = set(df['ZipCode'].astype(str).str.zfill(6))
                
                overlap = file_zipcodes & successful_zipcodes
                print(f"  🔄 Zipcodes already successful: {len(overlap):,}")
                print(f"  🆕 Truly new zipcodes: {len(file_zipcodes - successful_zipcodes):,}")
                
                if len(overlap) > 0:
                    print(f"  ⚠️  WARNING: This test is re-testing {len(overlap):,} zipcodes that already succeeded!")
                    print(f"  💡 RECOMMENDATION: Stop this test as it's redundant")
        
        except Exception as e:
            print(f"  ❌ Error reading parcel failed file: {e}")
    
    print(f"\n🎯 CONCLUSION:")
    print(f"=" * 70)
    
    if os.path.exists(analysis_file):
        with open(analysis_file, 'r') as f:
            analysis = json.load(f)
        
        never_succeeded = analysis['summary']['parcel_never_succeeded']
        
        if never_succeeded <= 1:
            print(f"  🎉 PARCEL TESTING IS ESSENTIALLY COMPLETE!")
            print(f"  📊 Success Rate: 99.996% ({analysis['summary']['parcel_successful']:,} / {analysis['summary']['total_zipcodes']:,})")
            print(f"  🚫 Only {never_succeeded} zipcode(s) never succeeded")
            print(f"  💡 RECOMMENDATION: Stop current test and focus on truck zipcodes")
            
            if never_succeeded == 1:
                print(f"  🔍 Single failed zipcode: {analysis['parcel_never_succeeded'][0]}")
                print(f"  📝 We already tested this zipcode individually - it has delivery success but no time windows")
        else:
            print(f"  🔄 Continue current test - {never_succeeded} zipcodes still need testing")

def main():
    """Main function"""
    check_current_test_discrepancy()

if __name__ == "__main__":
    main()
