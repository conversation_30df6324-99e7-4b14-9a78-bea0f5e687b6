#!/usr/bin/env python3
"""
Test single zipcode with custom item ID
"""

import pandas as pd
import sys
import os

def create_test_file_and_run(zipcode, item_id):
    """Create test file and run the test"""
    
    print(f"🧪 TESTING SINGLE ZIPCODE")
    print("=" * 50)
    print(f"📍 Zipcode: {zipcode}")
    print(f"🆔 Item ID: {item_id}")
    print("=" * 50)
    
    # Create DataFrame with single zipcode
    df = pd.DataFrame({
        'ZipCode': [zipcode]
    })
    
    # Save to Excel file
    test_file = f'single_test_{zipcode}_{item_id}.xlsx'
    df.to_excel(test_file, index=False)
    
    print(f"✅ Created test file: {test_file}")
    print(f"📊 Contains zipcode: {zipcode}")
    print(f"🎯 Testing with Item ID: {item_id}")
    
    return test_file

def main():
    """Main function"""
    
    # Test parameters
    zipcode = "226553"
    item_id = "00588793"
    
    # Create test file
    test_file = create_test_file_and_run(zipcode, item_id)
    
    print(f"\n🚀 READY TO RUN TEST:")
    print(f"Command: INPUT_EXCEL_FILE={test_file} python time_windows_test.py --item-id {item_id} --concurrency 1")
    print(f"\n💡 This will test zipcode {zipcode} with item ID {item_id}")

if __name__ == "__main__":
    main()
