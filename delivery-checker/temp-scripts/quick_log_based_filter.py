#!/usr/bin/env python3
"""
Quick filter based on log files only:
- SUCCESS: If zipcode succeeded at least once in any log
- NOT_TESTED_OR_FAILED: If zipcode never succeeded in any log
"""

import os
import pandas as pd
import re
from collections import defaultdict

def extract_results_from_logs():
    """Extract results directly from log files"""
    
    print("🔍 EXTRACTING RESULTS FROM LOG FILES")
    print("=" * 60)
    
    results_dir = "results"
    parcel_succeeded = set()  # Zipcodes that succeeded at least once
    truck_succeeded = set()   # Zipcodes that succeeded at least once
    parcel_tested = set()     # All zipcodes tested (success or failed)
    truck_tested = set()      # All zipcodes tested (success or failed)
    
    if not os.path.exists(results_dir):
        print(f"❌ Results directory not found: {results_dir}")
        return parcel_succeeded, truck_succeeded, parcel_tested, truck_tested
    
    # Get all test directories
    test_dirs = [d for d in os.listdir(results_dir) if d.startswith("timewindows_test_")]
    test_dirs.sort()
    
    print(f"📁 Found {len(test_dirs)} test directories")
    
    for test_dir in test_dirs:
        full_path = os.path.join(results_dir, test_dir)
        
        # Find log file
        log_files = [f for f in os.listdir(full_path) if f.endswith('.log')]
        if not log_files:
            continue
        
        log_path = os.path.join(full_path, log_files[0])
        
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            # Determine test type from item ID in log
            is_parcel = "Item ID: 10534224" in log_content
            is_truck = "Item ID: 70570836" in log_content
            
            if not (is_parcel or is_truck):
                print(f"  ⚠️  Skipping {test_dir}: Unknown item ID")
                continue
            
            delivery_type = "Parcel" if is_parcel else "Truck"
            current_succeeded = parcel_succeeded if is_parcel else truck_succeeded
            current_tested = parcel_tested if is_parcel else truck_tested
            
            # Extract all results from log using regex
            # Pattern: "Row X: ZIPCODE - SUCCESS" or "Row X: ZIPCODE - FAILED"
            success_pattern = r'Row \d+: (\d{6}) - SUCCESS'
            failed_pattern = r'Row \d+: (\d{6}) - FAILED'
            
            success_matches = re.findall(success_pattern, log_content)
            failed_matches = re.findall(failed_pattern, log_content)
            
            success_count = 0
            failed_count = 0
            
            # Process successful zipcodes
            for zipcode in success_matches:
                formatted_zipcode = str(zipcode).zfill(6)
                current_succeeded.add(formatted_zipcode)
                current_tested.add(formatted_zipcode)
                success_count += 1
            
            # Process failed zipcodes
            for zipcode in failed_matches:
                formatted_zipcode = str(zipcode).zfill(6)
                current_tested.add(formatted_zipcode)
                failed_count += 1
            
            print(f"  📊 {delivery_type} {test_dir}: {success_count} SUCCESS, {failed_count} FAILED")
        
        except Exception as e:
            print(f"  ⚠️  Error processing {test_dir}: {e}")
            continue
    
    print(f"\n📊 LOG-BASED RESULTS SUMMARY:")
    print(f"  📦 Parcel succeeded: {len(parcel_succeeded):,} zipcodes")
    print(f"  📦 Parcel tested: {len(parcel_tested):,} zipcodes")
    print(f"  🚛 Truck succeeded: {len(truck_succeeded):,} zipcodes")
    print(f"  🚛 Truck tested: {len(truck_tested):,} zipcodes")
    
    return parcel_succeeded, truck_succeeded, parcel_tested, truck_tested

def create_quick_filter_results():
    """Create quick filter results based on log analysis"""
    
    print("\n📋 CREATING QUICK FILTER RESULTS")
    print("=" * 60)
    
    # Load masterfile
    masterfile_path = "/Users/<USER>/Work/etc/delivery-checker/masterfile-0805.xlsx"
    
    try:
        masterfile_df = pd.read_excel(masterfile_path)
        print(f"✅ Loaded masterfile: {len(masterfile_df):,} rows")
        
        # Get all unique zipcodes from masterfile
        all_zipcodes = set(str(zipcode).zfill(6) for zipcode in masterfile_df['ZipCode'].unique())
        print(f"📊 Total unique zipcodes: {len(all_zipcodes):,}")
        
    except Exception as e:
        print(f"❌ Error loading masterfile: {e}")
        return
    
    # Extract results from logs
    parcel_succeeded, truck_succeeded, parcel_tested, truck_tested = extract_results_from_logs()
    
    # Create result DataFrames
    result_df = masterfile_df.copy()
    
    # Add quick filter columns
    result_df['Parcel_Quick_Status'] = 'NOT_TESTED_OR_FAILED'
    result_df['Truck_Quick_Status'] = 'NOT_TESTED_OR_FAILED'
    
    # Fill in the results
    parcel_success_count = 0
    truck_success_count = 0
    
    for idx, row in result_df.iterrows():
        zipcode = str(row['ZipCode']).zfill(6)
        
        # Parcel status
        if zipcode in parcel_succeeded:
            result_df.at[idx, 'Parcel_Quick_Status'] = 'SUCCESS'
            parcel_success_count += 1
        
        # Truck status
        if zipcode in truck_succeeded:
            result_df.at[idx, 'Truck_Quick_Status'] = 'SUCCESS'
            truck_success_count += 1
    
    # Save the quick filter results
    output_file = 'quick_filter_results.xlsx'
    
    # Create Excel writer with formatting
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        result_df.to_excel(writer, sheet_name='Results', index=False)
        
        # Get the workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Results']
        
        # Apply conditional formatting
        from openpyxl.styles import PatternFill
        from openpyxl.formatting.rule import CellIsRule
        
        # Find the status columns
        parcel_col = None
        truck_col = None
        for col_idx, col in enumerate(result_df.columns, 1):
            if col == 'Parcel_Quick_Status':
                parcel_col = col_idx
            elif col == 'Truck_Quick_Status':
                truck_col = col_idx
        
        # Green for SUCCESS
        green_fill = PatternFill(start_color='90EE90', end_color='90EE90', fill_type='solid')
        
        if parcel_col:
            worksheet.conditional_formatting.add(
                f'{chr(64+parcel_col)}2:{chr(64+parcel_col)}{len(result_df)+1}',
                CellIsRule(operator='equal', formula=['"SUCCESS"'], fill=green_fill)
            )
        
        if truck_col:
            worksheet.conditional_formatting.add(
                f'{chr(64+truck_col)}2:{chr(64+truck_col)}{len(result_df)+1}',
                CellIsRule(operator='equal', formula=['"SUCCESS"'], fill=green_fill)
            )
    
    print(f"✅ Created {output_file}")
    print(f"\n📊 QUICK FILTER RESULTS:")
    print(f"  📦 Parcel SUCCESS: {parcel_success_count:,} ({parcel_success_count/len(result_df)*100:.1f}%)")
    print(f"  📦 Parcel NOT_TESTED_OR_FAILED: {len(result_df)-parcel_success_count:,} ({(len(result_df)-parcel_success_count)/len(result_df)*100:.1f}%)")
    print(f"  🚛 Truck SUCCESS: {truck_success_count:,} ({truck_success_count/len(result_df)*100:.1f}%)")
    print(f"  🚛 Truck NOT_TESTED_OR_FAILED: {len(result_df)-truck_success_count:,} ({(len(result_df)-truck_success_count)/len(result_df)*100:.1f}%)")
    
    # Additional analysis
    print(f"\n🔍 ADDITIONAL ANALYSIS:")
    parcel_not_tested = all_zipcodes - parcel_tested
    truck_not_tested = all_zipcodes - truck_tested
    
    print(f"  📦 Parcel never tested: {len(parcel_not_tested):,}")
    print(f"  📦 Parcel tested but failed: {len(parcel_tested) - len(parcel_succeeded):,}")
    print(f"  🚛 Truck never tested: {len(truck_not_tested):,}")
    print(f"  🚛 Truck tested but failed: {len(truck_tested) - len(truck_succeeded):,}")
    
    return output_file

def main():
    """Main function"""
    
    print("🚀 QUICK LOG-BASED FILTER")
    print("=" * 70)
    print("This script will:")
    print("1. Extract results directly from log files")
    print("2. Classify zipcodes as:")
    print("   - SUCCESS: Succeeded at least once in any log")
    print("   - NOT_TESTED_OR_FAILED: Never succeeded in any log")
    print("3. Create quick filter results file")
    print("=" * 70)
    
    output_file = create_quick_filter_results()
    
    print(f"\n🎉 QUICK FILTER COMPLETE!")
    print(f"📁 Output file: {output_file}")
    print("=" * 70)

if __name__ == "__main__":
    main()
