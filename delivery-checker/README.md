# Delivery Arrangement API Tester

Tests delivery-arrangement API using zipcodes from Excel file with batch processing.

## Features

- **OAuth Authentication**: Automatic Microsoft OAuth token management
- **Concurrent Processing**: Configurable concurrency to avoid API overload
- **Progress Tracking**: Resume from interruption with progress files
- **Result Caching**: Cache duplicate zipcode results within session
- **Comprehensive Logging**: Detailed logs and API response tracking
- **Delivery Arrangement ID**: Track unique delivery arrangement IDs

## Installation

```bash
pip install -r requirements.txt
```

## Configuration

Edit `.env` file with your settings:
```
CLIENT_ID=your_client_id_here
CLIENT_SECRET=your_client_secret_here
TOKEN_URL=https://login.microsoftonline.com/720b637a-655a-40cf-816a-f22f40755c2c/oauth2/v2.0/token
API_URL=https://private-api.ingka.prodcn.ikea.com/cfb/customer-promise/cn/delivery-arrangement
CONCURRENCY=10
INPUT_EXCEL_FILE=masterfile-0805.xlsx
OUTPUT_EXCEL_FILE=delivery_test_results.xlsx
PROGRESS_FILE=progress.json
RESPONSE_LOG_FILE=api_responses.json
OUTPUT_DIR=results
```

## Usage

### Basic Usage

```bash
# Continue from last stop
python delivery_test.py

# Start fresh (reset progress)
python delivery_test.py --reset-progress

# Specify row range
python delivery_test.py --start-row 0 --end-row 1000

# Set batch size
python delivery_test.py --batch-size 100

# Check single item delivery
python check_single_item.py
```

### Parameters

- `--start-row`: Start row index (0-based)
- `--end-row`: End row index (exclusive)
- `--batch-size`: Batch size, default 50
- `--reset-progress`: Reset progress, start fresh

### Output Files

All output files are saved in timestamped folders: `results/test_YYYYMMDD_HHMMSS/`

- `delivery_test_YYYYMMDD_HHMMSS.log`: Detailed execution log
- `progress_YYYYMMDD_HHMMSS.json`: Progress tracking file
- `delivery_test_results_YYYYMMDD_HHMMSS.xlsx`: Test results Excel file
- `api_responses_YYYYMMDD_HHMMSS.json`: Complete API response log

Example: `results/test_20250805_132933/`

### Excel Output Columns

The script adds these columns to the Excel file:
- `test_status`: HTTP status code
- `test_success`: Test success (True/False)
- `test_response_time`: Response time (seconds)
- `test_delivery_count`: Available delivery services count
- `test_has_delivery`: Has delivery options (True/False)
- `test_delivery_arrangement_id`: Unique delivery arrangement ID
- `test_service_methods`: Delivery methods list
- `test_ship_nodes`: Ship nodes
- `test_merge_nodes`: Merge nodes
- `test_transport_methods`: Transport methods
- `test_earliest_ship_date`: Earliest ship date
- `test_error`: Error message (if any)
- `test_timestamp`: Test timestamp

## Monitoring

### View real-time logs (latest test)
```bash
tail -f results/test_*/delivery_test_*.log
```

### View progress (latest test)
```bash
cat results/test_*/progress_*.json
```

### View API responses
```bash
python -c "import json; print(json.dumps(json.load(open('results/test_*/api_responses_*.json')), indent=2))"
```

### Statistics
```bash
grep "SUCCESS" results/test_*/delivery_test_*.log | wc -l  # Success count
grep "FAILED" results/test_*/delivery_test_*.log | wc -l   # Failure count
```

## Notes

1. **API Limits**: Default concurrency is 10, adjust based on API capacity
2. **Token Expiry**: OAuth token valid for 1 hour, auto-refreshed
3. **Resume**: Can interrupt and resume from last position
4. **Caching**: Duplicate zipcodes use cached results within session
5. **Error Handling**: Network and API errors logged, won't stop execution

## Troubleshooting

### Common Issues

1. **Authentication Failed**: Check CLIENT_ID and CLIENT_SECRET
2. **Excel File Locked**: Ensure Excel file not open in other programs
3. **Network Timeout**: Check network connection
4. **Permission Error**: Ensure read/write permissions for files

### Restart Testing

Each run creates new timestamped folder, existing results preserved:
```bash
python3 delivery_test.py --reset-progress
```
