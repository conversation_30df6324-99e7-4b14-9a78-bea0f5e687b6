public class MemoryTest {
    public static void main(String[] args) {
        // 分配一个大数组来占用内存
        byte[] array = new byte[1024 * 1024 * 1024]; // 1GB

        System.out.println("数组分配完成,按Enter键继续...");
        try {
            System.in.read();
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 释放数组
        array = null;

        System.out.println("数组已释放,按Enter键退出...");
        try {
            System.in.read();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}