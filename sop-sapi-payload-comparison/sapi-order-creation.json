{"countryCode": "SE", "salesOrderGUId": "string", "orderCreationMethod": "INTERNET, STORE", "salesOrderKey": {"orderNo": *********, "orderNoSource": "A01 for EU, A02 for AP, A03 for NA, A04 for CN and A05 for RU"}, "externalOrderKey": {"orderNo": "*********-1", "orderNoSource": "88"}, "clientSystem": {"name": "OneWeb", "version": "1.0.0"}, "businessUnit": {"code": "Store Code for type STO(Ex 895), Country code for RU(Ex GB)", "type": "RU for WEB or STO for STORE"}, "customer": {"privateCustomer": {"globalCustomerId": "string", "preferredLocale": "sv-SE", "registeredType": "GUEST", "isSameBillingAndDeliveryContact": true, "alternateContact": {"firstName": "string", "surName": "string", "phoneticFirstName": "string", "phoneticSurName": "string"}, "billingContact": {"address": {"addressLine1": "string", "addressLine2": "string", "addressLine3": "string", "attention": "string", "careOfName": "string", "city": "string", "countryCode": "string", "countyName": "string", "prefecture": "string", "stateCode": "string", "zipCode": "string"}, "contactDetails": {"email": "string", "mobile": "string", "phone": "string"}, "personalInfo": {"familyNo": "string", "firstName": "string", "foreignCountry": "For Business customer - Foreign VAT and Private customer - PassportNumber", "genderCode": "string", "lastName": "string", "middleName": "string", "pan": "string", "phoneticFirstName": "string", "phoneticLastName": "string", "recipientCode": "If Recipient Code Type is EMAIL then valid Email Id or if Code Type is Code then 6 or 7 Alpha Numeric Code", "recipientCodeType": "CODE, EMAIL, NONE, NO_INVOICE", "secondSurName": "string", "taxCode": "string", "taxIdentifierNo": "string", "taxCodeType": "CIF", "title": "Mr, Mrs", "employeeId": "12345"}}, "deliveryContact": {"address": {"addressLine1": "string", "addressLine2": "string", "addressLine3": "string", "attention": "string", "careOfName": "string", "city": "string", "countryCode": "string", "countyName": "string", "prefecture": "string", "stateCode": "string", "zipCode": "string"}, "contactDetails": {"email": "string", "mobile": "string", "phone": "string"}, "personalInfo": {"familyNo": "string", "firstName": "string", "foreignCountry": "For Business customer - Foreign VAT and Private customer - PassportNumber", "genderCode": "string", "lastName": "string", "middleName": "string", "pan": "string", "phoneticFirstName": "string", "phoneticLastName": "string", "recipientCode": "If Recipient Code Type is EMAIL then valid Email Id or if Code Type is Code then 6 or 7 Alpha Numeric Code", "recipientCodeType": "CODE, EMAIL, NONE, NO_INVOICE", "secondSurName": "string", "taxCode": "string", "taxIdentifierNo": "string", "taxCodeType": "CIF", "title": "Mr, Mrs", "employeeId": "12345"}}}, "businessCustomer": {"globalCustomerId": "string", "preferredLocale": "sv-SE", "registeredType": "GUEST", "isSameBillingAndDeliveryContact": true, "alternateContact": {"firstName": "string", "surName": "string", "phoneticFirstName": "string", "phoneticSurName": "string"}, "billingContact": {"address": {"addressLine1": "string", "addressLine2": "string", "addressLine3": "string", "attention": "string", "careOfName": "string", "city": "string", "countryCode": "string", "countyName": "string", "prefecture": "string", "stateCode": "string", "zipCode": "string"}, "contactDetails": {"email": "string", "mobile": "string", "phone": "string"}, "personalInfo": {"familyNo": "string", "firstName": "string", "foreignCountry": "For Business customer - Foreign VAT and Private customer - PassportNumber", "genderCode": "string", "lastName": "string", "middleName": "string", "pan": "string", "phoneticFirstName": "string", "phoneticLastName": "string", "recipientCode": "If Recipient Code Type is EMAIL then valid Email Id or if Code Type is Code then 6 or 7 Alpha Numeric Code", "recipientCodeType": "CODE, EMAIL, NONE, NO_INVOICE", "secondSurName": "string", "taxCode": "string", "taxIdentifierNo": "string", "taxCodeType": "CIF", "title": "Mr, Mrs", "employeeId": "12345"}}, "businessInfo": {"businessName": "string", "organizationNo": "string", "phoneticBusinessName": "string"}, "deliveryContact": {"address": {"addressLine1": "string", "addressLine2": "string", "addressLine3": "string", "attention": "string", "careOfName": "string", "city": "string", "countryCode": "string", "countyName": "string", "prefecture": "string", "stateCode": "string", "zipCode": "string"}, "contactDetails": {"email": "string", "mobile": "string", "phone": "string"}, "personalInfo": {"familyNo": "string", "firstName": "string", "foreignCountry": "For Business customer - Foreign VAT and Private customer - PassportNumber", "genderCode": "string", "lastName": "string", "middleName": "string", "pan": "string", "phoneticFirstName": "string", "phoneticLastName": "string", "recipientCode": "If Recipient Code Type is EMAIL then valid Email Id or if Code Type is Code then 6 or 7 Alpha Numeric Code", "recipientCodeType": "CODE, EMAIL, NONE, NO_INVOICE", "secondSurName": "string", "taxCode": "string", "taxIdentifierNo": "string", "taxCodeType": "CIF", "title": "Mr, Mrs", "employeeId": "12345"}}}}, "itemLines": [{"id": 1, "itemNo": "Item Number 30214504", "itemLineReferences": [{"lineId": 1, "quantity": 1}], "itemType": "ART", "orderedQuantity": 1, "reservationId": "MHS367-*********-90111796", "unitPrice": {"familyPrice": {"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "taxAmount": 0, "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}]}, "listPrice": {"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "taxAmount": 0, "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}]}, "salesPrice": {"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "taxAmount": 0, "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}]}}, "itemPriceDetails": {"exclSavings": {"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "taxAmount": 0, "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}]}, "inclSavings": {"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "taxAmount": 0, "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}], "totalSavings": {"all": 10, "familyPrice": 0, "discounts": 10, "promotions": 0}}, "savings": [{"amount": 0, "code": "string", "description": "string", "discountDetails": {"count": 1, "value": 10, "type": "FIXED_PRICE_OFF", "applyOn": "GOODS_VALUE"}, "id": "string", "type": "string", "validFromDate": "string", "validFromDateUTC": "string", "validToDate": "string", "validToDateUTC": "string", "discountLevel": "orderLevel", "discountType": "FamilyDiscounts"}]}, "importDetails": {"receiptReferenceNo": "012-601-12-*********", "taxIndicator": "MAT_DOM", "originalQuantity": 3, "originalLineId": 2, "orderReferenceNo": *********}, "isCustomerPicked": "Y", "handInLocation": "Transport Desk", "isreferenceItem": false, "textLine": "Hall wall decor"}], "deliveryArrangements": [{"arrangementPrice": {"priceInclTax": 0, "priceExclTax": 0, "taxAmount": 0, "currencyCode": "string", "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}]}, "deliveryServices": [{"deliveryItems": [{"isExceptionalQty": "true or false", "quantity": 1, "itemLineId": 1, "fulfillingUnit": {"code": "124", "type": "CDC"}}], "deliveryPriceDetails": {"exclSavings": {"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "taxAmount": 0, "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}]}, "inclSavings": {"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "taxAmount": 0, "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}], "totalSavings": {"all": 10, "familyPrice": 0, "discounts": 10, "promotions": 0}}, "savings": [{"amount": 0, "code": "string", "description": "string", "discountDetails": {"count": 1, "value": 10, "type": "FIXED_PRICE_OFF", "applyOn": "GOODS_VALUE"}, "id": "string", "type": "string", "validFromDate": "string", "validFromDateUTC": "string", "validToDate": "string", "validToDateUTC": "string", "discountLevel": "orderLevel", "discountType": "FamilyDiscounts"}]}, "isExceptionalVolume": false, "pickupPoint": {"address": {"addressLine1": "string", "addressLine2": "string", "addressLine3": "string", "addressLine4": "string", "city": "string", "country": "string", "state": "string", "zipCode": "string"}, "distance": 0, "distanceUnitOfMeasure": "string", "id": "string", "identifier": "string", "lsc": {"code": "309", "type": "LSC"}, "name": "string", "openingHours": [{"dayOfWeek": "string", "timeSpan": "09:00-12:00;13:00-20:00", "timeSlot": {"end": "8.00", "start": "10.00"}}]}, "serviceItemId": "SGR40400005", "timeWindow": {"fromLocalDateTime": "2019-03-25T11:00:00Z", "id": "111d904a57a-eea5-484f-b8b0-0c3800", "resourcePoolId": "4ofK2kZLfkSdcgwGAI3qOg==", "timeZone": "Europe/London", "toLocalDateTime": "2019-03-26T11:00:00Z", "transferringUnit": {"code": "123", "type": "LSC"}}, "unitOfMeasure": "ORDERS", "paymentCutOffDateTime": "020-08-11T18:00:00.000Z", "capabilities": [["WHEELCHAIR", "AUTH_TO_LEAVE", "RANGE_OF_DAYS"]], "mergeOrderNo": *********, "mergeDeliveryNo": 2, "tspId": "string"}], "type": "STANDARD, EXPRESS, CURBSIDE,EXPRESS_CURBSIDE, PUP, PUOP, CLICK_COLLECT, CLICK_COLLECT_STORE, LOCKER, STANDARD_RD, EXPRESS_RD, CURBSIDE_RD, EXPRESS_CURBSIDE_RD, STANDARD_B2B", "fulfillmentMethod": "HOME_DELIVERY, COLLECT", "deliveryArrangementId": "20201009084659099413071"}], "providedServices": [{"serviceItemId": "SGR40045678", "serviceProductId": "FURNITURE_ASSEMBLY, SOFA_ASSEMBLY", "priceCalculationValue": 1, "servicePriceDetails": {"exclSavings": {"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "taxAmount": 0, "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}]}, "inclSavings": {"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "taxAmount": 0, "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}], "totalSavings": {"all": 10, "familyPrice": 0, "discounts": 10, "promotions": 0}}, "savings": [{"amount": 0, "code": "string", "description": "string", "discountDetails": {"count": 1, "value": 10, "type": "FIXED_PRICE_OFF", "applyOn": "GOODS_VALUE"}, "id": "string", "type": "string", "validFromDate": "string", "validFromDateUTC": "string", "validToDate": "string", "validToDateUTC": "string", "discountLevel": "orderLevel", "discountType": "FamilyDiscounts"}]}, "serviceProviderId": "1", "capacityUnitCode": "PAT_VALUE", "deliveryNumber": "*********", "serviceProviderName": "1", "servicePaymentTo": "PAY_TO_SP, PAY_TO_IKEA", "serviceTimeWindows": [{"id": "26b76fe9-7c42-42cc-8d7c-0cc800527dbb", "capacityAllocated": 1198, "fromDateTime": "2019-03-26T07:00:00Z", "timeZone": "Europe/Stockholm", "toDateTime": "2019-03-26T11:00:00Z"}], "itemLineReferenceIds": ["1, 2, 3"], "serviceRequestedLocale": "sv-SE"}], "questionnaires": {"deliveryQuestionnaires": [{"deliveryServiceItemId": "SGR40400005", "type": "FLAT or Business Customer or House", "questionnaire": {"answers": [{"option": "YES", "questionReference": "DEL_ADDRESS_PART_TIME_ACCESSIBLE", "textValue": "There is an elevator in the back"}]}}], "serviceQuestionnaires": [{"providedServiceItemId": "SGR40045678", "type": "ASSEMBLY", "questionnaire": {"answers": [{"option": "YES", "questionReference": "DEL_ADDRESS_PART_TIME_ACCESSIBLE", "textValue": "There is an elevator in the back"}]}}]}, "orderPayment": {"invoice": {"paymentDetails": {"currencyCode": "SEK", "amount": 123, "requisitionNo": "string"}, "paymentTransactions": [{"amount": 123, "currencyCode": "SEK", "paymentGateway": "POS", "paymentSystem": "POS", "paymentGatewayReferenceId": "string", "status": "CHALLENGED", "transactionDateTime": "2018-12-13T12:00:12Z", "tenderType": "string", "paymentBrand": "MULTIBANCO", "accountNumber": "string", "accountNumberSource": "string", "authorizationCode": "string", "authorizationExpiryDate": "string"}]}, "openInvoice": {"paymentDetails": {"currencyCode": "SEK", "amount": 123}, "paymentTransactions": [{"amount": 123, "currencyCode": "SEK", "paymentGateway": "POS", "paymentSystem": "POS", "paymentGatewayReferenceId": "string", "status": "CHALLENGED", "transactionDateTime": "2018-12-13T12:00:12Z", "tenderType": "string", "paymentBrand": "MULTIBANCO", "authorizationExpiryDate": "string"}]}, "paymentInAdvance": {"paymentDetails": {"currencyCode": "SEK", "amount": 123}, "paymentTransactions": [{"amount": 123, "currencyCode": "SEK", "paymentGateway": "POS", "paymentSystem": "POS", "paymentGatewayReferenceId": "string", "status": "CHALLENGED", "transactionDateTime": "2018-12-13T12:00:12Z", "tenderType": "string", "paymentBrand": "MULTIBANCO", "uncertainExpiryDate": "string", "cardExpiryDate": "919", "maskedCardNumber": "string", "authorizationCode": "string"}], "creditPaymentTransactions": [{"amount": 123, "currencyCode": "SEK", "paymentGateway": "POS", "paymentSystem": "POS", "paymentGatewayReferenceId": "string", "status": "CHALLENGED", "transactionDateTime": "2018-12-13T12:00:12Z", "tenderType": "string", "paymentBrand": "MULTIBANCO", "applicationDueDateTime": "string", "productType": "string"}]}, "paymentOnDelivery": {"paymentDetails": {"currencyCode": "SEK", "amount": 123}}, "paymentOnCollect": {"paymentDetails": {"currencyCode": "SEK", "amount": 123}}}, "customerSurvey": {"contactAllowed": true, "contactMethodType": "string", "contactMethodData": "string"}, "customerMeetingPoint": {"id": "string", "name": "string"}, "orderSummary": {"subTotals": {"goodsAmount": {"priceInclTax": 0, "priceExclTax": 0, "taxAmount": 0, "currencyCode": "string", "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}], "savingAmount": 0}, "deliveryAmount": {"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "savingAmount": 0, "taxAmount": 0, "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}]}, "serviceAmount": {"priceInclTax": 0, "priceExclTax": 0, "taxAmount": 0, "currencyCode": "string", "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}], "savingAmount": 0}}, "totalAmount": {"priceInclTax": 0, "priceExclTax": 0, "taxAmount": 0, "currencyCode": "string", "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}], "savingAmount": 0, "savingSummary": {"discounts": 10, "employee": 0, "familyDiscounts": 0, "familyPrice": 0, "family": 0, "manual": 0, "coupons": 0, "voucher": 0}}}, "orderSummaryExclSavings": {"subTotals": {"goodsAmount": {"priceInclTax": 0, "priceExclTax": 0, "taxAmount": 0, "currencyCode": "string", "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}], "savingAmount": 0}, "deliveryAmount": {"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "savingAmount": 0, "taxAmount": 0, "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}]}, "serviceAmount": {"priceInclTax": 0, "priceExclTax": 0, "taxAmount": 0, "currencyCode": "string", "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}], "savingAmount": 0}}, "totalAmount": {"priceInclTax": 0, "priceExclTax": 0, "taxAmount": 0, "currencyCode": "string", "taxDetails": [{"taxAmount": 120, "taxCode": "0", "taxPercentage": "25%", "taxType": "VAT", "taxableAmount": 12, "taxJurisdiction": {"taxJurisdictionType": "STATE", "taxJurisdictionCode": "1112"}}], "savingAmount": 0, "savingSummary": {"discounts": 10, "employee": 0, "familyDiscounts": 0, "familyPrice": 0, "family": 0, "manual": 0, "coupons": 0, "voucher": 0}}}, "orderSavings": [{"id": "M1", "sequence": 1, "type": "Manual", "discountDetails": {"count": 1, "value": 10, "type": "FIXED_PRICE_OFF", "applyOn": "GOODS_VALUE"}}], "couponInformations": [{"discountCode": "BUY3FÖR2", "discountAmount": 45}], "poOrderNo": "*********", "WorkStationName": "RETSE012-WS001", "groups": [{"groupNo": 5093022, "groupName": "kitchen", "id": 1, "referencedItems": [[2, 3, 4]]}], "drawings": [{"customerId": "string", "customerIdSource": "string", "externalCustomerRef": "string", "externalCustomerRefName": "string", "drawingCreateDateTime": "string", "drawingUrl": "string", "drawingName": "string", "drawingNote": "string", "createDateTime": "string", "imageUrl": "string", "imageSize": "string", "roomType": "string", "drawingUrlType": "string", "drawingId": "string", "drawingIdSource": "string", "countryCode": "string"}], "customWorktops": [{"cwtProjectId": "string", "cwtProjectSourceSystem": "string", "cwtProjectManualUrl": "string", "referencedItems": [[2, 3, 4]]}], "Messages": [{"messageText": "Message Text", "messageType": "INTERNAL, ORDER_CONF"}], "FileAttachments": [{"fileType": "pdf, jpg", "uploadDateTime": "2021-02-02T12:01:05Z", "originalFileName": "New kitchen.jpg", "sourceSystem": "IKEA, SERVICE_PROVIDER", "category": "DRAWING, ASSEMBLY_INSTRUCTION", "attachmentLocation": "/retail/iows/it/it/storage/servicedocs/isell/2011-11-08/22fa5cc3f5d149cfb82b2e529f70057d.jpg", "ServiceItemId": "SGR40045678 , SGR40400005", "attachmentComment": "kitchen drawing added", "sourceRef": "Italmond<PERSON>"}]}