{"billingContact": {"address": {"additionalAddressInfo": "string", "addressLine1": "string", "addressLine2": "string", "addressLine3": "string", "attention": "string", "careOf": "string", "cityName": "Stockholm", "countryCode": "SE", "countyName": "string", "floorNo": "string", "prefecture": "string", "provinceCode": "string", "stateCode": "CA", "streetName": "string", "streetNo": "string", "zipCode": "10012"}, "birthDate": "2024-01-04T02:55:33.072Z", "businessCardNo": "123123", "businessName": "ACME Inc", "emailAddress": "<EMAIL>", "firstName": "<PERSON>", "foreignCountry": "For Business customer - Foreign VAT and Private customer - Passport Number", "genderCode": "string", "informalName": "string", "landlinePhoneNo": "*********", "languageCode": "string", "lastName": "<PERSON>", "middleName": "string", "mobilePhoneNo": "*********", "namePrefix": "string", "nameSuffix": "string", "organisationNo": "123123", "phoneticBusinessName": "Higalo", "phoneticFirstName": "<PERSON><PERSON>", "phoneticLastName": "<PERSON><PERSON><PERSON>", "phoneticName": "string", "recipientCode": "If Recipient Code Type is EMAIL then valid", "recipientCodeType": "Empty, CODE, EMAIL", "sezType": "string", "sezValidityFrom": "string", "sezValidityTo": "string", "taxCodeType": "For Business Customer(ES) CIF, FOREIGN_VAT, For Private Customers DNI_NIF, NIE, PASSPORT_ID_CARD and For Business Customers(PT) NIF, FOREIGN_VAT, For Private Customers NIF, PASSPORT_ID_CARD", "taxIdentifierNo": "string", "taxNo": "123123"}, "buCode": "722", "clientSystem": {"name": "string", "version": "string"}, "countryCode": "SE", "customWorktops": [{"cwtProjectId": "string", "cwtProjectManualUrl": "string", "cwtProjectSourceSystem": "string", "referencedItems": [0]}], "customer": {"businessCustomer": {"businessCustomerDeliveryInfo": {"businessCustomerBusinessInfo": {"businessName": "string", "organisationNo": "string"}, "businessCustomerCommunication": {"email": "string", "mobile": "string", "phone": "string"}, "businessCustomerDeliveryAddress": {"addressLine1": "string", "addressLine2": "string", "addressLine3": "string", "attention": "string", "careOfName": "string", "city": "string", "countryCode": "string", "countyName": "string", "prefecture": "string", "stateCode": "string", "zipCode": "string"}, "businessCustomerPersonalInfo": {"customerLocale": "string", "familyNo": "string", "firstName": "string", "foreignCountry": "For Business customer - Foreign VAT and Private customer - Passport Number", "genderCode": "string", "language": "string", "lastName": "string", "middleName": "string", "pan": "string", "phoneticBusinessName": "string", "phoneticName": "string", "recipientCode": "If Recipient Code Type is EMAIL then valid", "recipientCodeType": "Empty, CODE, EMAIL", "secondSurName": "string", "taxCode": "string", "taxCodeType": "For Business Customer(ES) CIF, FOREIGN_VAT, For Private Customers DNI_NIF, NIE, PASSPORT_ID_CARD and For Business Customers(PT) NIF, FOREIGN_VAT, For Private Customers NIF, PASSPORT_ID_CARD", "taxIdentifierNo": "string", "title": "string"}}}, "customerType": "PRIVATE", "globalCustomerId": "string", "privateCustomer": {"privateCustomerBillingInfo": {"privateCustomerBillingAddress": {"addressLine1": "string", "addressLine2": "string", "addressLine3": "string", "attention": "string", "careOfName": "string", "city": "string", "countryCode": "string", "countyName": "string", "prefecture": "string", "stateCode": "string", "zipCode": "string"}, "privateCustomerCommunication": {"email": "string", "mobile": "string", "phone": "string"}, "privateCustomerPersonalInfo": {"customerLocale": "string", "familyNo": "string", "firstName": "string", "foreignCountry": "For Business customer - Foreign VAT and Private customer - Passport Number", "genderCode": "string", "language": "string", "lastName": "string", "middleName": "string", "pan": "string", "phoneticFirstName": "string", "phoneticLastName": "string", "phoneticName": "string", "recipientCode": "If Recipient Code Type is EMAIL then valid", "recipientCodeType": "Empty, CODE, EMAIL", "secondSurName": "string", "taxCode": "string", "taxCodeType": "For Business Customer(ES) CIF, FOREIGN_VAT, For Private Customers DNI_NIF, NIE, PASSPORT_ID_CARD and For Business Customers(PT) NIF, FOREIGN_VAT, For Private Customers NIF, PASSPORT_ID_CARD", "taxIdentifierNo": "string", "title": "string"}}, "privateCustomerDeliveryInfo": {"privateCustomerCommunication": {"email": "string", "mobile": "string", "phone": "string"}, "privateCustomerDeliveryAddress": {"addressLine1": "string", "addressLine2": "string", "addressLine3": "string", "attention": "string", "careOfName": "string", "city": "string", "countryCode": "string", "countyName": "string", "prefecture": "string", "stateCode": "string", "zipCode": "string"}, "privateCustomerPersonalInfo": {"customerLocale": "string", "familyNo": "string", "firstName": "string", "foreignCountry": "For Business customer - Foreign VAT and Private customer - Passport Number", "genderCode": "string", "language": "string", "lastName": "string", "middleName": "string", "pan": "string", "phoneticFirstName": "string", "phoneticLastName": "string", "phoneticName": "string", "recipientCode": "If Recipient Code Type is EMAIL then valid", "recipientCodeType": "Empty, CODE, EMAIL", "secondSurName": "string", "taxCode": "string", "taxCodeType": "For Business Customer(ES) CIF, FOREIGN_VAT, For Private Customers DNI_NIF, NIE, PASSPORT_ID_CARD and For Business Customers(PT) NIF, FOREIGN_VAT, For Private Customers NIF, PASSPORT_ID_CARD", "taxIdentifierNo": "string", "title": "string"}}}}, "customerProfile": {"customerType": "PRIVATE", "familyNumber": "987654327849123100", "globalCustomerId": "1233211", "preferredLocale": "en-GB, en_GB", "registeredType": "GUEST"}, "customerSurvey": {"contactAllowed": true, "contactMethodData": "string", "contactMethodType": "string"}, "deliveryContact": {"address": {"additionalAddressInfo": "string", "addressLine1": "string", "addressLine2": "string", "addressLine3": "string", "attention": "string", "careOf": "string", "cityName": "Stockholm", "countryCode": "SE", "countyName": "string", "floorNo": "string", "prefecture": "string", "provinceCode": "string", "stateCode": "CA", "streetName": "string", "streetNo": "string", "zipCode": "10012"}, "birthDate": "2024-01-04T02:55:33.072Z", "businessCardNo": "123123", "businessName": "ACME Inc", "emailAddress": "<EMAIL>", "firstName": "<PERSON>", "foreignCountry": "For Business customer - Foreign VAT and Private customer - Passport Number", "genderCode": "string", "informalName": "string", "landlinePhoneNo": "*********", "languageCode": "string", "lastName": "<PERSON>", "middleName": "string", "mobilePhoneNo": "*********", "namePrefix": "string", "nameSuffix": "string", "organisationNo": "123123", "phoneticBusinessName": "Higalo", "phoneticFirstName": "<PERSON><PERSON>", "phoneticLastName": "<PERSON><PERSON><PERSON>", "phoneticName": "string", "recipientCode": "If Recipient Code Type is EMAIL then valid", "recipientCodeType": "Empty, CODE, EMAIL", "sezType": "string", "sezValidityFrom": "string", "sezValidityTo": "string", "taxCodeType": "For Business Customer(ES) CIF, FOREIGN_VAT, For Private Customers DNI_NIF, NIE, PASSPORT_ID_CARD and For Business Customers(PT) NIF, FOREIGN_VAT, For Private Customers NIF, PASSPORT_ID_CARD", "taxIdentifierNo": "string", "taxNo": "123123"}, "deliveryServices": [{"deliveryMethod": "LCD_ZONE_A", "itemReferences": "\"deliveryMethod\": \"LCD_ZONE_B_NEXT_DAY\",\n      \"itemReferences\": [\n        {\n          \"lineId\": 1\n        }\n      ]", "lineId": 0, "timeWindows": [{"capacityAvailable": false, "cutOffDateTime": "2017-08-18T05:00:00.000Z", "deliveryDateTimeFrom": "2017-08-18T05:00:00.000Z", "deliveryDateTimeTo": "2017-08-18T11:00:00.000Z", "id": "d40f8e25-6e09-407f-9c1b-0a6a00a5148f", "price": {"currencyCode": "SEK", "priceExclTax": 20, "priceInclTax": 25}, "serviceProviderId": "AAA", "serviceProviderName": "A-Trans", "taxCode": "0", "taxPercentage": 25}]}], "drawings": [{"countryCode": "string", "createDateTime": "string", "customerId": "string", "customerIdSource": "string", "drawingCreateDateTime": "string", "drawingId": "80F9ACA4-5632-4018-8AFB-17677184DC61", "drawingIdSource": "IHP3", "drawingName": "Kitchen from Ihp3 with KASKER", "drawingNote": "string", "drawingUrl": "https://kitchen.planner.ikeadt.com/planner/#/gb/en/planner?rangeEnv=TestRange&amp;environmentStage=main&amp;projectId=80F9ACA4-5632-4018-8AFB-17677184DC61", "drawingUrlType": "APPLICATION_DEEPLINK", "externalCustomerRef": "string", "externalCustomerRefName": "string", "imageSize": "string", "imageUrl": "string", "roomType": "string"}], "familyCustomer": true, "groups": [{"groupName": "string", "groupNo": 0, "referencedItems": [{"itemReferenceId": 2, "seqNo": 0}], "seqNo": 1}], "handoverLocation": "CUST_PLACE", "items": [{"childItems": [{"itemId": "string", "itemType": "string", "quantityInSPR": "string"}], "hasCustomWorktopDrawing": true, "itemId": "99259868", "itemReferences": {"itemId": 9193259, "itemType": "SPR", "lineId": 3, "qty": 1, "itemReferences": [{"lineId": 1}, {"lineId": 2}]}, "itemType": "ART", "lineId": 1, "prices": [{"currencyCode": "string", "priceExclTax": 0, "priceInclTax": 0, "priceType": "string", "taxDetails": [{"taxAmount": 5, "taxCode": "0", "taxPercentage": 25, "taxType": "VAT", "taxableAmount": 20}]}], "qty": 1, "qtyType": "string", "seqNo": 0}], "notifications": [{"contactMethodData": "Notification Method data to notify user e.g - <EMAIL>/ +467807543568", "contactMethodType": "Notification Contact Method Type eg - EMAIL/MOBILE", "type": "The type of notification e.g - PICKING_READY_FOR_HANDOUT / FOR_CUSTOMER_SURVEY"}], "orderCreationMethod": "string", "orderServices": [{"capacityNeeded": 0, "capacityTemplatesList": [{"id": "45e235ae-26f2-480c-a3be-055800ca3634"}], "capacityUnit": "string", "mandatory": true, "price": {"currencyCode": "SEK", "priceExclTax": 20, "priceInclTax": 25}, "priceCalculation": 0, "priceCalculationCategoryList": [{"category": "string", "quantity": 0}], "servicePaymentTo": "string", "serviceProductId": "string", "serviceProviderId": "string", "serviceProviderName": "string", "serviceTimeWindow": {"allowedToOverBook": true, "serviceTimeWindowParts": [{"capacityAllocated": 12, "dateTimeFrom": "2017-08-18T05:00:00.000Z", "dateTimeTo": "2017-08-18T11:00:00.000Z", "timeWindowId": "d40f8e25-6e09-407f-9c1b-0a6a00a5148f"}], "willBeOverBooked": true}, "taxDetails": [{"taxAmount": 5, "taxCode": "0", "taxPercentage": 25, "taxType": "VAT", "taxableAmount": 20}]}], "planners": [{"itemReferences": [{"lineId": 2}], "plannerName": "Living room", "seqNo": 1}], "providedServices": [{"capacityNeeded": 12, "capacityTemplates": ["string"], "capacityTemplatesList": [{"id": "45e235ae-26f2-480c-a3be-055800ca3634"}], "capacityUnit": "GOODS_VALUE", "itemReferences": "\"deliveryMethod\": \"LCD_ZONE_B_NEXT_DAY\",\n      \"itemReferences\": [\n        {\n          \"lineId\": 1\n        }\n      ]", "lineId": 0, "mandatory": true, "price": {"currencyCode": "SEK", "priceExclTax": 20, "priceInclTax": 25}, "priceCalculation": 0, "priceCalculationCategoryList": [{"category": "string", "quantity": 0}], "servicePaymentTo": "PAY_TO_IKEA or PAY_TO_IKEA", "serviceProductId": "FURNITURE_ASSEMBLY", "serviceProviderId": "893997c7-e952-4d53-9eaf-022c00cef811", "serviceProviderName": "A-Trans and Westlunds Superserviceservare are service providers for Assembly service", "serviceTimeWindow": {"allowedToOverBook": true, "serviceTimeWindowParts": [{"capacityAllocated": 12, "dateTimeFrom": "2017-08-18T05:00:00.000Z", "dateTimeTo": "2017-08-18T11:00:00.000Z", "timeWindowId": "d40f8e25-6e09-407f-9c1b-0a6a00a5148f"}], "willBeOverBooked": true}, "taxDetails": [{"taxAmount": 5, "taxCode": "0", "taxPercentage": 25, "taxType": "VAT", "taxableAmount": 20}]}], "questionnaire": {"questionAnswers": [{"questionReference": "DEL_ADDRESS_PART_TIME_ACCESSIBLE", "option": "YES", "textValue": "There is an elevator in the back"}], "questionnaireType": "FLAT"}, "questionnaires": [{"questionAnswers": [{"questionReference": "DEL_ADDRESS_PART_TIME_ACCESSIBLE", "option": "YES", "textValue": "There is an elevator in the back"}], "questionnaireType": "FLAT"}], "userId": "test1", "workStationName": "string", "siteRefName": "string"}