#!/bin/bash

# splunk_search.sh
# This script uses the Splunk REST API to perform a search.
set -eux

# Check if curl is installed
if ! command -v curl &> /dev/null; then
    echo "curl could not be found. Please install it to continue."
    exit 1
fi

# Load .env file
if [ -f .env ]; then
    export $(cat .env | grep -v '#' | awk '/=/ {print $1}')
else
    echo "Error: .env file not found."
    exit 1
fi

# Configuration
SPLUNK_HOST="restapi.splunkchn.ikea.com"
SPLUNK_PORT="443"

# URL encode the search query
urlencode() {
    local length="${#1}"
    for (( i = 0; i < length; i++ )); do
        local c="${1:i:1}"
        case $c in
            [a-zA-Z0-9.~_-]) printf "$c" ;;
            *) printf '%%%02X' "'$c" ;;
        esac
    done
}

# Prompt user for a search query
read -rp "Enter your Splunk search query: " ordernumber 
ordernumber=262037729

# URL encode the search string
encoded_query=$(urlencode "$ordernumber container_name_=checkoutservice https://mpp-i.ingka-dt.cn/api/checkoutProvided/v2")

# Prepare the search command (assuming you're searching over the last 24 hours)
search_command="search $encoded_query earliest=-72h"

# Perform the search
response=$(curl -s -k -u "$SPLUNK_USERNAME:$SPLUNK_PASSWORD" \
  "https://$SPLUNK_HOST:$SPLUNK_PORT/en-US/services/search/jobs" \
  -d search="$search_command")

# Check if the search was successful
if echo "$response" | grep -q "<response>Success</response>"; then
    echo "Search initiated successfully."
else
    echo "Failed to initiate search."
    echo "Response from Splunk:"
    echo "$response"
    exit 1
fi

# Extract the search job ID
job_id=$(echo "$response" | grep -oPm1 "(?<=<sid>)[^<]+")

# Wait for the search to complete
while true; do
    job_status=$(curl -s -k -u "$SPLUNK_USERNAME:$SPLUNK_PASSWORD" \
        "https://$SPLUNK_HOST:$SPLUNK_PORT/services/search/jobs/$job_id")
    
    if echo "$job_status" | grep -q "<s:key name=\"isDone\">1</s:key>"; then
        echo "Search completed."
        break
    else
        echo "Waiting for search to complete..."
        sleep 5
    fi
done

# Retrieve the search results
results=$(curl -s -k -u "$SPLUNK_USERNAME:$SPLUNK_PASSWORD" \
    "https://$SPLUNK_HOST:$SPLUNK_PORT/services/search/jobs/$job_id/results?output_mode=csv")

# Output the results
echo "Search results:"
echo "$results"
