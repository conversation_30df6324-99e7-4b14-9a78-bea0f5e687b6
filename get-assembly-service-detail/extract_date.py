import pandas as pd
import json

# Load the CSV file
df = pd.read_csv('merged.csv')

# Define a function to extract the start time from the 'assembly' field
def extract_start_time(assembly_json):
    # Check if the value is not NaN and is a string
    if pd.notna(assembly_json) and isinstance(assembly_json, str):
        try:
            # Parse the JSON-like string into a Python dictionary
            assembly_dict = json.loads(assembly_json.replace('""', '"'))
            # Navigate through the dictionary to get the start time
            start_time = assembly_dict['services'][0]['deliveryTimeWindows'][0]['start']
            return start_time
        except (json.J<PERSON>NDecodeError, KeyError, IndexError) as e:
            # Log error and return None if there is an error in parsing or the path is incorrect
            print(f"Error parsing JSON: {e}")
            return None
    else:
        # Return None if the assembly_json is NaN or not a string
        return None

# Apply the function to the 'assembly' column to create a new 'start_time' column
df['start_time'] = df['assembly'].apply(extract_start_time)

# Save the updated DataFrame to a new CSV file
df.to_csv('updated_csv.csv', index=False)

print("Extraction completed. The result is saved in 'updated_csv.csv'.")
