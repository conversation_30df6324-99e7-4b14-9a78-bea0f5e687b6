import csv
import json
import re

# Function to extract orderId from the _raw column (which contains JSON data)
def extract_orderId(raw_json):
    try:
        # 使用正则表达式查找orderId 
        match = re.search(r'\\"orderId\\" : \\"(\d+)\\"', raw_json)
        
        # 如果找到了匹配项，则打印orderId的值
        if match:
            order_id = match.group(1)
            return order_id
    except json.JSONDecodeError:
        return ''  # Return an empty string if JSON parsing fails

# Function to process the CSV file
def process_csv(input_file_path, output_file_path):
    with open(input_file_path, 'r', newline='', encoding='utf-8') as csvfile, \
         open(output_file_path, 'w', newline='', encoding='utf-8') as outputfile:
        # Set up CSV reader
        csv_reader = csv.DictReader(csvfile)
        # Set up CSV writer
        csv_writer = csv.writer(outputfile)
        # Write the header for the output CSV
        csv_writer.writerow(['orderId', 'log_trace_id'])

        # Iterate over the rows in the CSV file
        for row in csv_reader:
            # Extract orderId from the JSON data in the _raw column
            order_id = extract_orderId(row['_raw'])
            # Write the orderId and log_trace_id pair to the output CSV
            csv_writer.writerow([order_id, row['log_trace_id']])

# Define the input and output CSV file paths
input_csv_path = 'allorders.csv'  # Replace with your input CSV file path
output_csv_path = 'output.csv'  # Replace with your desired output CSV file path

# Call the function to process the CSV file
process_csv(input_csv_path, output_csv_path)
