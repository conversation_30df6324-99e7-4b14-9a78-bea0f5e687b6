#!/usr/bin/env python3

import pandas as pd
import json
import csv

# Load the mapping

def load_mapping(mapping_file):
    try:
        with open(mapping_file, 'r') as f:
            mapping= json.load(f)
        return {item['unitName']: item['unitCode'] for item in mapping if item['isValid']}
    except FileNotFoundError:
        print(f"Mapping file {mapping_file} not found.")
        return None
    except json.JSONDecodeError:
        print(f"Error decoding JSON from {mapping_file}.")
        return None

mapping_dict = load_mapping('mapping.json')

# Load the csv files
df_masterfile = pd.read_csv('masterfile-0306.csv')
df_config = pd.read_csv('isom-config-cdc-0306.csv')

# Rename columns for easier processing
df_masterfile.columns = [col.replace(' ', '_') for col in df_masterfile.columns]
df_config.columns = [col.replace(' ', '_') for col in df_config.columns]

# Try to convert the 'From_Zip' and 'To_Zip' columns to int and ignore errors
df_config['From_Zip'] = pd.to_numeric(df_config['From_Zip'], errors='coerce')
df_config['To_Zip'] = pd.to_numeric(df_config['To_Zip'], errors='coerce')

# Drop rows where 'From_Zip' or 'To_Zip' is NaN
df_config.dropna(subset=['From_Zip', 'To_Zip'], inplace=True)

# Convert 'From_Zip' and 'To_Zip' to int
df_config['From_Zip'] = df_config['From_Zip'].astype(int)
df_config['To_Zip'] = df_config['To_Zip'].astype(int)


# Define the function to check & compare


def check_and_compare(zipcode):
    # Filter rows for the given zipcode
    df_masterfile_filtered = df_masterfile[df_masterfile['ZipCode'] == zipcode]
    df_config_filtered = df_config[(df_config['From_Zip'] <= zipcode) & (df_config['To_Zip'] >= zipcode)]
    
    # Get FF Unit lists
    masterfile_units = []
    for i in range(1, 5):
        masterfile_units.extend(df_masterfile_filtered[f'Truck_FF_Unit_{i}'].dropna().unique().tolist())
    for i in range(1, 3):
        masterfile_units.extend(df_masterfile_filtered[f'Parcel_FF_Unit_{i}'].dropna().unique().tolist())
    masterfile_units = set(masterfile_units)  # remove duplicates
    
    # Convert units to codes in masterfile
    masterfile_codes = [mapping_dict.get(unit) for unit in masterfile_units]
    masterfile_codes = [code for code in masterfile_codes if code is not None and code.startswith('CDC')]  # Remove None (unmapped units) and codes starting with "CDC"

    # Get codes from config and trim spaces
    config_codes = df_config_filtered['Node_Key'].str.strip().unique().tolist()
    
    # Compare and write differences to CSV file
    a2bdiff = set(masterfile_codes) - set(config_codes)
    b2adiff = set(config_codes) - set(masterfile_codes) 
    if a2bdiff or b2adiff:
        with open('differences0306-cdc.csv', 'a', newline='') as f:
            writer = csv.writer(f)
            # Write header if file is empty
            if f.tell() == 0:
                writer.writerow(['ZipCode', 'MasterfileConfig', 'ISOMConfig', 'ISOM Missing', 'ISOM Extra'])
            writer.writerow([zipcode, masterfile_codes, config_codes, a2bdiff, b2adiff])
# Check each unique zipcode in masterfile
for zipcode in df_masterfile['ZipCode'].unique():
    check_and_compare(zipcode)