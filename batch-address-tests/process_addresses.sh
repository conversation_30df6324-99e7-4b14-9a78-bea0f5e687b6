# 加载.env文件中的环境变量
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# 检查必要的环境变量
if [ -z "$DASHSCOPE_API_KEY" ]; then
    echo "错误: 请设置DASHSCOPE_API_KEY环境变量"
    exit 1
fi

# 检查是否安装了parallel
if ! command -v parallel &> /dev/null; then
    echo "错误: 请先安装GNU parallel"
    echo "可以使用 'brew install parallel' 进行安装"
    exit 1
fi

# 设置API相关变量
# PROD
# API_URL="https://dashscope.aliyuncs.com/api/v1/apps/7f95528b8da647a5bdf808e06b9e69d9/completion"

# Dev
API_URL="https://dashscope.aliyuncs.com/api/v1/apps/79eaa15917d04ae6bc5f911be07cd35e/completion"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_FILE="results_${TIMESTAMP}.csv"
TEMP_DIR="temp_${TIMESTAMP}"

# 创建临时目录
mkdir -p "$TEMP_DIR"

# 创建CSV头部
echo "原始地址,API响应" > "$OUTPUT_FILE"

# 定义函数内容
read -r -d '' FUNCTIONS << 'EOF'
process_address() {
    local line="$1"
    local index="$2"
    local temp_file="${TEMP_DIR}/result_${index}.txt"

    # 跳过空行
    if [ -z "$line" ]; then
        return
    fi

    # 对输入内容进行JSON转义处理
    echo $line
    ESCAPED_LINE=$(echo "$line" | jq -R -s '.')
    
    # 准备API请求数据
    REQUEST_DATA='{"input":{"prompt":'"$ESCAPED_LINE"'},"parameters":{},"debug":{}}'

    # 调用API并保存响应
    RESPONSE=$(curl -s -X POST "$API_URL" \
        --header "Authorization: Bearer $DASHSCOPE_API_KEY" \
        --header 'Content-Type: application/json' \
        --data "$REQUEST_DATA")

    echo $RESPONSE
    # 从API响应中提取text字段
    TEXT_RESPONSE=$(echo "$RESPONSE" | jq -r '.output.text // "处理失败"')
    
    # 将结果写入临时文件，确保CSV格式正确
    line_escaped=$(echo "$line" | sed 's/"/""/g' | sed 's/[[:space:]]*$//')
    response_escaped=$(echo "$TEXT_RESPONSE" | sed 's/"/""/g' | sed 's/[[:space:]]*$//')
    echo "\"$line_escaped\",\"$response_escaped\"" > "$temp_file"
}
EOF

# 将函数定义写入临时文件
echo "$FUNCTIONS" > "${TEMP_DIR}/functions.sh"

# 导出变量，使parallel可以使用
export API_URL DASHSCOPE_API_KEY TEMP_DIR

# 使用parallel并行处理地址，每次运行8个并行任务，每个任务间隔0.1秒
parallel --env _ --will-cite -j 8 --delay 0.1 \
    'source "${TEMP_DIR}/functions.sh" && process_address {} {#}' :::: xac

# 按顺序合并结果
for f in "${TEMP_DIR}"/result_*.txt(.N); do
    if [ -f "$f" ]; then
        cat "$f" >> "$OUTPUT_FILE"
    fi
done

# 清理临时文件
rm -rf "$TEMP_DIR"

echo "处理完成！结果已保存到 $OUTPUT_FILE"